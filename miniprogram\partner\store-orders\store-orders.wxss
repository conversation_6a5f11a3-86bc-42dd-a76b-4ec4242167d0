/* partner/store-orders/store-orders.wxss */
/* 合伙人端门店订单页面样式 */

.store-orders-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 门店选择区域样式 */
.store-select-section {
  background-color: #ffffff;
  padding: 15px 15px 10px 15px;
  margin-bottom: 1px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.store-select-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.section-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.store-select-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.store-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 36px;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  width: 100%;
}

.store-name {
  font-size: 14px;
  color: #333;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-arrow {
  font-size: 12px;
  color: #999;
  margin-left: 5px;
}

/* 搜索栏 */
.search-section {
  padding: 15rpx 30rpx;
  background-color: #ffffff;
  margin-bottom: 1px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 12rpx 24rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
}

.search-placeholder {
  color: #999;
}

/* 排序筛选栏 */
.sort-filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  padding: 0 30rpx;
  height: 80rpx;
  margin-bottom: 1px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.sort-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  transition: all 0.3s ease;
}

.sort-item.active {
  color: #333;
  font-weight: 500;
  background-color: #f0f0f0;
}

.sort-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 6rpx;
}

.filter-btn {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}

.filter-icon {
  width: 28rpx;
  height: 28rpx;
  margin-left: 8rpx;
}

/* 订单分类标签栏 */
.order-type-tabs {
  display: flex;
  background: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  align-items: center;
  min-height: 70rpx;
}

.tabs-container {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
}

.order-type-tabs .tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  font-size: 32rpx;
  color: #666;
  padding: 10rpx 0;
}

.order-type-tabs .tab-item.active {
  color: #5698c3;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 4rpx;
  background-color: #5698c3;
  border-radius: 2rpx;
}

/* 二级分类标签栏 */
.sub-type-tabs {
  display: flex;
  background: #fff;
  padding: 15rpx 30rpx;
  margin-bottom: 10rpx;
  justify-content: space-between;
  align-items: center;
  overflow-x: auto;
  white-space: nowrap;
}

.sub-type-tabs .tab-item {
  font-size: 26rpx;
  color: #666;
  padding: 6rpx 12rpx;
  border: 1rpx solid #ddd;
  border-radius: 14rpx;
  background: #fff;
  transition: all 0.3s ease;
  white-space: nowrap;
  line-height: 1.3;
  margin: 0 6rpx;
  flex-shrink: 0;
  min-width: fit-content;
}

.sub-type-tabs .tab-item.active {
  color: #5698c3;
  font-weight: 500;
  border-color: #5698c3;
  background-color: rgba(86, 152, 195, 0.08);
}

/* 订单列表容器 */
.order-list-container {
  padding: 0 30rpx;
}

/* 订单列表 */
.order-list {
  padding-bottom: 30rpx;
}

/* 加载状态和空状态 */
.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1aad19;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text,
.empty-text {
  font-size: 28rpx;
  color: #999;
}

.empty-container {
  padding: 120rpx 0;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}

.order-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 第1行：订单类型 + 订单状态 */
.order-row-1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 第2行：订单号 + 订单时间 */
.order-row-2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

/* 第3行：订单门店 + 操作人信息 */
.order-row-3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-type-tag {
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 500;
  text-align: center;
}

.order-type-tag.purchase {
  background-color: #1aad19;
}

.order-type-tag.transfer {
  background-color: #ff9800;
}

.order-status {
  text-align: right;
}

.order-no {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.order-date {
  font-size: 26rpx;
  color: #666;
}

.order-store {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.order-operator {
  font-size: 24rpx;
  color: #666;
  text-align: right;
}

.status-text {
  font-size: 26rpx;
  color: #1aad19;
  font-weight: 500;
  display: block;
  margin-bottom: 6rpx;
}

.status-time, .logistics-status {
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
}

/* 订单状态颜色 */
/* 待审核状态 - 适用于采购订单和移库订单 */
.status-text.pending_review,
.status-text.yixiadan,
.status-text.daishenhe,
.status-text.weishenhe {
  color: #ff9800; /* 待审核 - 橙色 */
}

/* 采购订单特有状态 */
.status-text.reviewed,
.status-text.yishenhe {
  color: #1aad19; /* 已审核 - 绿色 */
}

.status-text.rejected,
.status-text.yijujue {
  color: #f44336; /* 已拒绝 - 红色 */
}

.status-text.cancelled,
.status-text.yiquxiao {
  color: #999; /* 已取消 - 灰色 */
}

/* 移库订单特有状态 */

.status-text.pending_shipment,
.status-text.daifahuo {
  color: #ff9800; /* 待发货 - 橙色 */
}

.status-text.shipped,
.status-text.yifahuo {
  color: #34c759; /* 绿色 - 已发货 */
}

.status-text.received,
.status-text.yidaodian {
  color: #30d158; /* 深绿色 - 已到店 */
}

/* 商品列表 */
.product-list {
  margin-bottom: 20rpx;
}

.product-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background-color: #f9f9f9;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-specs {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.product-details {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-top: 8rpx;
}

.product-price {
  font-size: 26rpx;
  color: #ff6b00;
  font-weight: 500;
}

.product-qty {
  font-size: 24rpx;
  color: #666;
}

.product-amount {
  font-size: 26rpx;
  color: #ff6b00;
  font-weight: 500;
  margin-left: auto;
}

.more-products {
  text-align: center;
  padding: 20rpx 0 0;
  font-size: 24rpx;
  color: #999;
}

/* 第5行：订单总金额 */
.order-total {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.total-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.total-amount {
  font-size: 32rpx;
  color: #ff6b00;
  font-weight: 600;
  margin-left: 10rpx;
}

/* 第6行：操作按钮 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 20rpx;
}

.action-btn {
  padding: 0 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  border: none;
  font-weight: 500;
}

.primary-btn {
  background-color: #1aad19;
  color: #ffffff;
}

.cancel-btn {
  background-color: #ff4757;
  color: #ffffff;
}

/* 加载更多和没有更多 */
.load-more,
.no-more {
  text-align: center;
  padding: 30rpx 0;
}

.no-more {
  color: #999;
  font-size: 26rpx;
}

/* 筛选抽屉 */
.filter-drawer {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  visibility: hidden;
  transition: visibility 0.3s;
}

.filter-drawer.show {
  visibility: visible;
}

.filter-drawer-mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
}

.filter-drawer.show .filter-drawer-mask {
  opacity: 1;
}

.filter-drawer-content {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 80%;
  background-color: #ffffff;
  transform: translateX(100%);
  transition: transform 0.3s;
  display: flex;
  flex-direction: column;
}

.filter-drawer.show .filter-drawer-content {
  transform: translateX(0);
}

.filter-drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.filter-drawer-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.filter-drawer-close {
  padding: 10rpx;
}

.close-icon {
  width: 32rpx;
  height: 32rpx;
}

.filter-drawer-body {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-section-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.date-range-inputs {
  display: flex;
  align-items: center;
}

.date-input {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.date-input text {
  color: #999;
}

.date-range-separator {
  margin: 0 20rpx;
  color: #999;
}

.product-keyword-input {
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
}

.product-keyword-input input {
  height: 80rpx;
  font-size: 28rpx;
}

.filter-drawer-footer {
  display: flex;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f5f5f5;
}

.reset-btn,
.apply-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.reset-btn {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 20rpx;
}

.apply-btn {
  background-color: #1aad19;
  color: #ffffff;
}