Page({
  data: {
    currentRoleLabel: '',
    switchRoles: [],
    currentRoleType: ''
  },
  onLoad(options) {
    this.resolveCurrentRole(options);
  },
  onShow() {
    this.resolveCurrentRole();
  },
  resolveCurrentRole(options = {}) {
    const app = getApp();
    let label = '';
    let currentRoleType = '';
    let allRoles = [];
    
    // 1. 优先用参数
    if (options.role_type) {
      currentRoleType = options.role_type;
    }
    
    // 2. 其次用全局currentRole
    if (!currentRoleType && app.globalData && app.globalData.currentRole) {
      currentRoleType = app.globalData.currentRole.role_type;
      allRoles = app.globalData.userRoles || [];
      // 切换登录身份页面使用role_type显示身份，不使用role_name
      const currentRole = allRoles.find(r => r.role_type === currentRoleType);
      label = currentRole ? this.getRoleTypeLabel(currentRole.role_type) : '';
    }
    
    // 3. 再用userInfo
    if (!currentRoleType && app.globalData && app.globalData.userInfo) {
      if (app.globalData.userInfo.role_type) {
        currentRoleType = app.globalData.userInfo.role_type;
        // 切换登录身份页面使用role_type显示身份，不使用role_name
        label = this.getRoleTypeLabel(currentRoleType);
      }
      allRoles = app.globalData.userInfo.roles || [];
    }
    
    // 4. 兜底：roles第一个为当前
    if (!currentRoleType && allRoles.length > 0) {
      currentRoleType = allRoles[0].role_type;
      // 切换登录身份页面使用role_type显示身份，不使用role_name
      label = this.getRoleTypeLabel(allRoles[0].role_type);
    }
    
    // 5. 如果仍然没有角色信息，尝试从API获取
    if (!currentRoleType || allRoles.length === 0) {
      console.log('未找到角色信息，尝试从API获取');
      this.fetchUserRoles();
      return;
    }
    
    // 排序：顾客、合伙人、管理员
    const order = ['customer', 'partner', 'admin'];
    const switchRoles = allRoles.filter(r => r.role_type !== currentRoleType)
      .sort((a, b) => order.indexOf(a.role_type) - order.indexOf(b.role_type))
      .map(r => ({ ...r, roleTypeName: this.getRoleTypeLabel(r.role_type) }));
      
    console.log('解析角色信息完成:', {
      currentRoleType,
      currentRoleLabel: label,
      switchRoles: switchRoles.length,
      allRoles: allRoles.length
    });
    
    this.setData({ currentRoleLabel: label, switchRoles, currentRoleType });
  },
  
  // 从API获取用户角色信息
  fetchUserRoles() {
    const { userApi } = require('../../utils/api');
    const app = getApp();
    
    // 检查登录状态
    if (!app.globalData.isLogin) {
      console.log('用户未登录，无法获取角色信息');
      this.setData({ 
        currentRoleLabel: '未登录',
        switchRoles: [],
        currentRoleType: ''
      });
      return;
    }
    
    wx.showLoading({ title: '获取身份信息...' });
    
    // 根据页面栈判断用户来源，设置优先角色
    const pages = getCurrentPages();
    let preferredRole = null;
    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2];
      const prevRoute = prevPage.route;
      
      if (prevRoute.includes('/partner/')) {
        preferredRole = 'partner';
      } else if (prevRoute.includes('/admin/')) {
        preferredRole = 'admin';
      } else {
        preferredRole = 'customer';
      }
    }
    
    let clientType = 'customer'; // 默认为顾客端
    if (preferredRole === 'admin') {
      clientType = 'admin';
    } else if (preferredRole === 'partner') {
      clientType = 'partner';
    }
    
    console.log('检测到用户来源，设置客户端类型:', clientType);
    
    const params = { clientType };
    userApi.getUserRoles(params)
      .then(res => {
        wx.hideLoading();
        console.log('获取用户角色信息成功:', res);
        
        // 修正：检查res.data.roles而不是res.data
        if (res.success && res.data && res.data.roles && res.data.roles.length > 0) {
          // 更新全局数据
          app.globalData.userRoles = res.data.roles;
          
          // 设置当前角色，优先使用后端返回的currentRole
          if (res.data.currentRole) {
            app.globalData.currentRole = res.data.currentRole;
          } else if (!app.globalData.currentRole) {
            // 如果没有当前角色，设置第一个为当前角色
            app.globalData.currentRole = res.data.roles[0];
          }
          
          // 重新解析角色信息
          this.resolveCurrentRole();
        } else {
          console.error('角色数据格式错误:', res);
          this.setData({ 
            currentRoleLabel: '无身份信息',
            switchRoles: [],
            currentRoleType: ''
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('获取用户角色信息失败:', err);
        
        // 检查是否是需要重新登录的错误
        if (err.code === 'PREFERRED_ROLE_REQUIRED' || (err.message && err.message.includes('请退出重新登录'))) {
          wx.showModal({
            title: '提示',
            content: '请退出重新登录',
            showCancel: false,
            success: () => {
              // 清除登录状态
              const app = getApp();
              app.globalData.isLogin = false;
              app.globalData.userInfo = null;
              app.globalData.userRoles = [];
              app.globalData.currentRole = null;
              wx.removeStorageSync('userInfo');
              wx.removeStorageSync('token');
              
              // 跳转到登录页
              wx.reLaunch({ url: '/pages/login/login' });
            }
          });
          return;
        }
        
        wx.showToast({ title: '获取身份信息失败', icon: 'none' });
        this.setData({ 
          currentRoleLabel: '获取失败',
          switchRoles: [],
          currentRoleType: ''
        });
      });
  },
  
  // 将role_type转换为中文显示标签
  getRoleTypeLabel(roleType) {
    const roleLabels = {
      'customer': '顾客',
      'partner': '合伙人',
      'admin': '管理员'
    };
    return roleLabels[roleType] || roleType;
  },
  
  onSwitchRole(e) {
    const role = e.currentTarget.dataset.role;
    const app = getApp();
    // 切换后强制更新currentRole
    app.globalData.currentRole = role;
    if (role.role_type === 'admin') {
      wx.reLaunch({ url: '/admin/console/console' });
    } else if (role.role_type === 'partner') {
      wx.reLaunch({ url: '/partner/partner/partner' });
    } else if (role.role_type === 'customer') {
      wx.reLaunch({ url: '/pages/profile/profile' });
    }
  },
  onLogout() {
    // 退出登录逻辑，清空登录状态后跳转到顾客端首页
    const loginStateManager = require('../../utils/login-state-manager');
    loginStateManager.logout().then(() => {
      // 无论从哪个端进入切换登录身份页，退出登录后都跳转到顾客端首页
      wx.reLaunch({ url: '/pages/home/<USER>' });
    });
  }
});