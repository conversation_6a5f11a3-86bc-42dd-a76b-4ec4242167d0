.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 30rpx;
  padding-bottom: 160rpx;
}

/* 通用section样式 */
.section {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  min-height: 120rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 配送方式section特殊样式 */
.section:first-child {
  padding: 20rpx 30rpx 10rpx 30rpx;
  margin-bottom: 1rpx;
  min-height: 80rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

/* 配送方式切换按钮 */
.delivery-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60rpx;
  gap: 0;
}

.toggle-item {
  width: 160rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  position: relative;
  transition: all 0.3s;
  letter-spacing: 4rpx; /* 增加文字横向间距 */
}

.toggle-item:first-child {
  border-radius: 30rpx 0 0 30rpx;
  border: 2rpx solid #e74c3c;
  border-right: 1rpx solid #e74c3c;
}

.toggle-item:last-child {
  border-radius: 0 30rpx 30rpx 0;
  border: 2rpx solid #e74c3c;
  border-left: 1rpx solid #e74c3c;
}

/* 选中状态：红色背景，白色文字，加粗 */
.toggle-item.active {
  background: #e74c3c;
  color: #fff;
  font-weight: bold; /* 选中状态文字加粗 */
}

/* 未选中状态：灰色背景，深灰色文字，保留红色边框 */
.toggle-item:not(.active) {
  background: #f0f0f0;
  color: #666;
}

/* 收货地址 */
.address-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 120rpx;
}

.address-label {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-right: 30rpx;
  line-height: 1.4;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 80rpx;
}

.address-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80rpx;
}

.no-address {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80rpx;
  color: #999;
  font-size: 28rpx;
}

.address-info {
  flex: 1;
}

.store-info {
  flex: 1;
}

.store-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.store-no {
  font-size: 26rpx;
  color: #e74c3c;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.store-address {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.store-hours {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.address {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

.no-address {
  flex: 1;
  font-size: 28rpx;
  color: #999;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80rpx;
}

.arrow {
  color: #999;
  font-size: 32rpx;
  margin-left: 20rpx;
}

/* 商品列表 */
.product-list {
  min-height: 120rpx;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 120rpx;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  min-height: 100rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.3;
}

.product-spec {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.product-price {
  font-size: 32rpx;
  color: #e74c3c;
  font-weight: 500;
}

.product-quantity {
  font-size: 28rpx;
  color: #666;
}

.empty-products {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120rpx;
  color: #999;
  font-size: 28rpx;
}

/* 支付方式 */
.payment-list {
  min-height: 160rpx;
}

.payment-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 80rpx;
}

.payment-item:last-child {
  border-bottom: none;
}

.payment-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.payment-name {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.payment-check {
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  transition: all 0.3s;
}

.checkbox.checked {
  border-color: #e74c3c;
  background: #e74c3c;
}

.checkmark {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.payment-switch {
  display: flex;
  align-items: center;
}

/* 订单金额 */
.amount-info {
  min-height: 120rpx;
}

.amount-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  font-size: 28rpx;
  color: #666;
  min-height: 40rpx;
  align-items: center;
}

.amount-item.total {
  border-top: 1rpx solid #f0f0f0;
  margin-top: 15rpx;
  padding-top: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.total-amount {
  color: #e74c3c;
  font-weight: bold;
}

/* 提交按钮 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 40rpx 40rpx 40rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.amount-display {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
}

.amount-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.submit-btn {
  background: #e74c3c;
  color: #fff;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  padding: 0 60rpx;
}

/* 提交按钮悬浮效果 */
.submit-btn:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, #d73527 0%, #c62d1f 100%);
}

/* 跨城市提示条幅 */
.city-warning {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 2rpx solid #ffc107;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-top: 20rpx;
  animation: warning-pulse 2s infinite;
}

.warning-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  animation: warning-shake 1s infinite;
}

.warning-text {
  font-size: 28rpx;
  color: #856404;
  font-weight: 500;
  flex: 1;
  line-height: 1.4;
}

/* 警告动画效果 */
@keyframes warning-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10rpx rgba(255, 193, 7, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
  }
}

@keyframes warning-shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2rpx);
  }
  75% {
    transform: translateX(2rpx);
  }
}