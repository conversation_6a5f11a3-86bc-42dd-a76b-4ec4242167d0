<!-- 搜索框 -->
<view class="search-container">
  <view class="search-box">
    <icon class="search-icon" type="search" size="16"></icon>
    <input class="search-input"
           placeholder="搜索订单号或商品名"
           value="{{searchKeyword}}"
           bindinput="onSearchInput"
           confirm-type="search" />
    <view class="clear-btn" wx:if="{{searchKeyword}}" bindtap="clearSearch">
      <icon type="clear" size="14"></icon>
    </view>
  </view>
</view>

<!-- 顶部标签栏 -->
<view class="tabs-wrapper">
  <view class="left-indicator" wx:if="{{showLeftIndicator}}">‹</view>
  <scroll-view scroll-x class="tabs-container" bindscroll="onTabsScroll">
  <view class="tabs">
    <block wx:for="{{tabs}}" wx:key="key">
      <view class="tab-item {{currentTab === item.key ? 'active' : ''}}"
            bindtap="switchTab"
            data-tab="{{item.key}}">
        <text>{{item.name}}</text>
        <view class="tab-line" wx:if="{{currentTab === item.key}}"></view>
        <!-- 当为待自提标签且处于激活时，显示"自提说明"入口 -->
        <view wx:if="{{currentTab === 'pending_pickup' && item.key === 'pending_pickup'}}" class="tab-help" bindtap="showPickupGuide" catchtap="stopTap">
          自提说明
        </view>
      </view>
    </block>
  </view>
</scroll-view>
<view class="right-indicator" wx:if="{{showRightIndicator}}">›</view>
</view>

<!-- 内容容器 -->
<view class="container">
  <!-- 订单列表 -->
  <view class="order-list" wx:if="{{!loading && orderList.length > 0}}">
    <block wx:for="{{orderList}}" wx:key="id">
      <view class="order-item" bindtap="viewOrderDetail" data-id="{{item.id}}">
        <!-- 订单头部 -->
        <view class="order-header">
          <view class="order-no">订单号: {{item.order_no}}</view>
          <view class="order-status" data-status="{{item.status}}">{{item.status_text}}</view>
        </view>
        
        <!-- 订单商品列表 -->
        <view class="order-products">
          <block wx:for="{{item.items}}" wx:for-item="product" wx:key="id">
            <view class="product-item">
              <image class="product-image" src="{{product.image || '/images/icons2/默认商品.png'}}"></image>
              <view class="product-info">
                <view class="product-name">{{product.name}}</view>
                <view class="product-specs" wx:if="{{product.specs}}">{{product.specs}}</view>
                <view class="product-bottom">
                  <view class="product-price-info">
                    <text class="product-unit-price">单价: ¥{{product.price}}</text>
                    <text class="product-qty">数量: {{product.quantity}}</text>
                  </view>
                  <view class="product-total-price">¥{{product.formattedTotalPrice}}</view>
                </view>
              </view>
            </view>
          </block>
        </view>
        
        <!-- 订单金额 -->
        <view class="order-total">
          <text class="total-quantity">共{{item.total_quantity}}件商品</text>
          <view class="total-amount">
            <text class="total-label">合计:</text>
            <text class="total-price">¥{{item.total_amount}}</text>
          </view>
        </view>
        
        <!-- 订单操作按钮 -->
        <view class="order-actions" catchtap="stopPropagation">
          <!-- 待付款状态 -->
          <block wx:if="{{item.status === '待支付' || item.status === '待付款'}}">
            <view class="action-btn cancel" catchtap="cancelOrder" data-id="{{item.id}}">取消订单</view>
            <view class="action-btn primary" catchtap="goToPay" data-id="{{item.id}}">付款</view>
          </block>

          <!-- 待发货状态 -->
          <block wx:elif="{{item.status === '待发货'}}">
            <view class="action-btn" catchtap="applyRefund" data-id="{{item.id}}">退款</view>
          </block>

          <!-- 待自提状态 -->
          <block wx:elif="{{item.status === '待自提'}}">
            <view class="action-btn" catchtap="showPickupGuide" data-id="{{item.id}}">自提指南</view>
            <view class="action-btn" catchtap="applyRefund" data-id="{{item.id}}">退货</view>
          </block>

          <!-- 待收货状态 -->
          <block wx:elif="{{item.status_text === '待收货'}}">
            <view class="action-btn" catchtap="viewLogistics" data-id="{{item.id}}">查看物流</view>
            <view class="action-btn" catchtap="applyRefund" data-id="{{item.id}}">退货</view>
            <view class="action-btn" catchtap="buyAgain" data-id="{{item.id}}">再次购买</view>
            <view class="action-btn primary" catchtap="confirmReceipt" data-id="{{item.id}}">确认收货</view>
          </block>

          <!-- 已完成状态 -->
          <block wx:elif="{{item.status === '已完成'}}">
            <view class="action-btn" catchtap="viewLogistics" data-id="{{item.id}}">查看物流</view>
            <view wx:if="{{!item.is_rated}}" class="action-btn" catchtap="goToRate" data-id="{{item.id}}">去评价</view>
            <view class="action-btn primary" catchtap="buyAgain" data-id="{{item.id}}">再次购买</view>
          </block>

          <!-- 已取消状态 -->
          <block wx:elif="{{item.status === '已取消'}}">
            <view class="action-btn" catchtap="deleteOrder" data-id="{{item.id}}">删除订单</view>
            <view class="action-btn primary" catchtap="buyAgain" data-id="{{item.id}}">再次购买</view>
          </block>

          <!-- 退款/售后状态 -->
          <block wx:elif="{{item.status === '退款'}}">
            <view class="action-btn" catchtap="viewRefundDetail" data-id="{{item.id}}">查看进度</view>
          </block>

          <!-- 默认状态（兜底） -->
          <block wx:else>
            <view class="action-btn" catchtap="buyAgain" data-id="{{item.id}}">再次购买</view>
          </block>
        </view>
      </view>
    </block>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!loading && isEmpty}}">
    <image class="empty-icon" src="/images/icons/empty-order.svg"></image>
    <text class="empty-text">暂无相关订单</text>
  </view>
  
  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{!loading && !isEmpty && !hasMore}}">
    <text>没有更多订单了</text>
  </view>
</view>