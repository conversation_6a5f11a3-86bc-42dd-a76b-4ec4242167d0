const db = require('../config/db');

// 分类缩写映射
const TYPE_MAP = {
  ZZ: '合伙人注资',
  HK: '支付货款',
  GJ: '提取公积金',
  FH: '分红',
  TZ: '特别调整',
  QT: '其他'
};

class FundRecord {
  // 新增资金变动记录
  static async create({ store_no, type, amount, description, voucher_images }) {
    const now = new Date();
    
    // 根据type字段确定account_type
    let account_type = 'fund'; // 默认值
    if (['capital_increase', 'capital_decrease', 'capital_transfer'].includes(type)) {
      account_type = 'capital';
    } else if (['dividend_payment', 'dividend_distribution'].includes(type)) {
      account_type = 'dividend';
    } else if (['fund_deposit', 'fund_withdrawal', 'purchase_payment'].includes(type)) {
      account_type = 'fund';
    }
    
    // 先插入记录，获取自增ID
    const sql = `INSERT INTO store_fund_records (store_no, type, account_type, amount, description, created_at, voucher_images) VALUES (?, ?, ?, ?, ?, ?, ?)`;
    const params = [store_no, type, account_type, amount, description, now, JSON.stringify(voucher_images || [])];
    const result = await db.query(sql, params);
    const id = result.insertId;
    // 生成凭证号：分类缩写+时间戳+自增ID
    const typeShort = TYPE_MAP[type] ? type : 'QT';
    const typePrefix = typeShort;
    const timeStr = now.getFullYear().toString() +
      String(now.getMonth() + 1).padStart(2, '0') +
      String(now.getDate()).padStart(2, '0') +
      String(now.getHours()).padStart(2, '0') +
      String(now.getMinutes()).padStart(2, '0') +
      String(now.getSeconds()).padStart(2, '0');
    const voucherNo = `${typePrefix}${timeStr}-${String(id).padStart(4, '0')}`;
    // 更新voucher_no字段
    await db.query('UPDATE store_fund_records SET voucher_no = ? WHERE id = ?', [voucherNo, id]);
    return { success: true, id, voucher_no: voucherNo };
  }

  // 查询门店资金变动记录
  static async getByStoreNo(store_no, limit = 50, offset = 0) {
    const sql = `SELECT * FROM store_fund_records WHERE store_no = ? ORDER BY created_at DESC LIMIT ? OFFSET ?`;
    const records = await db.query(sql, [store_no, limit, offset]);
    // voucher_images转为数组
    return records.map(r => ({ ...r, voucher_images: r.voucher_images ? JSON.parse(r.voucher_images) : [] }));
  }
}

module.exports = FundRecord;