Component({
  properties: {
    selected: {
      type: Number,
      value: 3 // 默认选中第4个标签
    }
  },
  data: {
    list: [
      {
        pagePath: '/partner/select-products/select-products',
        text: '选品',
        iconPath: '/images/tabbar/partner_select_normal.svg',
        selectedIconPath: '/images/tabbar/partner_select_active.svg'
      },
      {
        pagePath: '/partner/publish/publish',
        text: '上架',
        iconPath: '/images/tabbar/partner_publish_normal.svg',
        selectedIconPath: '/images/tabbar/partner_publish_active.svg'
      },
      {
        pagePath: '/partner/messages/message',
        text: '消息',
        iconPath: '/images/tabbar/partner_messages_normal.svg',
        selectedIconPath: '/images/tabbar/partner_messages_active.svg'
      },
      {
        pagePath: '/partner/partner/partner',
        text: '我的',
        iconPath: '/images/tabbar/partner_partner_normal.svg',
        selectedIconPath: '/images/tabbar/partner_partner_active.svg'
      }
    ]
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      wx.reLaunch({ url })
      this.triggerEvent('tabchange', { index: data.index })
    }
  }
})