// partner/publish/checkout/checkout.js
const { partnerApi, orderApi, storeFundApi } = require('../../../utils/api');

Page({
  data: {
    // 门店信息
    selectedStore: null,
    
    // 选中的商品列表
    selectedItems: [],
    
    // 支付方式
    paymentMethods: {
      equityPayment: {
        name: '门店股本金支付',
        selected: true,
        available: true,
        amount: 0 // 可用金额
      },
      reservePayment: {
        name: '门店公积金支付',
        selected: false,
        available: true,
        amount: 0 // 可用金额
      },
      redPacket: {
        name: '红包',
        selected: false,
        available: false,
        amount: 0
      },
      coupon: {
        name: '优惠券',
        selected: false,
        available: false,
        amount: 0
      }
    },
    
    // 订单信息
    orderInfo: {
      totalAmount: 0,
      totalQuantity: 0,
      items: []
    },
    
    // 加载状态
    loading: true
  },

  onLoad: function (options) {
    console.log('采购结算页面加载');
    this.initCheckoutData();
  },

  // 初始化结算数据
  initCheckoutData: function() {
    // 获取门店信息
    const selectedStore = wx.getStorageSync('partnerSelectedStore');
    
    // 获取选中的商品
    const purchaseCart = wx.getStorageSync('purchaseCart') || [];
    const selectedItems = purchaseCart.filter(item => item.selected);
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '没有选中的商品',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    
    // 计算订单信息
    const totalAmount = selectedItems.reduce((sum, item) => {
      return sum + parseFloat(item.subtotal);
    }, 0);
    
    const totalQuantity = selectedItems.reduce((sum, item) => {
      return sum + item.quantity;
    }, 0);
    
    this.setData({
      selectedStore: selectedStore,
      selectedItems: selectedItems,
      orderInfo: {
        totalAmount: totalAmount.toFixed(2),
        totalQuantity: totalQuantity,
        items: selectedItems
      },
      loading: false
    });
    
    // 获取门店资金信息
    this.getStoreFundInfo();
  },

  // 获取门店资金信息
  getStoreFundInfo: function() {
    if (!this.data.selectedStore) return;
    
    console.log('获取门店资金信息:', this.data.selectedStore.store_no);
    
    storeFundApi.getStoreFunds(this.data.selectedStore.store_no)
      .then(res => {
        if (res.success && res.data) {
          const fundInfo = res.data.funds;
          console.log('门店资金信息:', fundInfo);
          
          const paymentMethods = this.data.paymentMethods;
          paymentMethods.equityPayment.amount = parseFloat(fundInfo.capital || 0);
          paymentMethods.reservePayment.amount = parseFloat(fundInfo.reserve_fund || 0);
          
          // 检查股本金是否足够支付
          const totalAmount = parseFloat(this.data.orderInfo.totalAmount);
          if (fundInfo.capital < totalAmount) {
            paymentMethods.equityPayment.available = false;
            // 如果股本金不足，自动启用公积金支付
            paymentMethods.reservePayment.selected = true;
          }
          
          this.setData({
            paymentMethods: paymentMethods
          });
        } else {
          console.error('获取门店资金信息失败:', res.message);
          wx.showToast({
            title: '获取资金信息失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取门店资金信息错误:', err);
        wx.showToast({
          title: '获取资金信息失败',
          icon: 'none'
        });
      });
  },

  // 切换支付方式
  togglePaymentMethod: function(e) {
    const methodKey = e.currentTarget.dataset.method;
    const paymentMethods = this.data.paymentMethods;
    
    if (methodKey === 'equityPayment') {
      // 股本金支付是默认的，不能取消
      return;
    }
    
    if (methodKey === 'reservePayment') {
      // 公积金支付可以切换
      paymentMethods.reservePayment.selected = !paymentMethods.reservePayment.selected;
    } else {
      // 其他支付方式暂时不可用
      wx.showToast({
        title: '该功能暂未开放',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      paymentMethods: paymentMethods
    });
  },

  // 提交订单
  submitOrder: function() {
    const selectedItems = this.data.selectedItems;
    const paymentMethods = this.data.paymentMethods;
    const selectedStore = this.data.selectedStore;
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '没有选中的商品',
        icon: 'none'
      });
      return;
    }
    
    // 检查支付方式
    if (!paymentMethods.equityPayment.selected && !paymentMethods.reservePayment.selected) {
      wx.showToast({
        title: '请选择支付方式',
        icon: 'none'
      });
      return;
    }
    
    // 检查资金是否足够
    const totalAmount = parseFloat(this.data.orderInfo.totalAmount);
    const equityAmount = paymentMethods.equityPayment.amount;
    const reserveAmount = paymentMethods.reservePayment.amount;
    
    let availableAmount = 0;
    if (paymentMethods.equityPayment.selected) {
      availableAmount += equityAmount;
    }
    if (paymentMethods.reservePayment.selected) {
      availableAmount += reserveAmount;
    }
    
    if (availableAmount < totalAmount) {
      wx.showModal({
        title: '资金不足',
        content: `订单金额：¥${totalAmount}，可用资金：¥${availableAmount.toFixed(2)}，请选择其他支付方式或减少商品数量。`,
        showCancel: false
      });
      return;
    }
    
    // 构建订单数据
    const orderData = {
      storeNo: selectedStore.store_no,
      storeName: selectedStore.name,
      storeNo: selectedStore.store_no,
      items: selectedItems.map(item => ({
        productId: item.productId,
        name: item.name,
        quantity: item.quantity,
        purchasePrice: item.purchasePrice,
        subtotal: item.subtotal
      })),
      totalAmount: this.data.orderInfo.totalAmount,
      totalQuantity: this.data.orderInfo.totalQuantity,
      paymentMethods: {
        equityPayment: paymentMethods.equityPayment.selected,
        reservePayment: paymentMethods.reservePayment.selected
      },
      status: 'pending', // 待审核
      createTime: new Date().toISOString()
    };
    
    console.log('提交订单数据:', orderData);
    
    // 显示确认对话框
    wx.showModal({
      title: '确认提交订单',
      content: `订单金额：¥${this.data.orderInfo.totalAmount}\n商品数量：${this.data.orderInfo.totalQuantity}件\n\n确认提交采购订单吗？`,
      success: (res) => {
        if (res.confirm) {
          this.createOrder(orderData);
        }
      }
    });
  },

  // 创建订单
  createOrder: function(orderData) {
    wx.showLoading({
      title: '提交订单中...'
    });
    
    // 获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.id) {
      wx.hideLoading();
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 为每个商品创建采购订单
    const orderPromises = orderData.items.map(item => {
      // 根据选择的支付方式确定payment_method
      let paymentMethod = 'capital'; // 默认股本金支付
      if (orderData.paymentMethods.equityPayment.selected && orderData.paymentMethods.reservePayment.selected) {
        paymentMethod = 'mixed'; // 混合支付
      } else if (orderData.paymentMethods.reservePayment.selected && !orderData.paymentMethods.equityPayment.selected) {
        paymentMethod = 'reserve'; // 仅公积金支付
      }
      
      const orderRequest = {
        user_id: userInfo.id,
        product_id: item.productId,
        quantity: item.quantity,
        store_no: orderData.storeNo,
        payment_method: paymentMethod
      };
      
      console.log('创建采购订单请求:', orderRequest);
      return orderApi.createPurchaseOrder(orderRequest);
    });
    
    // 批量创建订单
    Promise.all(orderPromises)
      .then(results => {
        console.log('订单创建结果:', results);
        
        // 检查是否所有订单都创建成功
        const successCount = results.filter(res => res.success).length;
        const totalCount = results.length;
        
        if (successCount === totalCount) {
          // 计算总金额，用于资金记录
          const totalAmount = parseFloat(orderData.totalAmount);
          
          // 如果有订单金额，记录股本金支付
          if (totalAmount > 0) {
            return this.recordCapitalPayment(orderData);
          } else {
            return Promise.resolve();
          }
        } else {
          throw new Error(`部分订单创建失败: ${successCount}/${totalCount}`);
        }
      })
      .then(() => {
        wx.hideLoading();
        
        wx.showModal({
          title: '订单提交成功',
          content: `成功创建 ${orderData.items.length} 个采购订单\n\n订单已提交，等待平台管理员审核。`,
          showCancel: false,
          success: () => {
            // 清空已采购的商品
            this.clearPurchasedItems();
            
            // 返回上架页面
            wx.navigateBack();
          }
        });
      })
      .catch(error => {
        wx.hideLoading();
        console.error('创建订单失败:', error);
        
        wx.showModal({
          title: '订单创建失败',
          content: error.message || '网络连接异常，请检查网络后重试。',
          showCancel: false
        });
      });
  },

  // 记录股本金支付
  recordCapitalPayment: function(orderData) {
    const totalAmount = parseFloat(orderData.totalAmount);
    const paymentMethods = this.data.paymentMethods;
    
    // 计算股本金支付金额
    let equityPaymentAmount = 0;
    if (paymentMethods.equityPayment.selected) {
      equityPaymentAmount = Math.min(totalAmount, paymentMethods.equityPayment.amount);
    }
    
    if (equityPaymentAmount > 0) {
      const fundRecordData = {
        store_no: orderData.storeNo,
        type: 'purchase_payment',
        amount: -equityPaymentAmount, // 负数表示扣减
        description: `采购订单支付 - 订单金额: ¥${totalAmount.toFixed(2)}`,
        voucher_images: null
      };
      
      console.log('记录股本金支付:', fundRecordData);
      
      return storeFundApi.createFundRecord(fundRecordData)
        .then(res => {
          if (res.success) {
            console.log('股本金支付记录成功:', res.data);
            console.log('凭证号:', res.data.voucher_no);
          } else {
            console.error('股本金支付记录失败:', res.message);
          }
        })
        .catch(err => {
          console.error('股本金支付记录错误:', err);
          // 不阻止订单流程，只记录错误
        });
    }
    
    return Promise.resolve();
  },

  // 清空已采购的商品
  clearPurchasedItems: function() {
    const purchaseCart = wx.getStorageSync('purchaseCart') || [];
    const selectedItemIds = this.data.selectedItems.map(item => item.id);
    
    // 移除已采购的商品
    const remainingItems = purchaseCart.filter(item => !selectedItemIds.includes(item.id));
    
    wx.setStorageSync('purchaseCart', remainingItems);
    console.log('已清空采购车中的已采购商品');
  },

  // 图片加载错误处理
  onImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    const selectedItems = this.data.selectedItems;
    
    if (selectedItems[index]) {
      // 设置默认图片
      selectedItems[index].image = '/images/mo/mogoods.jpg';
      
      this.setData({
        selectedItems: selectedItems
      });
      
      console.log('商品图片加载失败，使用默认图片:', index);
    }
  },

  // 返回上一页
  onBack: function() {
    wx.navigateBack();
  }
});