// partner/store-funds/store-funds.js
// 合伙人端门店资金页面

const { storeFundApi, partnerApi, apiRequest } = require('../../utils/api');
const { formatDate } = require('../../utils/util');

Page({
  data: {
    // 门店相关数据
    storeNo: '',
    storeName: '',
    storeList: [],
    selectedStore: null,
    
    // 资金统计数据
    fundStats: {
      capital: '0.00',
      dividend: '0.00',
      fund: '0.00'
    },
    
    // 资金记录相关
    fundRecords: [],
    currentTab: 'all', // 当前选中的标签：all, capital, dividend, fund
    
    // 分页相关
    page: 1,
    pageSize: 20,
    hasMore: true,
    loading: false
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    console.log('门店资金页面加载', options);
    
    // 如果从合伙人页面跳转过来，可能会带有账户类型参数
    if (options.type) {
      this.setData({
        currentTab: options.type
      });
    }
    
    this.checkLoginStatus();
  },

  /**
   * 页面显示
   */
  onShow() {
    // 每次显示页面时刷新数据
    // 如果已经有选中的门店，直接刷新数据
    if (this.data.selectedStore) {
      this.loadFundData();
    } else {
       // 如果没有选中的门店，重新检查登录状态和加载门店数据
       this.checkLoginStatus();
     }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshData();
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreRecords();
    }
  },

  /**
   * 检查登录状态
   */
  async checkLoginStatus() {
    try {
      // 使用登录状态管理器检查登录状态
      const loginStateManager = require('../../utils/login-state-manager');
      const loginState = loginStateManager.getLoginState();
      
      // 检查登录状态是否有效
      if (!loginState || !loginState.token || !loginState.isLogin) {
        console.log('登录状态无效，需要重新登录');
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      // 验证登录状态是否过期
      const validationResult = await loginStateManager.validateLoginState();
      if (!validationResult.isValid) {
        console.log('登录状态验证失败:', validationResult.message);
        wx.showToast({
          title: validationResult.message || '登录已过期，请重新登录',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      console.log('登录状态验证通过，开始加载门店数据');
      await this.loadStoreData();
    } catch (error) {
      console.error('检查登录状态失败:', error);
      wx.showToast({
        title: '登录状态异常',
        icon: 'none'
      });
    }
  },

  /**
   * 加载门店数据
   */
  async loadStoreData() {
    try {
      wx.showLoading({ title: '加载门店数据...' });
      
      // 先从本地存储获取门店数据
      const storedStoreList = wx.getStorageSync('partnerStoreList');
      const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
      
      if (storedStoreList && storedStoreList.length > 0) {
        console.log('使用本地存储的门店数据');
        this.setData({
          storeList: storedStoreList,
          selectedStore: storedSelectedStore || storedStoreList[0],
          storeNo: (storedSelectedStore || storedStoreList[0]).store_no,
          storeName: (storedSelectedStore || storedStoreList[0]).name
        });
        
        // 加载资金数据
        await this.loadFundData();
        wx.hideLoading(); // 确保隐藏加载状态
        return;
      }
      
      // 如果本地没有数据，则从API获取
      console.log('从API获取门店数据');
      const [partnerStores, joinedStores] = await Promise.all([
        partnerApi.getPartnerStores(),
        partnerApi.getPartnerJoinedStores()
      ]);
      
      let allStores = [];
      let hasApiError = false;
      
      if (partnerStores && partnerStores.success && partnerStores.data) {
        allStores = [...partnerStores.data];
      } else {
        console.error('获取合伙人门店列表失败:', partnerStores);
        hasApiError = true;
      }
      
      if (joinedStores && joinedStores.success && joinedStores.data) {
        joinedStores.data.forEach(store => {
          const existingIndex = allStores.findIndex(s => s.store_no === store.store_no);
          if (existingIndex === -1) {
            allStores.push(store);
          }
        });
      } else {
        console.error('获取合伙人加入的门店列表失败:', joinedStores);
        hasApiError = true;
      }
      
      if (allStores.length > 0) {
        // 尝试从本地存储获取上次选择的门店
        const lastSelectedStore = wx.getStorageSync('selectedStore');
        let selectedStore = null;
        
        if (lastSelectedStore && allStores.find(store => store.store_no === lastSelectedStore.store_no)) {
          selectedStore = lastSelectedStore;
        } else {
          selectedStore = allStores[0];
        }
        
        this.setData({
          storeList: allStores,
          selectedStore,
          storeNo: selectedStore.store_no,
          storeName: selectedStore.name
        });
        
        // 保存到本地存储
        wx.setStorageSync('partnerStoreList', allStores);
        wx.setStorageSync('partnerSelectedStore', selectedStore);
        wx.setStorageSync('selectedStore', selectedStore);
        
        // 加载资金数据
        await this.loadFundData();
      } else {
        // 没有门店数据的处理
        if (hasApiError) {
          // API调用失败
          wx.showModal({
            title: '门店数据获取失败',
            content: '无法获取门店信息，请检查网络连接。是否重新获取？',
            confirmText: '重新获取',
            cancelText: '返回上页',
            success: (res) => {
              if (res.confirm) {
                this.loadStoreData();
              } else {
                wx.navigateBack();
              }
            }
          });
        } else {
          // API成功但没有门店数据
          wx.showModal({
            title: '暂无门店数据',
            content: '您还没有关联的门店，请联系管理员或返回上一页。',
            confirmText: '返回上页',
            showCancel: false,
            success: () => {
              wx.navigateBack();
            }
          });
        }
      }
    } catch (error) {
      console.error('加载门店数据失败:', error);
      
      // 根据错误类型显示不同的提示
      if (error.code === -1 || (error.message && error.message.includes('网络'))) {
        wx.showModal({
          title: '网络连接失败',
          content: '请检查网络连接后重试',
          confirmText: '重新获取',
          cancelText: '返回上页',
          success: (res) => {
            if (res.confirm) {
              this.loadStoreData();
            } else {
              wx.navigateBack();
            }
          }
        });
      } else if (error.code === 401) {
        // 登录失效，不需要额外处理，request.js已经处理了
        console.log('登录失效，已由request.js处理');
      } else {
        wx.showToast({
          title: error.message || '加载门店数据失败',
          icon: 'none',
          duration: 3000
        });
      }
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 门店选择变化
   */
  async onStoreChange(e) {
    const index = e.detail.value;
    const selectedStore = this.data.storeList[index];
    
    this.setData({
      selectedStore,
      storeNo: selectedStore.store_no,
      storeName: selectedStore.name
    });
    
    // 保存选中的门店到本地存储
    wx.setStorageSync('selectedStore', selectedStore);
    
    // 重新加载资金数据
    await this.loadFundData();
  },

  /**
   * 加载资金数据
   */
  async loadFundData() {
    if (!this.data.storeNo) {
      return;
    }
    
    try {
      // 同时加载资金统计和记录
      await Promise.all([
        this.loadFundStats(),
        this.loadFundRecords(true)
      ]);
    } catch (error) {
      console.error('加载资金数据失败:', error);
    }
  },

  /**
   * 加载资金统计数据
   */
  async loadFundStats() {
    if (!this.data.selectedStore || !this.data.selectedStore.store_no) {
      return;
    }
    
    try {
      const response = await storeFundApi.getStoreFunds(this.data.selectedStore.store_no);
      
      // 确保即使API返回空数据也能正常显示默认值
      let fundStats = {
        capital: '0.00',
        dividend: '0.00',
        fund: '0.00'
      };
      
      if (response && response.success && response.data && response.data.funds) {
        const funds = response.data.funds;
        
        fundStats = {
          capital: (parseFloat(funds.capital) || 0).toFixed(2),
          dividend: (parseFloat(funds.dividend) || 0).toFixed(2),
          fund: (parseFloat(funds.fund) || 0).toFixed(2)
        };
      }
      
      this.setData({ fundStats });
      
    } catch (error) {
      console.error('加载资金统计失败:', error);
      
      // 即使出错也要设置默认值，避免页面显示异常
      const defaultFundStats = {
        capital: '0.00',
        dividend: '0.00',
        fund: '0.00'
      };
      
      this.setData({ fundStats: defaultFundStats });
      
      // 只有在非网络错误时才显示错误提示
      if (error.code !== -1 && !error.message?.includes('网络')) {
        wx.showToast({
          title: '加载资金统计失败',
          icon: 'none',
          duration: 2000
        });
      }
    }
  },

  /**
   * 加载资金记录
   */
  async loadFundRecords(reset = false) {
    if (this.data.loading) {
      return;
    }
    
    if (!this.data.selectedStore || !this.data.selectedStore.store_no) {
      return;
    }
    
    try {
      this.setData({ loading: true });
      
      const page = reset ? 1 : this.data.page;
      const limit = this.data.pageSize;
      const offset = (page - 1) * limit;
      
      // 根据当前标签确定账户类型参数
      const accountType = this.data.currentTab === 'all' ? null : this.data.currentTab;
      
      // 使用storeFundApi.getFundRecords方法，传递账户类型参数
      const response = await storeFundApi.getFundRecords(
        this.data.selectedStore.store_no,
        limit,
        offset,
        accountType
      );
      
      // 确保即使API返回空数据也能正常处理
      let records = [];
      let hasMore = false;
      
      if (response && response.success && response.data) {
        records = response.data.records || [];
        // 判断是否还有更多数据
        hasMore = records.length >= this.data.pageSize;
      }
      
      // 格式化记录数据
      const formattedRecords = records.map(record => ({
        ...record,
        created_at_formatted: formatDate(record.created_at, 'MM-dd HH:mm'),
        amount: parseFloat(record.amount || 0).toFixed(2)
      }));
      
      if (reset) {
        this.setData({
          fundRecords: formattedRecords,
          page: 2, // 下次加载第2页
          hasMore: hasMore
        });
      } else {
        this.setData({
          fundRecords: [...this.data.fundRecords, ...formattedRecords],
          page: page + 1,
          hasMore: hasMore
        });
      }
      
    } catch (error) {
      console.error('加载资金记录失败:', error);
      
      // 只有在非网络错误时才显示错误提示
      if (error.code !== -1 && !error.message?.includes('网络')) {
        wx.showToast({
          title: '加载资金记录失败',
          icon: 'none',
          duration: 2000
        });
      }
    } finally {
      this.setData({ loading: false });
      if (wx.stopPullDownRefresh) {
        wx.stopPullDownRefresh();
      }
    }
  },

  /**
   * 加载更多记录
   */
  async loadMoreRecords() {
    await this.loadFundRecords(false);
  },

  /**
   * 标签切换
   */
  async onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    
    if (tab === this.data.currentTab) {
      return;
    }
    
    this.setData({
      currentTab: tab,
      fundRecords: [],
      page: 1,
      hasMore: true
    });
    
    // 重新加载记录
    await this.loadFundRecords(true);
  },

  /**
   * 点击账户类型统计项
   */
  async onAccountTypeChange(e) {
    const type = e.currentTarget.dataset.type;
    
    // 切换到对应的标签
    this.setData({
      currentTab: type,
      fundRecords: [],
      page: 1,
      hasMore: true
    });
    
    // 重新加载记录
    await this.loadFundRecords(true);
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({
      fundRecords: [],
      page: 1,
      hasMore: true
    });
    
    await this.loadFundData();
  }
});