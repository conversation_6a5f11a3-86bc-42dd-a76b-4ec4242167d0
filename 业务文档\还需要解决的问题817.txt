顾客端
》》1、在【我的订单】页给“未付款”的订单付款后，应该返回【我的订单】页————————

》》2、【新品上市】页的功能与UI————————


3、如果“订阅门店”有专属商品，优先显示
（商品数据表改造，归属门店专属）

4、首页分类标签，数据表改造，适应分类

5、顾客端要增加【系统通知】页，显示后台管理员发送的通知

6、轮播图放合伙人广告（广告图片设计）

7、常见问题、合伙人协议（内容编写）



合伙人端
》》1、消息页用户头像和昵称显示————

》》2、消息页群聊卡片页导航栏显示问题————


》》3、【门店订单】页，采购价显示、操作用户显示、状态分类显示————

》》4、钱包页未开发提示改成未开放————

》》5、【我的门店】页的UI，背景色————


6、M型合伙人的加入方式要显示“总部委派”

7、移库订单功能

8、上传门店专属商品

9、门店设置功能（库存、橱窗设置）


管理端
》》1、用户管理页有重叠内容、门店管理页有重叠内容————————

》》2、管理端导航栏冒泡————————


》》3、消息管理，屏蔽功能实现，添加删除功能————————

》》4、VIP会员权益的管理################——————


》》5、创建商品类别功能
，，创建商品从抽屉组件改为常规页面，可以选择类别，创建类别时使用抽屉组件
，，

》》6、根据商品类别自动编号商品ID

》》7、给合伙人发送通知功能

8、价格模板功能（根据基准价自动填写各级采购价、零售价）

9、用户数据表改造优化（判断身份减少关联查询，显示身份级别联表）

10、当门店镜像库存高于平台库存时，触发采购退货

11、管理员角色创建、权限设置（运营管理、财务管理、平台管理、系统管理）

12、财务管理功能（发红包，提现，充值，商品折扣，会员折扣）

13、

****开发PC端后台管理系统


