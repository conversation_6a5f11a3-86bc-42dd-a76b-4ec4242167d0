/**
 * 门店库存路由
 */
const express = require('express');
const router = express.Router();
const storeInventoryController = require('../controllers/storeInventoryController');
const { checkAuth } = require('../middleware/auth');

// 获取门店库存商品列表
router.get('/', checkAuth, storeInventoryController.getInventoryProducts);

// 更新门店商品库存
router.post('/update', checkAuth, storeInventoryController.updateInventory);

// 批量更新门店商品库存
router.post('/batch-update', checkAuth, storeInventoryController.batchUpdateInventory);

// 检查门店线下库存
router.post('/check-offline', checkAuth, storeInventoryController.checkOfflineInventory);

module.exports = router;