/* 必填字段标识 */
.required {
  color: #ff4d4f;
  font-weight: bold;
}

/* 可选字段标识 */
.optional {
  color: #999;
  font-size: 24rpx;
  font-weight: normal;
}

/* 表单组样式 */
.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #1E6A9E;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
  resize: none;
}

.form-textarea:focus {
  border-color: #1E6A9E;
}

.form-picker {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  box-sizing: border-box;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

/* 图片上传区域 */
.image-upload-area {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 比例 (9/16 = 0.5625) */
  border: 2rpx dashed #e8e8e8;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  overflow: hidden;
}

.store-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-hint {
  position: absolute;
  bottom: 16rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 24rpx;
  color: #666;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  cursor: pointer;
}

.delete-image-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.delete-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 抽屉样式 */
.drawer-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.drawer-mask-show {
  display: block;
  opacity: 1;
  visibility: visible;
}

.drawer-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  padding: 0 40rpx;
  box-sizing: border-box;
}

.drawer-mask-show .drawer-content {
  transform: translateY(0);
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0 24rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.drawer-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.drawer-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-icon {
  font-size: 36rpx;
  color: #999;
  font-weight: bold;
}

.drawer-body {
  flex: 1;
  padding: 32rpx 0;
  overflow-y: auto;
}

.drawer-footer {
  display: flex;
  gap: 24rpx;
  padding: 24rpx 0 96rpx;
  border-top: 2rpx solid #f0f0f0;
}

.btn {
  flex: 1;
  margin: 0 12rpx;
  height: 72rpx;
  line-height: 72rpx;
  font-size: 30rpx;
  border-radius: 12rpx;
  border: none;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(255,77,79,0.08);
  transition: background 0.2s;
}

.btn-cancel {
  background: #fff;
  color: #ff4d4f;
  border: 1rpx solid #ff4d4f;
}

.btn-confirm {
  background: linear-gradient(90deg, #ff4d4f 0%, #ff7a45 100%);
  color: #fff;
  border: none;
}