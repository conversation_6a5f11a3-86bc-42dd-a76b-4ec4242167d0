/**
 * 商品控制器
 */
const productService = require('../services/productService');

exports.getProducts = async (req, res, next) => {
  try {
    // 参数类型转换，page和pageSize为数字，其余保持原类型
    const query = { ...req.query };
    if (query.page) query.page = Number(query.page);
    if (query.pageSize) query.pageSize = Number(query.pageSize);
    // categoryId和subCategoryId保持字符串类型
    
    // 从请求头或查询参数中获取门店级别
    const storeLevel = req.headers['x-store-level'] || req.query.storeLevel;
    if (storeLevel) {
      query.storeLevel = storeLevel;
      console.log('接收到门店级别参数:', storeLevel);
    } else {
      console.log('未接收到门店级别参数');
      console.log('请求头:', req.headers);
      console.log('查询参数:', req.query);
    }
    
    console.log('商品查询参数:', query);
    
    const result = await productService.getProducts(query);
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.getProductById = async (req, res, next) => {
  try {
    const result = await productService.getProductById(req.params.id);
    if (!result.success) {
      return res.status(404).json(result);
    }
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.getCategories = async (req, res, next) => {
  try {
    const result = await productService.getCategories();
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.getShopCategories = async (req, res, next) => {
  try {
    const result = await productService.getShopCategories();
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.getShopSubCategories = async (req, res, next) => {
  try {
    const result = await productService.getShopSubCategories(req.query.parentId);
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.getBanners = async (req, res, next) => {
  try {
    const result = await productService.getBanners();
    res.json(result);
  } catch (error) {
    next(error);
  }
};

// 批量修改商品状态
exports.batchUpdateStatus = async (req, res, next) => {
  try {
    const { ids, status } = req.body;
    if (!Array.isArray(ids) || ids.length === 0 || ![0, 1, 2].includes(Number(status))) {
      return res.status(400).json({ success: false, message: '参数错误' });
    }
    const db = require('../config/db');
    const placeholders = ids.map(() => '?').join(',');
    const sql = `UPDATE products SET status = ? WHERE id IN (${placeholders})`;
    await db.query(sql, [status, ...ids]);
    res.json({ success: true, message: '批量修改成功' });
  } catch (error) {
    next(error);
  }
};

// 更新商品信息（增强日志）
exports.updateProduct = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    if (!id) {
      return res.status(400).json({ success: false, message: '商品ID不能为空' });
    }
    // 确保使用正确的images字段名（数据库字段为images，不是image）
    // 前端发送的images字段直接使用，不需要转换
    
    // 自动将 cloud:// fileID 转为 HTTPS 链接
    if (updateData.images && typeof updateData.images === 'string' && updateData.images.startsWith('cloud://')) {
      try {
        // 获取微信云托管临时链接
        const axios = require('axios');
        const appid = process.env.WX_APPID || process.env.WECHAT_APPID;
        const secret = process.env.WX_SECRET || process.env.WECHAT_SECRET;
        // 获取 access_token
        const tokenResp = await axios.get(`https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appid}&secret=${secret}`);
        const access_token = tokenResp.data.access_token;
        if (access_token) {
          // 获取临时链接
          const fileList = [{ fileid: updateData.image, max_age: 60 * 60 * 24 }];
          const urlResp = await axios.post(`https://api.weixin.qq.com/tcb/batchdownloadfile?access_token=${access_token}`, {
            env: process.env.WX_CLOUD_ENV || process.env.WECHAT_CLOUD_ENV,
            file_list: fileList
          });
          if (urlResp.data && urlResp.data.file_list && urlResp.data.file_list[0] && urlResp.data.file_list[0].download_url) {
            updateData.images = urlResp.data.file_list[0].download_url;
          }
        }
      } catch (err) {
        console.error('[商品编辑] fileID转HTTPS失败:', err.message);
        // 保留原始 images 字段
      }
    }
    const db = require('../config/db');
    // 只允许更新指定字段（数据库字段为images，不是image）
    const allowedFields = ['sku', 'name', 'images', 'spec', 'platform_price', 'store_price', 'retail_price'];
    const fields = [];
    const values = [];
    for (const key of allowedFields) {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    }
    if (!fields.length) {
      return res.status(400).json({ success: false, message: '没有可更新的字段' });
    }
    const sql = `UPDATE products SET ${fields.join(', ')} WHERE id = ?`;
    values.push(id);
    console.log('[商品编辑] SQL:', sql);
    console.log('[商品编辑] 参数:', values);
    await db.query(sql, values);
    res.json({ success: true, message: '商品信息更新成功' });
  } catch (error) {
    console.error('[商品编辑] 更新异常:', error);
    next(error);
  }
};
