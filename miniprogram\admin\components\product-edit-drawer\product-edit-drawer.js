Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    product: {
      type: Object,
      value: null
    }
  },
  data: {
    sku: '',
    name: '',
    image: '',
    imageTemp: '',
    customImageMode: false, // 标记是否使用自定义图片
    spec: '',
    platform_price: '',
    store_price: '',
    retail_price: ''
  },
  observers: {
    'product': function(product) {
      if (product) {
        // 如果当前不是自定义图片模式，才清空imageTemp
        const updateData = {
          sku: product.sku || '',
          name: product.name || '',
          image: product.images || product.image || '', // 优先使用images字段，兼容旧的image字段
          spec: product.spec || '',
          platform_price: product.platform_price != null ? String(product.platform_price) : '',
          store_price: product.store_price != null ? String(product.store_price) : '',
          retail_price: product.retail_price != null ? String(product.retail_price) : ''
        };
        
        // 只有在非自定义图片模式下才清空imageTemp和customImageMode
        if (!this.data.customImageMode) {
          updateData.imageTemp = '';
          updateData.customImageMode = false;
        }
        
        this.setData(updateData);
      }
    },
    'visible': function(visible) {
      // 当组件变为可见时，检查是否有待处理的裁剪图片
      if (visible) {
        setTimeout(() => {
          this.checkForCroppedImage();
        }, 100); // 延迟执行，确保页面数据已更新
      }
    }
  },
  lifetimes: {
    attached() {
      // 组件实例被放入页面节点树后执行
    },
    detached() {
      // 组件实例被从页面节点树移除后执行
    }
  },
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show() {
      console.log('商品编辑抽屉组件页面显示事件触发');
      // 只有在组件可见时才检查裁剪图片
      if (this.data.visible) {
        setTimeout(() => {
          this.checkForCroppedImage();
        }, 50); // 短暂延迟确保数据同步
      }
    }
  },
  methods: {
    // 检查是否有待处理的裁剪图片
    checkForCroppedImage() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      
      console.log('编辑商品组件检查裁剪图片，当前页面数据:', {
        croppedImage: currentPage.data.croppedImage,
        imageType: currentPage.data.imageType,
        visible: this.data.visible
      });
      
      // 只处理标记为编辑商品的裁剪图片
      if (currentPage.data.croppedImage && currentPage.data.imageType === 'product-edit') {
        console.log('编辑商品组件发现待处理的裁剪图片:', currentPage.data.croppedImage);
        
        // 设置临时图片显示
        this.setData({ 
          imageTemp: currentPage.data.croppedImage,
          customImageMode: true
        }, () => {
          console.log('编辑商品裁剪图片设置完成:', {
            imageTemp: this.data.imageTemp,
            customImageMode: this.data.customImageMode
          });
        });
        
        // 清除页面数据
        currentPage.setData({
          croppedImage: null,
          imageType: null,
          customImageMode: false
        });
      }
    },
    
    // 添加图片加载事件
    onImageLoad: function(e) {
      console.log('商品图片加载成功:', e.detail);
    },
    onImageError: function(e) {
      console.warn('商品图片加载失败:', e.detail);
    },
    preventTouchMove() { return false; },
    catchTouchMove() {},
    onSkuInput(e) { this.setData({ sku: e.detail.value }); },
    onNameInput(e) { this.setData({ name: e.detail.value }); },
    onSpecInput(e) { this.setData({ spec: e.detail.value }); },
    onPlatformPriceInput(e) { this.setData({ platform_price: e.detail.value }); },
    onStorePriceInput(e) { this.setData({ store_price: e.detail.value }); },
    onRetailPriceInput(e) { this.setData({ retail_price: e.detail.value }); },
    // 选择商品图片
    chooseImage() {
      console.log('开始选择商品图片');
      // 先尝试使用chooseMedia API
      try {
        wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sourceType: ['album', 'camera'],
          success: res => {
            console.log('选择商品图片成功:', res);
            const file = res.tempFiles[0];
            if (file.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
              wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
              return;
            }
            // 跳转到裁剪页面
            wx.navigateTo({
              url: `/admin/cropper/cropper?src=${encodeURIComponent(file.tempFilePath)}&type=product-edit`
            });
          },
          fail: err => {
            console.error('chooseMedia 失败:', err);
            // 检查是否是用户取消操作
            if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
              console.log('用户取消选择商品图片');
              return; // 用户取消，直接返回
            }
            // 其他错误情况才使用备选方案
            this.chooseImageFallback();
          }
        });
      } catch (error) {
        console.error('chooseMedia 异常:', error);
        // 如果chooseMedia出现异常，尝试使用chooseImage作为备选方案
        this.chooseImageFallback();
      }
    },

    // 选择商品图片备选方案
    chooseImageFallback() {
      console.log('使用chooseImage作为备选方案选择商品图片');
      wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: res => {
          console.log('chooseImage选择商品图片成功:', res);
          const tempFilePath = res.tempFilePaths[0];
          const tempFile = res.tempFiles[0];
          if (tempFile && tempFile.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
            wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
            return;
          }
          // 跳转到裁剪页面
          wx.navigateTo({
            url: `/admin/cropper/cropper?src=${encodeURIComponent(tempFilePath)}&type=product-edit`
          });
        },
        fail: err => {
          console.error('chooseImage 失败:', err);
          // 检查是否是用户取消操作
          if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
            console.log('用户取消选择商品图片');
            return; // 用户取消，直接返回
          }
          // 其他错误情况才显示提示
          wx.showToast({
            title: '选择图片失败，请检查相关权限',
            icon: 'none'
          });
        }
      });
    },
    deleteImage() {
      this.setData({ 
        imageTemp: '', 
        image: '', 
        customImageMode: false 
      });
    },
    previewImage() {
      const url = this.data.imageTemp || this.data.image;
      if (url) {
        wx.previewImage({ urls: [url] });
      }
    },
    onCancel() {
      this.triggerEvent('cancel');
    },
    async onConfirm() {
      // 校验
      if (!this.data.name) {
        wx.showToast({ title: '请输入品名', icon: 'none' });
        return;
      }
      if (!this.data.sku) {
        wx.showToast({ title: '请输入SKU号', icon: 'none' });
        return;
      }
      
      wx.showLoading({ title: '保存中', mask: true });
      
      try {
        // 1. 上传图片（如果有新选择的图片）
        let imageUrl = this.data.imageTemp || this.data.image;

        // 判断是否是临时文件路径（需要上传的图片）
        const isTempFilePath = imageUrl && (imageUrl.startsWith('wxfile://') ||
                              imageUrl.startsWith('http://tmp') ||
                              imageUrl.startsWith('https://tmp') ||
                              imageUrl.includes('tmp_') ||
                              imageUrl.includes('temp'));

        const needUploadImage = this.data.customImageMode && isTempFilePath;

        if (needUploadImage) {
          try {
            wx.showLoading({ title: '正在上传图片...' });
            const { uploadFile } = require('../../../utils/api');
            imageUrl = await uploadFile(imageUrl, 'product_images');
            wx.hideLoading();
          } catch (uploadError) {
            console.error('商品图片上传失败:', uploadError);
            wx.hideLoading();
            throw new Error('图片上传失败');
          }
        }
        
        // 2. 价格字段类型转换
        let platform_price = this.data.platform_price;
        let store_price = this.data.store_price;
        let retail_price = this.data.retail_price;
        platform_price = platform_price !== '' ? Number(platform_price) : undefined;
        store_price = store_price !== '' ? Number(store_price) : undefined;
        retail_price = retail_price !== '' ? Number(retail_price) : undefined;
        
        // 3. 构建保存数据
        const payload = {
          sku: this.data.sku,
          name: this.data.name,
          images: imageUrl, // 字段名改为 images
          spec: this.data.spec
        };
        if (platform_price !== undefined && !isNaN(platform_price)) payload.platform_price = platform_price;
        if (store_price !== undefined && !isNaN(store_price)) payload.store_price = store_price;
        if (retail_price !== undefined && !isNaN(retail_price)) payload.retail_price = retail_price;
        
        wx.hideLoading();
        this.triggerEvent('confirm', payload);
      } catch (error) {
        console.error('保存商品失败:', error);
        wx.hideLoading();
        wx.showToast({ 
          title: '保存失败: ' + (error.message || '未知错误'), 
          icon: 'none' 
        });
      }
    }
  }
});