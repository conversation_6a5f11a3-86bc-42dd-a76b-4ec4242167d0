// 管理端图片裁剪页面 - 支持动态裁剪比例
// 获取设备信息，避免使用已弃用的API
let device = { windowWidth: 375 }; // 默认值
try {
  // 使用同步API但添加错误处理
  device = wx.getSystemInfoSync();
} catch (e) {
  console.warn('获取系统信息失败，使用默认值:', e);
}
// 计算裁剪窗口基础宽度：95vw，以屏幕宽度为准，不限制最大像素
const baseWidth = device.windowWidth * 0.95;

// 根据图片类型计算裁剪窗口尺寸
function getCropSize(imageType) {
  const width = baseWidth;
  let height;
  
  switch(imageType) {
    case 'banner':
    case 'newBanner':
    case 'storeImage':
    case 'storeImageForEdit':
    case 'storeImageForCreate':
      // 16:9 比例用于轮播图和门店形象照
      height = width * 9 / 16;
      break;
    case 'logo':
    case 'store':
    case 'product':
    case 'product-create':
    case 'product-edit':
    case 'material':
    default:
      // 1:1 比例用于头像、商品图等
      height = width;
      break;
  }
  
  console.log(`图片类型 ${imageType} 的裁剪尺寸:`, { width, height });
  return { width, height };
}

Page({
  data: {
    src: '',
    cropWidth: baseWidth,
    cropHeight: baseWidth,
    imgInfo: null,
    scale: 1,
    minScale: 1,
    maxScale: 4,
    offsetX: 0,
    offsetY: 0,
    lastX: 0,
    lastY: 0,
    lastDist: 0,
    mode: '', // 'move' or 'scale'
    imageType: '', // 图片类型：logo, banner, product等
    cropBoxStyle: '' // 动态裁剪窗口样式
  },

  onLoad(options) {
    console.log('管理端裁剪页面加载，参数:', options);
    if (options && options.src) {
      let src = decodeURIComponent(options.src);
      console.log('解码后的图片路径:', src);
      
      // 获取图片类型参数（管理端使用type参数）
      const imageType = options.type || 'store';
      console.log('图片类型:', imageType);
      
      // 根据图片类型计算裁剪窗口尺寸
      const { width: cropWidth, height: cropHeight } = getCropSize(imageType);
      
      // 根据图片类型设置页面标题
      this.setPageTitle(imageType);
      
      this.setData({
        src,
        imageType,
        cropWidth,
        cropHeight
      });

      // 设置裁剪窗口的显示尺寸（这会更新cropWidth和cropHeight）
      this.setCropBoxStyle(imageType, cropWidth, cropHeight);

      // 延迟初始化，确保窗口尺寸设置完成
      setTimeout(() => {
        this.initImage();
      }, 100);
    } else {
      console.error('裁剪页面缺少图片路径参数');
      wx.showToast({ title: '缺少图片参数', icon: 'none' });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 页面显示时重新设置标题，确保标题正确
  onShow() {
    const { imageType } = this.data;
    if (imageType) {
      this.setPageTitle(imageType);
      console.log('页面显示时重新设置标题，图片类型:', imageType);
    }
  },

  // 根据图片类型设置页面标题
  setPageTitle(imageType) {
    const titleMap = {
      'store': '裁剪门店图片',
      'product': '裁剪商品图片',
      'product-create': '裁剪商品图片 - 创建',
      'product-edit': '裁剪商品图片 - 编辑',
      'logo': '裁剪公司Logo',
      'banner': '裁剪轮播图片 (16:9)',
      'newBanner': '裁剪新品横幅 (16:9)',
      'storeImage': '裁剪门店形象照 (16:9)',
      'storeImageForEdit': '裁剪门店形象照 (16:9) - 编辑',
      'storeImageForCreate': '裁剪门店形象照 (16:9) - 创建',
      'material': '裁剪素材图片'
    };

    const title = titleMap[imageType] || '裁剪图片';
    wx.setNavigationBarTitle({ title });
    console.log('设置页面标题:', title);
  },

  // 设置裁剪窗口样式
  setCropBoxStyle(imageType, cropWidth, cropHeight) {
    console.log('设置裁剪窗口样式，图片类型:', imageType);
    console.log('裁剪尺寸:', { cropWidth, cropHeight });

    // 获取屏幕宽度，避免使用已弃用的API
    let screenWidth = 375; // 默认值
    try {
      screenWidth = wx.getSystemInfoSync().windowWidth;
    } catch (e) {
      console.warn('获取屏幕宽度失败，使用默认值:', e);
    }
    let boxStyle = '';

    if (imageType === 'storeImage' || imageType === 'storeImageForEdit' || imageType === 'storeImageForCreate' || imageType === 'banner' || imageType === 'newBanner') {
      // 16:9 比例的裁剪窗口
      const aspectRatio = 16 / 9;
      const maxWidth = screenWidth * 0.9; // 90vw
      const boxWidth = Math.min(maxWidth, cropWidth);
      const boxHeight = boxWidth / aspectRatio;

      // 更新实际的裁剪尺寸，确保Canvas尺寸与显示窗口一致
      this.setData({
        cropWidth: boxWidth,
        cropHeight: boxHeight
      });

      boxStyle = `width: ${boxWidth}px; height: ${boxHeight}px;`;
      console.log('16:9裁剪窗口尺寸:', { boxWidth, boxHeight, aspectRatio });
    } else {
      // 1:1 比例的裁剪窗口
      const maxSize = Math.min(screenWidth * 0.9, cropWidth);
      
      // 更新实际的裁剪尺寸
      this.setData({
        cropWidth: maxSize,
        cropHeight: maxSize
      });
      
      boxStyle = `width: ${maxSize}px; height: ${maxSize}px;`;
      console.log('1:1裁剪窗口尺寸:', maxSize);
    }

    this.setData({
      cropBoxStyle: boxStyle
    });

    console.log('设置裁剪窗口样式:', boxStyle);
  },

  initImage() {
    const { src, cropWidth, cropHeight } = this.data;
    if (!src) return;

    console.log('开始初始化图片，路径:', src);
    console.log('当前裁剪窗口尺寸:', { cropWidth, cropHeight });

    // 直接使用适配算法，确保图片完整显示
    this.setupImageFitDisplay(src, cropWidth, cropHeight);
  },

  // 设置图片适配显示
  setupImageFitDisplay(src, cropWidth, cropHeight) {
    console.log('设置图片适配显示');

    // 先尝试获取图片信息，如果失败则使用默认策略
    wx.getImageInfo({
      src: src,
      success: (res) => {
        console.log('获取图片信息成功:', res);
        this.calculateImageFit(src, res.width, res.height, cropWidth, cropHeight);
      },
      fail: (err) => {
        console.log('获取图片信息失败，使用默认策略:', err);
        // 使用常见的图片比例作为默认值
        this.calculateImageFit(src, 1080, 1080, cropWidth, cropHeight);
      }
    });
  },

  // 计算图片适配参数
  calculateImageFit(src, imgWidth, imgHeight, cropWidth, cropHeight) {
    console.log('计算图片适配参数:', {
      imgWidth,
      imgHeight,
      cropWidth,
      cropHeight,
      imageType: this.data.imageType
    });

    const imgInfo = {
      width: imgWidth,
      height: imgHeight,
      path: src
    };

    // 计算缩放比例，保持图片原始比例
    const scaleX = cropWidth / imgWidth;
    const scaleY = cropHeight / imgHeight;

    let scale;
    let displayMode;

    // 根据图片类型决定缩放策略
    if (this.data.imageType === 'storeImage' || this.data.imageType === 'storeImageForEdit' || this.data.imageType === 'storeImageForCreate' || this.data.imageType === 'banner' || this.data.imageType === 'newBanner' || this.data.imageType === 'product' || this.data.imageType === 'product-create' || this.data.imageType === 'product-edit') {
      // 对于门店形象照、轮播图和商品图，使用较大的缩放比例，确保图片铺满裁剪窗口
      scale = Math.max(scaleX, scaleY);
      displayMode = '铺满模式';
    } else {
      // 对于Logo等，使用较小的缩放比例，确保图片完整显示
      scale = Math.min(scaleX, scaleY);
      displayMode = '完整显示模式';
    }

    // 确保最小缩放比例，避免图片过小
    const minDisplayScale = Math.min(scaleX, scaleY);
    if (scale < minDisplayScale * 0.8) {
      scale = minDisplayScale;
      console.log('调整缩放比例以确保图片可见性');
    }

    // 计算缩放后的尺寸
    const scaledWidth = imgWidth * scale;
    const scaledHeight = imgHeight * scale;

    // 计算居中偏移，确保图片在裁剪窗口中央
    let offsetX = (cropWidth - scaledWidth) / 2;
    let offsetY = (cropHeight - scaledHeight) / 2;

    // 对于铺满模式，确保图片完全覆盖裁剪区域并居中显示
    if (displayMode === '铺满模式') {
      // 铺满模式下，图片应该完全覆盖裁剪区域，超出部分会被裁剪
      // 保持居中偏移，让用户可以通过拖拽调整显示区域
      offsetX = (cropWidth - scaledWidth) / 2;
      offsetY = (cropHeight - scaledHeight) / 2;
    }

    console.log(`图片${displayMode}适配参数:`, {
      scaleX: scaleX.toFixed(3),
      scaleY: scaleY.toFixed(3),
      selectedScale: scale.toFixed(3),
      scaledWidth: scaledWidth.toFixed(1),
      scaledHeight: scaledHeight.toFixed(1),
      offsetX: offsetX.toFixed(1),
      offsetY: offsetY.toFixed(1),
      displayMode
    });

    // 根据显示模式设置最小缩放比例
    let minScale;
    if (displayMode === '铺满模式') {
      // 铺满模式下，最小缩放比例应确保图片始终能覆盖裁剪区域
      minScale = Math.max(scaleX, scaleY) * 0.8; // 最小不低于铺满比例的80%
    } else {
      // 完整显示模式下，允许更小的缩放比例
      minScale = Math.max(scale * 0.3, minDisplayScale * 0.5);
    }

    this.setData({
      imgInfo,
      scale,
      minScale,  // 使用计算后的最小缩放比例
      maxScale: scale * 4,   // 最大放大4倍
      offsetX,
      offsetY,
      isInitialized: true
    }, () => {
      // 延迟绘制，确保数据设置完成
      setTimeout(() => {
        this.drawToCanvas();
      }, 100);
    });
  },

  drawToCanvas() {
    const { src, cropWidth, cropHeight, imgInfo, scale, offsetX, offsetY, isInitialized } = this.data;
    if (!imgInfo || !isInitialized) {
      console.log('绘制画布跳过，缺少必要数据:', { imgInfo: !!imgInfo, isInitialized });
      return;
    }

    console.log('绘制画布，参数:', {
      scale,
      offsetX,
      offsetY,
      imgInfo,
      cropWidth,
      cropHeight
    });

    const ctx = wx.createCanvasContext('cropperCanvas', this);

    // 清空画布，使用白色背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, cropWidth, cropHeight);

    // 计算绘制尺寸，保持原始比例
    const scaledWidth = imgInfo.width * scale;
    const scaledHeight = imgInfo.height * scale;

    // 确保绘制位置正确，图片居中显示
    const drawX = offsetX;
    const drawY = offsetY;

    console.log('绘制图片参数:', {
        原始尺寸: `${imgInfo.width}x${imgInfo.height}`,
        缩放比例: scale.toFixed(3),
        绘制尺寸: `${scaledWidth.toFixed(1)}x${scaledHeight.toFixed(1)}`,
        绘制位置: `(${drawX.toFixed(1)}, ${drawY.toFixed(1)})`,
        Canvas尺寸: `${cropWidth}x${cropHeight}`,
        图片类型: this.data.imageType
      });

      try {
        // 确保图片路径有效
        if (src && src.length > 0) {
          // 按计算好的参数绘制图片
          ctx.drawImage(src, drawX, drawY, scaledWidth, scaledHeight);
          console.log('图片绘制完成，位置和尺寸已优化');
        } else {
          console.error('图片路径无效:', src);
        }

      ctx.draw(false, () => {
        console.log('Canvas绘制完成');
      });
    } catch (error) {
      console.error('Canvas绘制失败:', error);
      // 如果绘制失败，尝试重新绘制
      setTimeout(() => {
        console.log('尝试重新绘制');
        this.retryDraw();
      }, 500);
    }
  },

  // 重试绘制
  retryDraw() {
    const { src, cropWidth, cropHeight, imgInfo, scale, offsetX, offsetY } = this.data;
    const ctx = wx.createCanvasContext('cropperCanvas', this);

    console.log('重试绘制图片，使用当前参数');

    // 设置画布背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, cropWidth, cropHeight);

    if (imgInfo && scale && offsetX !== undefined && offsetY !== undefined) {
      // 使用当前的缩放和偏移参数
      const scaledWidth = imgInfo.width * scale;
      const scaledHeight = imgInfo.height * scale;
      ctx.drawImage(src, offsetX, offsetY, scaledWidth, scaledHeight);
      console.log('重试绘制使用参数:', { scaledWidth, scaledHeight, offsetX, offsetY });
    } else {
      // 如果参数不完整，使用简化的居中绘制
      const size = Math.min(cropWidth, cropHeight) * 0.9;
      const x = (cropWidth - size) / 2;
      const y = (cropHeight - size) / 2;
      ctx.drawImage(src, x, y, size, size);
      console.log('重试绘制使用简化参数:', { size, x, y });
    }

    ctx.draw();
  },

  touchStart(e) {
    console.log('触摸开始:', e.touches.length);
    if (e.touches.length === 1) {
      this.setData({
        mode: 'move',
        lastX: e.touches[0].clientX,
        lastY: e.touches[0].clientY
      });
    } else if (e.touches.length === 2) {
      this.setData({
        mode: 'scale',
        lastDist: this.getDistance(e.touches[0], e.touches[1])
      });
    }
  },

  touchMove(e) {
    const { mode, lastX, lastY, lastDist, scale, minScale, maxScale, offsetX, offsetY, imgInfo, cropWidth, cropHeight } = this.data;

    if (mode === 'move' && e.touches.length === 1) {
      const deltaX = e.touches[0].clientX - lastX;
      const deltaY = e.touches[0].clientY - lastY;

      let newOffsetX = offsetX + deltaX;
      let newOffsetY = offsetY + deltaY;

      // 限制边界，确保图片不会完全移出裁剪区
      const scaledWidth = imgInfo.width * scale;
      const scaledHeight = imgInfo.height * scale;
      const maxOffsetX = cropWidth - scaledWidth;
      const maxOffsetY = cropHeight - scaledHeight;

      // 如果图片比裁剪区小，则不允许移动
      if (scaledWidth <= cropWidth) {
        newOffsetX = (cropWidth - scaledWidth) / 2;
      } else {
        newOffsetX = Math.max(maxOffsetX, Math.min(0, newOffsetX));
      }

      if (scaledHeight <= cropHeight) {
        newOffsetY = (cropHeight - scaledHeight) / 2;
      } else {
        newOffsetY = Math.max(maxOffsetY, Math.min(0, newOffsetY));
      }

      this.setData({
        offsetX: newOffsetX,
        offsetY: newOffsetY,
        lastX: e.touches[0].clientX,
        lastY: e.touches[0].clientY
      }, this.drawToCanvas);
    } else if (mode === 'scale' && e.touches.length === 2) {
      const currentDist = this.getDistance(e.touches[0], e.touches[1]);
      const scaleRatio = currentDist / lastDist;

      let newScale = scale * scaleRatio;
      newScale = Math.max(minScale, Math.min(maxScale, newScale));

      // 缩放时保持图片居中
      const oldScaledWidth = imgInfo.width * scale;
      const oldScaledHeight = imgInfo.height * scale;
      const newScaledWidth = imgInfo.width * newScale;
      const newScaledHeight = imgInfo.height * newScale;

      const newOffsetX = offsetX + (oldScaledWidth - newScaledWidth) / 2;
      const newOffsetY = offsetY + (oldScaledHeight - newScaledHeight) / 2;

      this.setData({
        scale: newScale,
        offsetX: newOffsetX,
        offsetY: newOffsetY,
        lastDist: currentDist
      }, this.drawToCanvas);
    }
  },

  touchEnd() {
    console.log('触摸结束');
    this.setData({ mode: '' });
  },

  getDistance(touch1, touch2) {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  },

  onCancel() {
    wx.navigateBack();
  },

  doCrop() {
    const { cropWidth, cropHeight, imageType } = this.data;
    
    // 显示加载中提示
    wx.showLoading({
      title: '正在裁剪...',
      mask: true
    });
    
    // 准备裁剪图片
    wx.canvasToTempFilePath({
      canvasId: 'cropperCanvas',
      width: cropWidth,
      height: cropHeight,
      destWidth: cropWidth,
      destHeight: cropHeight,
      fileType: 'jpg',
      quality: 0.8,
      success: res => {
        wx.hideLoading();
        console.log('管理端图片裁剪成功，临时路径：', res.tempFilePath);
        
        // 获取上一个页面实例
        const pages = getCurrentPages();
        const prevPage = pages[pages.length - 2];
        
        if (prevPage) {
          try {
            // 根据不同的图片类型设置相应的数据字段
            let updateData = {};
            
            switch (imageType) {
              case 'store':
                updateData = {
                  croppedStoreImage: res.tempFilePath,
                  selectedStoreImage: res.tempFilePath,
                  customStoreImageMode: true
                };
                break;
              case 'product':
                updateData = {
                  croppedImage: res.tempFilePath,
                  imageType: 'product',
                  customImageMode: true
                };
                break;
              case 'product-create':
                updateData = {
                  croppedImage: res.tempFilePath,
                  imageType: 'product-create',
                  customImageMode: true
                };
                break;
              case 'product-edit':
                updateData = {
                  croppedImage: res.tempFilePath,
                  imageType: 'product-edit',
                  customImageMode: true
                };
                break;
              case 'logo':
                updateData = {
                  croppedLogo: res.tempFilePath,
                  selectedLogo: res.tempFilePath,
                  customLogoMode: true
                };
                break;
              case 'banner':
                updateData = {
                  croppedImage: res.tempFilePath, // 修复：使用croppedImage字段以匹配materials.js的处理逻辑
                  imageType: 'banner', // 添加imageType标识
                  croppedBanner: res.tempFilePath, // 保留原字段以防其他地方使用
                  selectedBanner: res.tempFilePath,
                  customBannerMode: true
                };
                break;
              case 'newBanner':
                updateData = {
                  croppedImage: res.tempFilePath,
                  selectedImage: res.tempFilePath,
                  customImageMode: true,
                  imageType: 'newBanner'
                };
                break;
              case 'storeImage':
                updateData = {
                  croppedStoreImage: res.tempFilePath,
                  storeImageTemp: res.tempFilePath,
                  customImageMode: true
                };
                break;
              case 'storeImageForEdit':
                // 编辑门店时的图片裁剪结果
                updateData = {
                  croppedStoreImageForEdit: res.tempFilePath,
                  storeImageTemp: res.tempFilePath,
                  customImageMode: true
                };
                break;
              case 'storeImageForCreate':
                // 创建门店时的图片裁剪结果
                updateData = {
                  croppedStoreImageForCreate: res.tempFilePath,
                  storeImageTemp: res.tempFilePath,
                  customImageMode: true
                };
                break;
              case 'material':
                updateData = {
                  croppedImage: res.tempFilePath,
                  imageTemp: res.tempFilePath,
                  customImageMode: true
                };
                break;
              default:
                updateData = {
                  croppedImage: res.tempFilePath,
                  selectedImage: res.tempFilePath,
                  customImageMode: true
                };
            }
            
            console.log('设置上一页数据，类型:', imageType, '数据:', updateData);
            prevPage.setData(updateData);

            wx.showToast({
              title: '裁剪成功',
              icon: 'success',
              duration: 1000
            });

            setTimeout(() => {
              wx.navigateBack();
            }, 1000);
          } catch (error) {
            console.error('设置上一页数据失败:', error);
            wx.showToast({ title: '返回数据失败', icon: 'none' });
          }
        } else {
          console.error('找不到上一页面实例');
          wx.showToast({ title: '找不到上一页面', icon: 'none' });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('管理端裁剪失败:', err);
        wx.showToast({ title: '裁剪失败', icon: 'none' });
      }
    }, this);
  }
});