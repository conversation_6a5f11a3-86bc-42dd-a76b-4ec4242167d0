const { validateLoginState } = require('../../utils/login-state-manager');
const { vipApi } = require('../../utils/api');

Page({
  data: {
    userInfo: {},
    vipInfo: {
      expireDate: '',
      levelName: '',
      levelCode: ''
    },
    currentProductIndex: 0,
    currentVipBenefits: [], // 当前VIP用户的权益列表
    vipProducts: [],
    loading: {
      vipInfo: false,
      vipProducts: false,
      vipBenefits: false
    },
    defaultVipProducts: [
      {
        id: 1,
        level_code: 'v1',
        level_name: 'V1青铜会员',
        benefits: ['专属会员标识', '基础客服支持', '会员专属活动', '基础文档模板', '每月免费咨询1次'],
        buttonText: '立即开通',
        price: 99
      },
      {
        id: 2,
        level_code: 'v2',
        level_name: 'V2白银会员',
        benefits: ['全部青铜会员权益', '白银会员专属标识', '7×12小时专属客服', '9折服务优惠', '每月免费咨询2次', '高级文档模板库'],
        buttonText: '立即开通',
        price: 299
      },
      {
        id: 3,
        level_code: 'v3',
        level_name: 'V3黄金会员',
        benefits: ['全部白银会员权益', '黄金会员专属标识', '专享85折服务优惠', '高级会员专属礼包', '每月免费咨询3次', '专属高级模板库'],
        buttonText: '立即开通',
        price: 599
      },
      {
        id: 4,
        level_code: 'v4',
        level_name: 'V4钻石会员',
        benefits: ['全部黄金会员权益', '钻石会员专属标识', '一对一专属服务顾问', '专享8折服务优惠', '每月免费咨询5次', '项目优先对接'],
        buttonText: '立即开通',
        price: 999
      },
      {
        id: 5,
        level_code: 'v5',
        level_name: 'V5王者会员',
        benefits: ['全部钻石会员权益', '王者会员至尊标识', '专属VIP服务团队', '专享7折服务优惠', '无限制专业咨询', '所有线下活动免费'],
        buttonText: '立即开通',
        price: 1999
      }
    ],
    identityLabel: ''
  },
  onLoad: function (options) {
    this.checkLoginAndInit();
  },

  onShow: function() {
    this.fetchVIPData();
  },

  // 切换到上一个会员产品
  prevProduct: function() {
    if (this.data.currentProductIndex > 0) {
      this.setData({
        currentProductIndex: this.data.currentProductIndex - 1
      });
    }
  },

  // 切换到下一个会员产品
  nextProduct: function() {
    if (this.data.currentProductIndex < this.data.vipProducts.length - 1) {
      this.setData({
        currentProductIndex: this.data.currentProductIndex + 1
      });
    }
  },

  // 监听轮播图切换事件
  onProductSwiperChange: function(e) {
    this.setData({
      currentProductIndex: e.detail.current
    });
  },

  // 获取VIP相关数据
  fetchVIPData: function() {
    const that = this;

    // 获取用户会员信息
    this.setData({ 'loading.vipInfo': true });
    vipApi.getUserVipInfo()
      .then(res => {
        if (res.success && res.data) {
          // 格式化过期时间
          const expireDate = that.formatDate(res.data.expire_time);
          // 新增：身份标签逻辑（VIP中心页面使用role_type显示身份）
          let identityLabel = '';
          if (res.data.role === 'admin') {
            // VIP中心页面非主页面，使用role_type而不是role_name
            identityLabel = this.getRoleTypeLabel(res.data.role) || '';
          } else {
            identityLabel = res.data.vipLevel || '';
          }
          that.setData({
            vipInfo: {
              ...res.data,
              expireDate
            },
            identityLabel
          });

          // 根据用户当前会员等级，设置当前展示的会员产品索引
          that.setInitialProductIndex(res.data.levelCode);
        }
      })
      .catch(err => {
        console.error('获取会员信息失败:', err);
        wx.showToast({ title: '获取会员信息失败', icon: 'none' });
      })
      .finally(() => {
        that.setData({ 'loading.vipInfo': false });
      });

    // 获取会员产品列表
    this.setData({ 'loading.vipProducts': true });
    vipApi.getVipLevels()
      .then(res => {
        if (res.success && res.data) {
          // 保存原始会员产品数据
          const vipLevelData = res.data;

          // 获取所有会员产品的权益
          that.fetchAllVipBenefits(vipLevelData);
        }
      })
      .catch(err => {
        console.error('获取会员等级列表失败:', err);
        wx.showToast({ title: '获取会员产品失败', icon: 'none' });

        // 失败时使用默认数据
        that.setData({ vipProducts: that.data.defaultVipProducts });
      })
      .finally(() => {
        that.setData({ 'loading.vipProducts': false });
      });
  },

  // 获取所有会员等级的权益
  fetchAllVipBenefits: function(vipLevelData) {
    const that = this;
    this.setData({ 'loading.vipBenefits': true });

    vipApi.getVipBenefits()
      .then(res => {
        if (res.success && res.data) {
          const benefitsData = res.data;

          // 组装会员产品数据（等级+权益）
          let vipProducts = [];

          vipLevelData.forEach(level => {
            if (level.level_code !== 'free') { // 排除普通用户
              const levelBenefits = benefitsData
                .filter(b => b.level_code === level.level_code)
                .map(b => b.benefit_name)
                .filter((benefit, index, arr) => arr.indexOf(benefit) === index); // 去重

              // 限制权益数量，避免卡片内容过多
              const limitedBenefits = levelBenefits.slice(0, 6); // 最多显示6条权益

              vipProducts.push({
                id: level.id,
                level_code: level.level_code,
                level_name: level.level_name,
                description: level.description,
                benefits: limitedBenefits,
                price: level.price,
                buttonText: that.getButtonText(level.level_code)
              });
            }
          });

          // 更新会员产品数据
          that.setData({ vipProducts });

          // 设置当前展示的会员产品索引
          if (that.data.vipInfo.level_code) {
            that.setInitialProductIndex(that.data.vipInfo.level_code);
          }

          // 设置当前VIP用户的权益
          that.setCurrentVipBenefits(vipProducts, benefitsData);
        }
      })
      .catch(err => {
        console.error('获取会员权益失败:', err);

        // 失败时使用默认数据
        that.setData({ vipProducts: that.data.defaultVipProducts });
        that.setCurrentVipBenefits(that.data.defaultVipProducts, []);
      })
      .finally(() => {
        that.setData({ 'loading.vipBenefits': false });
      });
  },

  // 设置当前VIP用户的权益
  setCurrentVipBenefits: function(vipProducts, benefitsData) {
    const currentLevelCode = this.data.vipInfo.level_code;
    
    if (!currentLevelCode || currentLevelCode === 'free') {
      this.setData({ currentVipBenefits: [] });
      return;
    }

    // 从服务器数据中获取当前等级的权益
    if (benefitsData && benefitsData.length > 0) {
      const currentBenefits = benefitsData
        .filter(b => b.level_code === currentLevelCode)
        .map(b => b.benefit_name)
        .filter((benefit, index, arr) => arr.indexOf(benefit) === index); // 去重

      this.setData({ currentVipBenefits: currentBenefits });
    } else {
      // 使用默认数据
      const defaultProduct = this.data.defaultVipProducts.find(p => p.level_code === currentLevelCode);
      if (defaultProduct) {
        this.setData({ currentVipBenefits: defaultProduct.benefits });
      } else {
        this.setData({ currentVipBenefits: [] });
      }
    }
  },

  // 根据会员等级获取按钮文本
  getButtonText: function(levelCode) {
    const currentLevel = this.data.vipInfo.level_code;

    // 无会员信息或为普通用户
    if (!currentLevel || currentLevel === 'free') {
      return '立即开通';
    }

    // 当前等级与产品等级相同，显示续费
    if (currentLevel === levelCode) {
      return '会员续费';
    }

    // 从低级升高级
    const levels = ['free', 'v1', 'v2', 'v3', 'v4', 'v5'];
    const currentIndex = levels.indexOf(currentLevel);
    const targetIndex = levels.indexOf(levelCode);

    if (targetIndex > currentIndex) {
      return '升级会员';
    } else {
      return '立即开通';
    }
  },

  // 设置初始显示的会员产品索引
  setInitialProductIndex: function(levelCode) {
    const levelMap = {
      'free': 0,
      'v1': 0,
      'v2': 1,
      'v3': 2,
      'v4': 3,
      'v5': 4
    };

    const index = levelMap[levelCode] || 0;
    this.setData({ currentProductIndex: index });
  },

  // 格式化日期
  formatDate: function(dateStr) {
    if (!dateStr) return '';

    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    return `${year}年${month}月${day}日`;
  },

  // 将role_type转换为中文显示标签
  getRoleTypeLabel(roleType) {
    const roleLabels = {
      'customer': '顾客',
      'partner': '合伙人',
      'admin': '管理员'
    };
    return roleLabels[roleType] || roleType;
  },

  checkLoginAndInit() {
    const that = this;
    validateLoginState().then(res => {
      if (!res.isValid) {
        wx.showModal({
          title: '请先登录',
          content: '登录后可查看会员中心',
          showCancel: false,
          success: () => {
            wx.navigateTo({ url: '/pages/auth/auth' });
          }
        });
        return;
      }
      // 已登录，直接用本地全局信息，兼容多字段
      const app = getApp();
      let userInfo = (app.globalData && app.globalData.userInfo) || {};
      // 字段兼容
      userInfo.avatarUrl = userInfo.avatarUrl || userInfo.avatar || '/images/icons2/默认头像.png';
      userInfo.nickName = userInfo.nickName || userInfo.nickname || userInfo.username || '昵称';
      userInfo.id = userInfo.id || userInfo._id || userInfo.userid || 'XXXXXXXXXX';

      that.setData({ userInfo });

      // 获取VIP数据
      that.fetchVIPData();
    });  },

  // 处理会员购买/续费
  handleVipAction: function() {
    wx.showModal({
      title: '会员购买',
      content: '暂未开放会员功能',
      showCancel: false
    });
  }
});