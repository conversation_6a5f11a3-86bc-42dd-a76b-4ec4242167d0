const FundRecord = require('../models/FundRecord');

exports.createFundRecord = async (req, res) => {
  try {
    const { store_no, type, amount, description, voucher_images } = req.body;
    if (!store_no || !type || !amount) {
      return res.status(400).json({ success: false, message: '参数不完整' });
    }
    const result = await FundRecord.create({ store_no, type, amount, description, voucher_images });
    res.json({ success: true, id: result.id, voucher_no: result.voucher_no });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

exports.getFundRecordsByStore = async (req, res) => {
  try {
    const { store_no, limit = 50, offset = 0 } = req.query;
    if (!store_no) {
      return res.status(400).json({ success: false, message: '缺少store_no参数' });
    }
    const records = await FundRecord.getByStoreNo(store_no, parseInt(limit), parseInt(offset));
    // 只返回需要的字段
    const data = records.map(r => ({
      id: r.id,
      voucher_no: r.voucher_no,
      type: r.type,
      description: r.description,
      created_at: r.created_at,
      amount: r.amount,
      voucher_images: r.voucher_images
    }));
    res.json({ success: true, data });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};