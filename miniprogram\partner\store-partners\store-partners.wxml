<!--partner/store-partners/store-partners.wxml-->
<view class="store-partners-page">
  <!-- 门店选择区域 -->
  <view class="store-select-section">
    <view class="store-select-header">
      <view class="section-title">当前门店</view>
      <view class="store-select-container">
        <picker class="store-picker" 
                mode="selector" 
                range="{{storeList}}" 
                range-key="name" 
                bindchange="onStoreChange" 
                wx:if="{{storeList.length > 0}}">
          <view class="store-selector">
            <text class="store-name">{{selectedStore.name}}（{{selectedStore.store_no}}）</text>
            <view class="dropdown-arrow">▼</view>
          </view>
        </picker>
        <view class="store-selector" wx:else>
          <text class="store-name">暂无门店数据</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 当前用户状况 -->
  <view class="user-status-section" wx:if="{{selectedStore}}">
    <view class="user-status-grid">
      <view class="status-item">
        <view class="status-label">我的股本金</view>
        <view class="status-value">¥{{userStatus.capital}}</view>
      </view>
      <view class="status-item">
        <view class="status-label">我的股份比例</view>
        <view class="status-value">{{userStatus.percent}}%</view>
      </view>
      <view class="status-item">
        <view class="status-label">我的角色</view>
        <view class="status-value">{{userStatus.role}}</view>
      </view>
    </view>
  </view>

  <!-- 合伙人列表 -->
  <view class="partners-section" wx:if="{{selectedStore}}">
    <view class="section-header">
      <view class="section-title">合伙人列表</view>
      <view class="partner-count">共{{partnerCount}}人</view>
    </view>
    
    <!-- 加载状态 -->
    <view wx:if="{{loadingPartners}}" class="loading-container">
      <view class="loading-text">加载中...</view>
    </view>
    
    <!-- 合伙人列表 -->
    <view wx:else class="partner-list">
      <block wx:if="{{partnerList.length > 0}}">
        <view wx:for="{{partnerList}}" 
              wx:key="id" 
              class="partner-item">
          
          <!-- 合伙人头像和基本信息 -->
          <view class="partner-header">
            <image class="partner-avatar" 
                   src="{{item.avatar || '/images/profile.png'}}" 
                   mode="aspectFill" />
            <view class="partner-info">
              <view class="partner-name">{{item.nickname || '未知用户'}}</view>
              <view class="partner-id">ID: {{item.user_id}}</view>
              <view class="partner-join-date">加入日期: {{item.formatted_join_date}}</view>
            </view>
            <view class="partner-actions">
              <button class="message-btn" 
                      bindtap="sendPrivateMessage" 
                      data-user-id="{{item.user_id}}" 
                      data-user-name="{{item.nickname}}">
                发私信
              </button>
            </view>
          </view>
          
          <!-- 合伙人详细信息 -->
          <view class="partner-details">
            <view class="detail-row">
              <view class="detail-label">股本金:</view>
              <view class="detail-value">¥{{item.amount}}</view>
            </view>
            <view class="detail-row">
              <view class="detail-label">合伙人类型:</view>
              <view class="detail-value">{{item.formatted_partner_type}}</view>
            </view>
            <view class="detail-row">
              <view class="detail-label">股份比例:</view>
              <view class="detail-value">{{item.percent}}%</view>
            </view>
            <view class="detail-row">
              <view class="detail-label">加入方式:</view>
              <view class="detail-value">{{item.amount > 0 ? '投资加入' : '奖励赠送'}}</view>
            </view>
          </view>
        </view>
      </block>
      
      <!-- 空状态 -->
      <view wx:else class="empty-container">
        <image class="empty-icon" src="/images/icons2/暂无数据.svg"></image>
        <text class="empty-text">暂无合伙人数据</text>
      </view>
    </view>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-spacing"></view>
</view>