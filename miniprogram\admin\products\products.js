const { productApi } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager');

Page({
  data: {
    searchValue: '',
    activeTab: 0,
    tabs: [], // 分类列表
    tabIds: [], // 分类ID列表
    products: [],
    selectAll: false,
    page: 1,
    pageSize: 20,
    total: 0,
    selectedCount: 0, // 新增，已选中商品数量
    showEditDrawer: false,
    editProduct: null,
    showCreateDrawer: false, // 创建商品抽屉显示状态
    searchTimer: null, // 搜索防抖定时器
    // 排序和筛选相关
    sortBy: '', // 排序字段：name-名称，price-价格，空字符串表示不排序
    sortOrder: '', // 排序方向：asc-升序，desc-降序，空字符串表示不排序
    showFilter: false, // 是否显示筛选面板
    priceMin: '', // 价格区间最小值
    priceMax: '', // 价格区间最大值
    statusFilter: [], // 商品状态筛选：['on', 'off']
    filterApplied: false // 是否已应用筛选
  },

  onLoad() {
    // 页面加载时恢复全局登录状态
    loginStateManager.restoreLoginStateToGlobal();
    // 不再require wx-server-sdk，只依赖小程序端wx.cloud
    this.fetchCategories();
  },
  
  // 页面卸载时清理定时器
  onUnload() {
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer);
    }
  },

  // 获取分类
  fetchCategories() {
    productApi.getCategories().then(res => {
      console.log('分类接口返回：', res);
      if (res.success && Array.isArray(res.data)) {
        const tabs = ['全部', ...res.data.map(item => item.name)];
        const tabIds = ['', ...res.data.map(item => item.id)];
        this.setData({ tabs, tabIds });
        // 默认加载全部商品
        this.fetchProducts('');
      }
    }).catch(err => {
      console.error('分类接口请求失败', err);
    });
  },

  // 获取商品列表
  fetchProducts(categoryId = '', keyword = '') {
    const { sortBy, sortOrder, priceMin, priceMax, statusFilter } = this.data;
    
    // 构建排序类型参数 - 只有在明确指定排序字段和方向时才设置排序
    let sortType = undefined;
    if (sortBy && sortOrder) {
      if (sortBy === 'name') {
        sortType = sortOrder === 'asc' ? 'name_asc' : 'name_desc';
      } else if (sortBy === 'price') {
        sortType = sortOrder === 'asc' ? 'price_asc' : 'price_desc';
      }
    }
    
    const params = {
      categoryId,
      keyword,
      page: this.data.page,
      pageSize: this.data.pageSize,
      sortType: sortType || undefined,
      priceMin: priceMin || undefined,
      priceMax: priceMax || undefined,
      statusFilter: statusFilter.length > 0 ? statusFilter : undefined
    };
    
    console.log('发送给后端的参数:', params);
    productApi.getProducts(params).then(res => {
      console.log('商品列表接口返回：', res);
      if (res.success && res.data && Array.isArray(res.data.list)) {
        const list = res.data.list;
        // 适配前端展示字段
        let products = list.map(item => {
          // 调试输出图片字段
          console.log('商品图片字段:', item.id, item.image, item.images);
          let mainImage = '';
          if (Array.isArray(item.images) && item.images.length > 0) {
            mainImage = item.images[0];
          } else if (typeof item.images === 'string' && item.images) {
            mainImage = item.images;
          } else if (item.image) {
            mainImage = item.image;
          }
          return {
            id: item.id,
            sku: item.sku,
            name: item.name,
            spec: item.spec || '',
            price: item.price,
            platform_price: item.platform_price,
            store_price: item.store_price,
            retail_price: item.retail_price,
            image: mainImage || '/images/icons2/默认商品.png',
            images: item.images || [],
            status: item.status === 2 ? 'frozen' : (item.status === 1 ? 'on' : 'off'),
            statusText: item.status === 2 ? '冻结' : (item.status === 1 ? '已上架' : '已下架'),
            statusClass: item.status === 2 ? 'frozen' : (item.status === 1 ? 'on' : 'off'),
            checked: false
          };
        });
        // 只在小程序端调用云API
        if (products.length > 0 && typeof wx !== 'undefined' && wx.cloud && wx.cloud.getTempFileURL) {
          const cloudFileIds = products
            .filter(p => p.image && typeof p.image === 'string' && p.image.startsWith('cloud://'))
            .map(p => p.image);
          if (cloudFileIds.length > 0) {
            wx.cloud.getTempFileURL({
              fileList: cloudFileIds
            }).then(res2 => {
              const fileMap = {};
              (res2.fileList || []).forEach(f => {
                if (f.fileID && f.tempFileURL) fileMap[f.fileID] = f.tempFileURL;
              });
              products = products.map(p => {
                if (p.image && fileMap[p.image]) {
                  return { ...p, image: fileMap[p.image] };
                }
                return p;
              });
              this.setData({ products, total: res.data.total, selectedCount: 0, selectAll: false });
            }).catch(err2 => {
              console.error('云文件ID转临时链接失败', err2);
              this.setData({ products, total: res.data.total, selectedCount: 0, selectAll: false });
            });
            return;
          }
        }
        this.setData({ products, total: res.data.total, selectedCount: 0, selectAll: false });
      }
    }).catch(err => {
      console.error('商品列表接口请求失败', err);
    });
  },

  // 搜索输入 - 即输即搜模式
  onSearchInput(e) {
    const searchValue = e.detail.value;
    this.setData({ searchValue });
    
    // 清除之前的定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer);
    }
    
    // 设置新的防抖定时器，500ms后执行搜索
    const timer = setTimeout(() => {
      this.performSearch(searchValue);
    }, 500);
    
    this.setData({ searchTimer: timer });
  },
  
  // 执行搜索
  performSearch(keyword = '') {
    const { tabIds, activeTab } = this.data;
    this.setData({ page: 1 }); // 重置页码
    this.fetchProducts(tabIds[activeTab] || '', keyword);
  },
  
  // 搜索按钮（保留，用于回车确认搜索）
  onSearch() {
    // 清除防抖定时器，立即执行搜索
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer);
      this.setData({ searchTimer: null });
    }
    this.performSearch(this.data.searchValue);
  },
  // Tab切换
  onTabChange(e) {
    const idx = e.currentTarget.dataset.index;
    
    // 清除搜索定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer);
    }
    
    this.setData({ 
      activeTab: idx, 
      searchValue: '',
      searchTimer: null,
      page: 1 // 重置页码
    });
    this.fetchProducts(this.data.tabIds[idx] || '', '');
  },
  // 全选/取消
  onSelectAll() {
    const { selectAll, products } = this.data;
    const newSelectAll = !selectAll;
    const newProducts = products.map(item => ({ ...item, checked: newSelectAll }));
    const selectedCount = newSelectAll ? newProducts.length : 0;
    this.setData({
      selectAll: newSelectAll,
      products: newProducts,
      selectedCount
    });
  },
  // 单选
  onCheckProduct(e) {
    const idx = e.currentTarget.dataset.index;
    const products = this.data.products.slice();
    products[idx].checked = !products[idx].checked;
    const selectedCount = products.filter(item => item.checked).length;
    this.setData({
      products,
      selectAll: products.every(item => item.checked),
      selectedCount
    });
  },
  // 批量上架
  async onBatchUp() {
    const ids = this.data.products.filter(item => item.checked).map(item => item.id);
    if (!ids.length) {
      wx.showToast({ title: '请先选择商品', icon: 'none' });
      return;
    }
    wx.showLoading({ title: '批量上架中...' });
    try {
      const res = await productApi.batchUpdateStatus(ids, 1);
      wx.hideLoading();
      if (res.success) {
        wx.showToast({ title: '批量上架成功', icon: 'success' });
        // 刷新商品列表
        this.fetchProducts(this.data.tabIds[this.data.activeTab] || '', this.data.searchValue);
      } else {
        wx.showToast({ title: res.message || '批量上架失败', icon: 'none' });
      }
    } catch (err) {
      wx.hideLoading();
      wx.showToast({ title: '网络异常', icon: 'none' });
    }
  },
  // 批量下架
  async onBatchDown() {
    const ids = this.data.products.filter(item => item.checked).map(item => item.id);
    if (!ids.length) {
      wx.showToast({ title: '请先选择商品', icon: 'none' });
      return;
    }
    wx.showLoading({ title: '批量下架中...' });
    try {
      const res = await productApi.batchUpdateStatus(ids, 0);
      wx.hideLoading();
      if (res.success) {
        wx.showToast({ title: '批量下架成功', icon: 'success' });
        // 刷新商品列表
        this.fetchProducts(this.data.tabIds[this.data.activeTab] || '', this.data.searchValue);
      } else {
        wx.showToast({ title: res.message || '批量下架失败', icon: 'none' });
      }
    } catch (err) {
      wx.hideLoading();
      wx.showToast({ title: '网络异常', icon: 'none' });
    }
  },
  // 批量冻结（如有需求，可扩展）
  onBatchFreeze() {
    let products = this.data.products.map(item => item.checked ? { ...item, status: 'frozen', statusText: '冻结', statusClass: 'frozen' } : item);
    this.setData({ products });
    wx.showToast({ title: '批量冻结成功', icon: 'success' });
  },
  // 编辑商品
  onEditProduct(e) {
    const idx = e.currentTarget.dataset.index;
    const product = this.data.products[idx];
    this.setData({
      showEditDrawer: true,
      editProduct: { ...product }
    });
  },
  onEditDrawerCancel() {
    this.setData({ showEditDrawer: false, editProduct: null });
  },
  async onEditDrawerConfirm(e) {
    const { editProduct } = this.data;
    const updateData = { ...e.detail };
    console.log('商品编辑确认，商品ID:', editProduct.id, '更新数据:', updateData);
    wx.showLoading({ title: '保存中' });
    try {
      const res = await productApi.updateProduct(editProduct.id, updateData);
      console.log('商品更新API响应:', res);
      wx.hideLoading();
      // 增强响应判断逻辑，支持多种成功标识
      const isSuccess = res.success === true || res.success === 'true' || 
                       res.code === 200 || res.statusCode === 200;
      if (isSuccess) {
        wx.showToast({ title: '保存成功', icon: 'success' });
        this.setData({ showEditDrawer: false, editProduct: null });
        // 刷新商品列表
        this.fetchProducts(this.data.tabIds[this.data.activeTab] || '', this.data.searchValue);
      } else {
        console.error('商品更新失败:', res);
        wx.showToast({ title: res.message || '保存失败', icon: 'none' });
      }
    } catch (err) {
      console.error('商品更新异常:', err);
      wx.hideLoading();
      wx.showToast({ title: '网络异常: ' + (err.message || '未知错误'), icon: 'none' });
    }
  },
  
  // 创建商品
  onCreateProduct() {
    this.setData({ showCreateDrawer: true });
  },
  
  onCreateDrawerCancel() {
    this.setData({ showCreateDrawer: false });
  },
  
  async onCreateDrawerConfirm(e) {
    const createData = { ...e.detail };
    console.log('商品创建确认，创建数据:', createData);
    wx.showLoading({ title: '创建中' });
    try {
      const res = await productApi.createProduct(createData);
      console.log('商品创建API响应:', res);
      wx.hideLoading();
      // 增强响应判断逻辑，支持多种成功标识
      const isSuccess = res.success === true || res.success === 'true' || 
                       res.code === 200 || res.statusCode === 200;
      if (isSuccess) {
        wx.showToast({ title: '创建成功', icon: 'success' });
        this.setData({ showCreateDrawer: false });
        // 刷新商品列表
        this.fetchProducts(this.data.tabIds[this.data.activeTab] || '', this.data.searchValue);
      } else {
        console.error('商品创建失败:', res);
        wx.showToast({ title: res.message || '创建失败', icon: 'none' });
      }
    } catch (err) {
      console.error('商品创建异常:', err);
      wx.hideLoading();
      wx.showToast({ title: '网络异常: ' + (err.message || '未知错误'), icon: 'none' });
    }
  },

  // 排序相关方法 - 实现三种状态循环：升序、降序、取消
  onSortChange(e) {
    const sortBy = e.currentTarget.dataset.sort;
    const { sortBy: currentSort, sortOrder } = this.data;
    
    let newSortBy = sortBy;
    let newSortOrder = 'asc';
    
    if (currentSort === sortBy) {
      // 如果点击的是当前排序字段，按照三种状态循环
      if (sortOrder === 'asc') {
        // 升序 -> 降序
        newSortOrder = 'desc';
      } else if (sortOrder === 'desc') {
        // 降序 -> 取消排序
        newSortBy = '';
        newSortOrder = '';
      } else {
        // 取消状态 -> 升序（理论上不会到这里，但保险起见）
        newSortOrder = 'asc';
      }
    } else {
      // 如果点击的是不同的排序字段，直接设置为升序
      newSortOrder = 'asc';
    }
    
    this.setData({
      sortBy: newSortBy,
      sortOrder: newSortOrder,
      page: 1 // 重置页码
    });
    
    // 重新获取数据
    this.fetchProducts(this.data.tabIds[this.data.activeTab] || '', this.data.searchValue);
  },

  // 显示/隐藏筛选面板
  onToggleFilter() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 价格区间输入
  onPriceMinInput(e) {
    this.setData({
      priceMin: e.detail.value
    });
  },

  onPriceMaxInput(e) {
    this.setData({
      priceMax: e.detail.value
    });
  },

  // 商品状态筛选
  onStatusFilterChange(e) {
    const status = e.currentTarget.dataset.status;
    const { statusFilter } = this.data;
    let newStatusFilter = [...statusFilter];
    
    if (newStatusFilter.includes(status)) {
      // 如果已选中，则取消选中
      newStatusFilter = newStatusFilter.filter(s => s !== status);
    } else {
      // 如果未选中，则添加
      newStatusFilter.push(status);
    }
    
    this.setData({
      statusFilter: newStatusFilter
    });
  },

  // 应用筛选
  onApplyFilter() {
    const { priceMin, priceMax } = this.data;
    
    // 验证价格区间
    if (priceMin && priceMax && parseFloat(priceMin) > parseFloat(priceMax)) {
      wx.showToast({ title: '最小价格不能大于最大价格', icon: 'none' });
      return;
    }
    
    this.setData({
      showFilter: false,
      filterApplied: true,
      page: 1 // 重置页码
    });
    
    // 重新获取数据
    this.fetchProducts(this.data.tabIds[this.data.activeTab] || '', this.data.searchValue);
  },

  // 清除筛选
  onClearFilter() {
    this.setData({
      priceMin: '',
      priceMax: '',
      statusFilter: [],
      filterApplied: false,
      showFilter: false,
      page: 1 // 重置页码
    });
    
    // 重新获取数据
    this.fetchProducts(this.data.tabIds[this.data.activeTab] || '', this.data.searchValue);
  }
});