const db = require('../../config/db');

/**
 * 资金分配服务层
 * 处理订单资金分配的通用逻辑
 */
class FundAllocationService {
  /**
   * 分配订单资金
   */
  static async allocateOrderFunds(orderId, subOrderStore, orderType, totalAmount, connection = null) {
    try {
      // 获取订单信息
      let orderInfo;
      if (orderType === 'customer') {
        const orderQuery = 'SELECT * FROM customer_orders WHERE id = ?';
        let orderResult;
        if (connection) {
          [orderResult] = await connection.execute(orderQuery, [orderId]);
        } else {
          orderResult = await db.query(orderQuery, [orderId]);
        }
        if (orderResult.length === 0) {
          throw new Error('订单不存在');
        }
        orderInfo = orderResult[0];
      } else {
        const orderQuery = 'SELECT * FROM store_orders WHERE id = ?';
        let orderResult;
        if (connection) {
          [orderResult] = await connection.execute(orderQuery, [orderId]);
        } else {
          orderResult = await db.query(orderQuery, [orderId]);
        }
        if (orderResult.length === 0) {
          throw new Error('订单不存在');
        }
        orderInfo = orderResult[0];
      }

      const amount = parseFloat(totalAmount || orderInfo.total_amount || 0);
      
      // 根据订单类型进行不同的资金分配
      if (orderType === 'customer') {
        await this.allocateCustomerOrderFunds(orderId, subOrderStore, orderInfo, amount, connection);
      } else if (orderType === 'purchase') {
        await this.allocatePurchaseOrderFunds(orderId, orderInfo, amount, connection);
      } else if (orderType === 'transfer') {
        await this.allocateTransferOrderFunds(orderId, orderInfo, amount, connection);
      }
      
    } catch (error) {
      console.error('资金分配失败:', error);
      throw error;
    }
  }

  /**
   * 分配顾客订单资金
   * 根据业务文档实现正确的资金分配逻辑
   */
  static async allocateCustomerOrderFunds(orderId, subOrderStore, orderInfo, amount, connection = null) {
    // 获取用户信息
    const userQuery = 'SELECT * FROM users WHERE user_id = ?';
    let userResult;
    if (connection) {
      [userResult] = await connection.execute(userQuery, [orderInfo.user_id]);
    } else {
      userResult = await db.query(userQuery, [orderInfo.user_id]);
    }
    if (userResult.length === 0) {
      throw new Error('用户不存在');
    }
    const user = userResult[0];

    // 获取子订单商品信息，用于计算门店成本和销售人佣金
    const subOrderQuery = `
      SELECT coi.*, p.store_price
      FROM customer_order_items coi 
      JOIN customer_sub_orders cso ON coi.sub_order_id = cso.id 
      JOIN products p ON coi.product_id = p.id 
      WHERE cso.main_order_id = ? AND cso.store_no = ?
    `;
    let subOrderItems;
    if (connection) {
      [subOrderItems] = await connection.execute(subOrderQuery, [orderId, subOrderStore]);
    } else {
      subOrderItems = await db.query(subOrderQuery, [orderId, subOrderStore]);
    }
    
    let storeCost = 0;
    let salesmanCommission = 0;
    
    // 计算门店成本和销售人佣金
    for (const item of subOrderItems) {
      const purchasePrice = parseFloat(item.store_price || 0); // 采购基准价
      const salePrice = parseFloat(item.product_price || 0); // 销售价（使用订单商品表中的实际销售价）
      const quantity = parseInt(item.quantity || 0);
      
      // 门店成本 = 门店采购价 * 数量（这里使用采购基准价作为门店采购价）
      storeCost += purchasePrice * quantity;
      
      // 销售人佣金计算
      if (user.salesman_id) {
        const profit = (salePrice - purchasePrice) * quantity;
        if (subOrderStore && subOrderStore !== 'platform') {
          // 归属门店的订单：佣金 = (销售价-采购基准价) * 70%
          salesmanCommission += profit * 0.7;
        } else {
          // 归属平台总部的订单：佣金 = (销售价-采购基准价) * 30%
          salesmanCommission += profit * 0.3;
        }
      }
    }
    
    // 记录销售人佣金到待结分佣（订单创建时）
    if (salesmanCommission > 0 && user.salesman_id) {
      await this.recordPendingCommission(user.salesman_id, salesmanCommission, orderId, subOrderStore || 'platform', '销售人佣金-待结算', connection);
    }

    // 根据业务文档：只有归属门店的订单才有门店成本和门店利润
    if (subOrderStore && subOrderStore !== 'platform') {
      // 记录门店成本到待结算（订单创建时）
      if (storeCost > 0) {
        await this.recordPendingStoreCost(subOrderStore, storeCost, orderId, '门店股本金-待结算', connection);
      }

      // 门店利润 = 订单金额 - 门店成本 - 销售人佣金
      const storeProfit = amount - storeCost - salesmanCommission;
      if (storeProfit > 0) {
        await this.recordFundAllocation(orderId, subOrderStore, 'store_profit', storeProfit, 'store', subOrderStore, '门店利润池-销售利润', connection);
      }
    } else {
      // 归属平台总部的订单，剩余金额归平台
      const platformIncome = amount - salesmanCommission;
      if (platformIncome > 0) {
        await this.recordFundAllocation(orderId, 'platform', 'platform_income', platformIncome, 'platform', 'platform', '平台收入', connection);
      }
    }
  }

  /**
   * 分配采购订单资金
   */
  static async allocatePurchaseOrderFunds(orderId, orderInfo, amount, connection = null) {
    // 采购订单：全部金额作为采购成本
    await this.recordFundAllocation(orderId, 'platform', 'purchase_cost', amount, 'platform', 'platform', '采购成本', connection);
  }

  /**
   * 分配移库订单资金
   */
  static async allocateTransferOrderFunds(orderId, orderInfo, amount, connection = null) {
    // 移库订单：全部金额作为移库成本
    await this.recordFundAllocation(orderId, 'platform', 'transfer_cost', amount, 'platform', 'platform', '移库成本', connection);
  }

  /**
   * 计算门店利润
   */
  static async calculateStoreProfit(storeId, orderAmount) {
    if (!storeId) return 0;
    
    // 获取门店信息
    const storeInfo = await this.getStoreInfo(storeId);
    if (!storeInfo) return 0;
    
    // 门店利润比例，默认10%
    const profitRate = parseFloat(storeInfo.profit_rate || 0.1);
    return orderAmount * profitRate;
  }

  /**
   * 计算销售员佣金
   */
  static async calculateSalesmanCommission(salesmanId, orderAmount) {
    if (!salesmanId) return 0;
    
    // 获取销售员信息
    const salesmanQuery = 'SELECT commission_rate FROM users WHERE id = ? AND role = "salesman"';
    const salesmanResult = await db.query(salesmanQuery, [salesmanId]);
    if (salesmanResult.length === 0) return 0;
    
    // 销售员佣金比例，默认3%
    const commissionRate = parseFloat(salesmanResult[0].commission_rate || 0.03);
    return orderAmount * commissionRate;
  }

  /**
   * 计算推荐人佣金
   */
  static async calculateReferrerCommission(referrerId, orderAmount) {
    if (!referrerId) return 0;
    
    // 获取推荐人信息
    const referrerQuery = 'SELECT referral_commission_rate FROM users WHERE id = ?';
    const referrerResult = await db.query(referrerQuery, [referrerId]);
    if (referrerResult.length === 0) return 0;
    
    // 推荐人佣金比例，默认2%
    const commissionRate = parseFloat(referrerResult[0].referral_commission_rate || 0.02);
    return orderAmount * commissionRate;
  }

  /**
   * 获取门店信息
   */
  static async getStoreInfo(storeId) {
    const storeQuery = 'SELECT * FROM stores WHERE id = ?';
    const storeResult = await db.query(storeQuery, [storeId]);
    return storeResult.length > 0 ? storeResult[0] : null;
  }

  /**
   * 记录待结销售分佣
   */
  static async recordPendingCommission(salesmanId, amount, orderId, subOrderStore, description, connection = null) {
    // 更新销售人的待结分佣金额
    const dbConnection = connection || db;
    
    if (connection) {
      await connection.execute(
        'UPDATE user_fund_accounts SET pending_commission = pending_commission + ?, updated_at = ? WHERE user_id = ?',
        [amount, Date.now(), salesmanId]
      );

      // 记录分佣明细
      await connection.execute(
        'INSERT INTO fund_allocations (order_id, sub_order_store, allocation_type, amount, recipient_type, recipient_id, description, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [orderId, subOrderStore || 'platform', '待结分佣', amount, 'user', salesmanId, description, '待处理']
      );
    } else {
      // 如果没有传入连接，抛出错误，强制使用事务连接
      throw new Error('recordPendingCommission must be called with a database connection to avoid lock conflicts');
    }
  }

  /**
   * 记录待结门店成本
   */
  static async recordPendingStoreCost(storeId, amount, orderId, description, connection = null) {
    // 记录门店成本明细
    if (connection) {
      await connection.execute(
        'INSERT INTO fund_allocations (order_id, sub_order_store, allocation_type, amount, recipient_type, recipient_id, description, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [orderId, storeId || 'platform', '待结门店成本', amount, 'store', storeId, description, '待处理']
      );
    } else {
      // 如果没有传入连接，抛出错误，强制使用事务连接
      throw new Error('recordPendingStoreCost must be called with a database connection to avoid lock conflicts');
    }
  }

  /**
   * 处理订单完成 - 将待结分佣转入账户余额
   */
  static async processOrderCompletion(orderId) {
    // 查询该订单的待结分佣记录
    const pendingCommissions = await db.query(
      'SELECT * FROM fund_allocations WHERE order_id = ? AND allocation_type = "待结分佣" AND status = "待处理"',
      [orderId]
    );
    
    for (const commission of pendingCommissions) {
      const salesmanId = commission.recipient_id;
      const amount = parseFloat(commission.amount);
      
      // 从待结分佣转入账户余额
      await db.query(
        'UPDATE user_fund_accounts SET pending_commission = pending_commission - ?, account_balance = account_balance + ?, total_commission = total_commission + ?, updated_at = ? WHERE user_id = ?',
        [amount, amount, amount, Date.now(), salesmanId]
      );
      
      // 更新分佣记录状态
      await db.query(
        'UPDATE fund_allocations SET status = "已完成" WHERE id = ?',
        [commission.id]
      );
      
      // 记录资金变动
      await db.query(
        'INSERT INTO user_fund_records (user_id, type, amount, description, order_id, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)',
        [salesmanId, 'commission', amount, '销售分佣到账', orderId, '已完成', Date.now()]
      );
    }

    // 查询该订单的待结门店成本记录
    const pendingStoreCosts = await db.query(
      'SELECT * FROM fund_allocations WHERE order_id = ? AND allocation_type = "待结门店成本" AND status = "待处理"',
      [orderId]
    );
    
    for (const storeCost of pendingStoreCosts) {
      const storeId = storeCost.recipient_id;
      const amount = parseFloat(storeCost.amount);
      
      // 更新门店成本记录状态
      await db.query(
        'UPDATE fund_allocations SET status = "已完成" WHERE id = ?',
        [storeCost.id]
      );
      
      // 记录门店资金变动
      await db.query(
        'INSERT INTO store_fund_records (store_no, type, account_type, amount, description, created_at) VALUES (?, ?, ?, ?, ?, ?)',
        [storeId, 'cost', 'cost', amount, '门店成本结算', new Date()]
      );
    }
  }

  /**
   * 记录资金分配
   */
  static async recordFundAllocation(orderId, subOrderStore, allocationType, amount, recipientType, recipientId, description, connection = null, status = '已完成') {
    const insertQuery = `
      INSERT INTO fund_allocations
      (order_id, sub_order_store, allocation_type, amount, recipient_type, recipient_id, description, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    // 确保sub_order_store不为null，如果为null则使用'platform'
    const storeValue = subOrderStore || 'platform';

    if (connection) {
      await connection.execute(insertQuery, [
        orderId,
        storeValue,
        allocationType,
        amount,
        recipientType,
        recipientId || '',
        description,
        status
      ]);
    } else {
      // 如果没有传入连接，抛出错误，强制使用事务连接
      throw new Error('recordFundAllocation must be called with a database connection to avoid lock conflicts');
    }
  }

  /**
   * 获取订单资金分配记录
   */
  static async getOrderFundAllocations(orderId) {
    const query = 'SELECT * FROM fund_allocations WHERE order_id = ? ORDER BY created_at DESC';
    return await db.query(query, [orderId]);
  }

  /**
   * 获取门店资金分配统计
   */
  static async getStoreFundStats(storeId, startDate = null, endDate = null) {
    let query = `
      SELECT 
        allocation_type,
        SUM(amount) as total_amount,
        COUNT(*) as count
      FROM fund_allocations 
      WHERE recipient_type = 'store' AND recipient_id = ?
    `;
    const params = [storeId];
    
    if (startDate && endDate) {
      query += ' AND created_at BETWEEN ? AND ?';
      params.push(startDate, endDate);
    }
    
    query += ' GROUP BY allocation_type';
    
    return await db.query(query, params);
  }

  /**
   * 获取用户佣金统计
   */
  static async getUserCommissionStats(userId, startDate = null, endDate = null) {
    let query = `
      SELECT 
        allocation_type,
        SUM(amount) as total_amount,
        COUNT(*) as count
      FROM fund_allocations 
      WHERE recipient_type = 'user' AND recipient_id = ?
    `;
    const params = [userId];
    
    if (startDate && endDate) {
      query += ' AND created_at BETWEEN ? AND ?';
      params.push(startDate, endDate);
    }
    
    query += ' GROUP BY allocation_type';
    
    return await db.query(query, params);
  }
}

module.exports = FundAllocationService;