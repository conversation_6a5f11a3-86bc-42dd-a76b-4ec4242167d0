<!--partner/store-funds/store-funds.wxml-->
<!--合伙人端门店资金页面-->
<view class="store-funds-container">
  <!-- 门店选择区域 -->
  <view class="store-select-section">
    <view class="store-select-header">
      <view class="section-title">当前门店</view>
      <view class="store-select-container">
        <picker class="store-picker" 
                mode="selector" 
                range="{{storeList}}" 
                range-key="name" 
                bindchange="onStoreChange" 
                wx:if="{{storeList.length > 0}}">
          <view class="store-selector">
            <text class="store-name">{{selectedStore.name}}（{{selectedStore.store_no}}）</text>
            <view class="dropdown-arrow">▼</view>
          </view>
        </picker>
        <view class="store-selector" wx:else>
          <text class="store-name">暂无门店数据</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 门店账户统计数据 -->
  <view class="fund-stats-section" wx:if="{{selectedStore}}">
    <view class="stats-header">
      <text class="stats-title">门店账户统计</text>
    </view>
    <view class="stats-grid">
      <view class="stats-item" bindtap="onAccountTypeChange" data-type="capital">
        <view class="stats-label">门店股本金</view>
        <view class="stats-value">¥{{fundStats.capital || '0.00'}}</view>
      </view>
      <view class="stats-item" bindtap="onAccountTypeChange" data-type="dividend">
        <view class="stats-label">门店分红</view>
        <view class="stats-value">¥{{fundStats.dividend || '0.00'}}</view>
      </view>
      <view class="stats-item" bindtap="onAccountTypeChange" data-type="fund">
        <view class="stats-label">门店公积金</view>
        <view class="stats-value">¥{{fundStats.fund || '0.00'}}</view>
      </view>
    </view>
  </view>

  <!-- 资金记录标签栏 -->
  <view class="record-tabs-section" wx:if="{{selectedStore}}">
    <view class="tabs-container">
      <view class="tab-item {{currentTab === 'all' ? 'active' : ''}}" 
            bindtap="onTabChange" 
            data-tab="all">
        全部记录
      </view>
      <view class="tab-item {{currentTab === 'capital' ? 'active' : ''}}" 
            bindtap="onTabChange" 
            data-tab="capital">
        股本金
      </view>
      <view class="tab-item {{currentTab === 'dividend' ? 'active' : ''}}" 
            bindtap="onTabChange" 
            data-tab="dividend">
        分红
      </view>
      <view class="tab-item {{currentTab === 'fund' ? 'active' : ''}}" 
            bindtap="onTabChange" 
            data-tab="fund">
        公积金
      </view>
    </view>
  </view>

  <!-- 资金记录列表 -->
  <view class="records-section" wx:if="{{selectedStore}}">
    <view class="records-list">
      <view class="record-item" wx:for="{{fundRecords}}" wx:key="id">
        <view class="record-header">
          <view class="record-voucher">{{item.voucher_no}}</view>
          <view class="record-time">{{item.created_at_formatted}}</view>
        </view>
        <view class="record-content">
          <view class="record-description">{{item.description}}</view>
          <view class="record-amount {{item.amount > 0 ? 'positive' : 'negative'}}">
            {{item.amount > 0 ? '+' : ''}}¥{{item.amount}}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && fundRecords.length === 0}}">
      <image class="empty-icon" src="/images/icons2/空状态.svg"></image>
      <text class="empty-text">暂无资金记录</text>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{!loading && hasMore}}">
      <text class="load-more-text">上拉加载更多</text>
    </view>
    
    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!loading && !hasMore && fundRecords.length > 0}}">
      <text class="no-more-text">没有更多记录了</text>
    </view>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-spacing"></view>
</view>