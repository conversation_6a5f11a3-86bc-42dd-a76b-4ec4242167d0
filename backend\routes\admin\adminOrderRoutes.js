const express = require('express');
const router = express.Router();
const AdminOrderController = require('../../controllers/admin/adminOrderController');

/**
 * 管理端订单路由
 * 基础路径: /api/admin/orders
 */

// 获取所有订单列表（管理端）
router.get('/', AdminOrderController.getAllOrders);

// 获取订单详情（管理端）
router.get('/:orderId', AdminOrderController.getOrderDetail);

// 更新订单状态（管理端）
router.put('/:orderId/status', AdminOrderController.updateOrderStatus);

// 处理退款申请（管理端）
router.post('/:orderId/refund', AdminOrderController.processRefund);

// 审核采购订单
router.post('/:orderId/approve', AdminOrderController.approveOrder);

// 拒绝采购订单
router.post('/:orderId/reject', AdminOrderController.rejectOrder);

// 发货订单
router.post('/:orderId/ship', AdminOrderController.shipOrder);

// 管理端确认收货（替代顾客操作）
router.post('/:orderId/confirm-receipt', AdminOrderController.confirmReceipt);

// 获取订单统计数据
router.get('/statistics', AdminOrderController.getOrderStatistics);

module.exports = router;