/* pages/product/detail.wxss */
.container {
  padding-bottom: 100px; /* 为底部操作栏留出空间 */
  position: relative;
}

/* 加载中 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading image {
  width: 60px;
  height: 60px;
  margin-bottom: 10px;
}

.loading text {
  font-size: 14px;
  color: #999999;
}



/* 轮播图 */
.product-swiper {
  width: 100%;
  height: 350px;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

/* 商品信息 */
.product-info {
  padding: 15px;
  background-color: #FFFFFF;
  margin-bottom: 10px;
}

.product-price-row {
  display: flex;
  align-items: baseline;
  margin-bottom: 10px;
}

.product-price {
  font-size: 24px;
  color: #FF4D4F;
  font-weight: bold;
}

.product-original-price {
  font-size: 14px;
  color: #999999;
  text-decoration: line-through;
  margin-left: 10px;
}

.product-name {
  font-size: 16px;
  color: #333333;
  margin-bottom: 10px;
  line-height: 1.4;
}

.product-sales {
  font-size: 12px;
  color: #999999;
}

/* 合伙人端采购价样式 */
.purchase-price-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: #f0f8ff;
  border-radius: 6px;
  border-left: 3px solid #5698c3;
}

.purchase-price-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.purchase-price {
  font-size: 18px;
  color: #5698c3;
  font-weight: bold;
}

/* 商品描述 */
.product-description {
  padding: 15px;
  background-color: #FFFFFF;
  margin-bottom: 10px;
}

.section-title {
  font-size: 16px;
  color: #333333;
  font-weight: bold;
  margin-bottom: 10px;
  position: relative;
  padding-left: 10px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 2px;
  width: 4px;
  height: 16px;
  background-color: #1890FF;
  border-radius: 2px;
}

.description-content {
  font-size: 14px;
  color: #666666;
  line-height: 1.6;
}

/* 店铺信息 */
.shop-info {
  padding: 15px;
  background-color: #FFFFFF;
  margin-bottom: 10px;
}

.shop-row {
  display: flex;
  align-items: center;
}

.shop-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.shop-name {
  flex: 1;
  font-size: 14px;
  color: #333333;
}

.shop-btn {
  padding: 5px 10px;
  background-color: #F5F5F5;
  color: #666666;
  font-size: 12px;
  border-radius: 15px;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.cart-btn {
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.cart-btn image {
  width: 24px;
  height: 24px;
}

.cart-badge {
  position: absolute;
  top: 5px;
  right: 5px;
  min-width: 16px;
  height: 16px;
  background-color: #FF4D4F;
  color: #FFFFFF;
  font-size: 10px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 4px;
}

.action-btn {
  flex: 1;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
}

.add-to-cart {
  background-color: #FFF2E8;
  color: #FF4D4F;
}

.buy-now {
  background-color: #FF4D4F;
  color: #FFFFFF;
}

/* 商品不存在 */
.not-found {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  padding: 0 30px;
}

.not-found text {
  font-size: 16px;
  color: #999999;
  margin-bottom: 30px;
}

.back-button {
  width: 200px;
  height: 40px;
  line-height: 40px;
  background-color: #1890FF;
  color: #FFFFFF;
  font-size: 14px;
  border-radius: 20px;
}
