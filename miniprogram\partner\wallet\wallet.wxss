/* partner/wallet/wallet.wxss */
/* 合伙人端我的钱包页面样式 */

.wallet-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 20rpx;
}

/* 加载中 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #5698c3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 余额卡片区 */
.balance-section {
  margin: 20rpx;
}

.balance-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.balance-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.balance-icon {
  margin-right: 16rpx;
}

.balance-icon .icon {
  width: 40rpx;
  height: 40rpx;
  filter: none;
}

.balance-content {
  flex: 1;
}

.balance-label {
  font-size: 28rpx;
  color: #5698c3;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.balance-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.balance-actions {
  display: flex;
  gap: 20rpx;
}

/* 统计数据区 */
.stats-section {
  margin: 20rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stats-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.stats-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #5698c3;
  font-weight: 500;
}
/* 操作按钮样式 */

.action-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 35rpx;
  border: none;
  font-size: 26rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.withdraw-btn {
  background: #5698c3;
  color: #fff;
}

.recharge-btn {
  background: #5698c3;
  color: #fff;
}

.btn-icon {
  width: 28rpx;
  height: 28rpx;
  filter: brightness(0) invert(1);
}

/* 明细区 */
.details-section {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.details-header {
  margin-bottom: 30rpx;
}

.details-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 标签栏 */
.tabs-container {
  margin-bottom: 30rpx;
}

.tabs-bar {
  display: flex;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 4rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: #5698c3;
  color: #fff;
}

.tab-name {
  font-size: 26rpx;
  font-weight: 500;
}

/* 明细记录列表 */
.records-container {
  min-height: 200rpx;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-left {
  flex: 1;
  margin-right: 20rpx;
}

.record-desc {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.record-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.record-extra {
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
}

.extra-label {
  margin-right: 8rpx;
}

.extra-value {
  color: #5698c3;
}

.record-right {
  text-align: right;
  flex-shrink: 0;
}

.record-amount {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.record-amount.positive {
  color: #07c160;
}

.record-amount.negative {
  color: #5698c3;
}

.record-balance {
  font-size: 24rpx;
  color: #666;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

.load-more .loading-spinner {
  width: 30rpx;
  height: 30rpx;
  margin-right: 20rpx;
}

/* 没有更多数据 */
.no-more {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #999;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .stats-grid {
    gap: 16rpx;
  }
  
  .stats-card {
    padding: 20rpx;
    min-height: 100rpx;
  }
  
  .card-value {
    font-size: 24rpx;
  }
  
  .card-label {
    font-size: 20rpx;
  }
}