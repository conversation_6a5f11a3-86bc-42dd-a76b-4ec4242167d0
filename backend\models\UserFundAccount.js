/**
 * 用户资金账户模型
 */
const db = require('../config/db');

class UserFundAccount {
  /**
   * 获取或创建用户资金账户
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 用户资金账户信息
   */
  static async getOrCreate(userId) {
    try {
    const now = Date.now();
    
    // 检查用户资金账户是否存在
    const account = await db.query('SELECT * FROM user_fund_accounts WHERE user_id = ?', [userId]);
    
    if (account.length === 0) {
      // 创建新的资金账户
      await db.query(
        'INSERT INTO user_fund_accounts (user_id, account_balance, pending_commission, total_commission, total_withdrawal, total_dividend, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [userId, 0.00, 0.00, 0.00, 0.00, 0.00, now, now]
      );
      
      return {
        user_id: userId,
        account_balance: 0.00,
        pending_commission: 0.00,
        total_commission: 0.00,
        total_withdrawal: 0.00,
        total_dividend: 0.00,
        created_at: now,
        updated_at: now
      };
    }
    
    return account[0];
    } catch (error) {
      console.error('getOrCreate 错误:', error);
      throw error;
    }
  }

  /**
   * 更新用户资金账户
   * @param {string} userId 用户ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<Object>} 更新后的账户信息
   */
  static async update(userId, updateData) {
    const now = Date.now();
    updateData.updated_at = now;
    
    const fields = Object.keys(updateData);
    const values = Object.values(updateData);
    const placeholders = fields.map(() => '?').join(', ');
    
    const sql = `UPDATE user_fund_accounts SET ${fields.map(field => `${field} = ?`).join(', ')} WHERE user_id = ?`;
    await db.query(sql, [...values, userId]);
    
    return await this.getOrCreate(userId);
  }

  /**
   * 添加资金变动记录
   * @param {Object} recordData 记录数据
   * @returns {Promise<Object>} 创建结果
   */
  static async addFundRecord(recordData) {
    const now = Date.now();
    const sql = `INSERT INTO user_fund_records (user_id, type, amount, balance, description, order_id, store_no, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    const params = [
      recordData.user_id,
      recordData.type,
      recordData.amount,
      recordData.balance,
      recordData.description || '',
      recordData.order_id || null,
      recordData.store_no || null,
      recordData.status || '已完成',
      now
    ];
    
    const result = await db.query(sql, params);
    return { success: true, id: result.insertId };
  }

  /**
   * 获取用户资金变动记录
   * @param {string} userId 用户ID
   * @param {number} limit 限制数量
   * @param {number} offset 偏移量
   * @returns {Promise<Array>} 资金变动记录列表
   */
  static async getFundRecords(userId, limit = 20, offset = 0) {
    const sql = `SELECT * FROM user_fund_records WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?`;
    return await db.query(sql, [userId, limit, offset]);
  }

  /**
   * 获取用户推荐统计
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 推荐统计信息
   */
  static async getReferralStats(userId) {
    try {
    const stats = await db.query('SELECT * FROM user_referral_stats WHERE user_id = ?', [userId]);
    
    if (stats.length === 0) {
      // 创建新的推荐统计记录
      const now = Date.now();
      await db.query(
        'INSERT INTO user_referral_stats (user_id, referral_count, store_count, created_at, updated_at) VALUES (?, ?, ?, ?, ?)',
        [userId, 0, 0, now, now]
      );
      
      return {
        user_id: userId,
        referral_count: 0,
        store_count: 0,
        created_at: now,
        updated_at: now
      };
    }
    
    return stats[0];
    } catch (error) {
      console.error('getReferralStats 错误:', error);
      throw error;
    }
  }

  /**
   * 更新用户推荐统计
   * @param {string} userId 用户ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<Object>} 更新后的统计信息
   */
  static async updateReferralStats(userId, updateData) {
    try {
    const now = Date.now();
    updateData.updated_at = now;
    
    const fields = Object.keys(updateData);
    const values = Object.values(updateData);
    
    const sql = `UPDATE user_referral_stats SET ${fields.map(field => `${field} = ?`).join(', ')} WHERE user_id = ?`;
    await db.query(sql, [...values, userId]);
    
    return await this.getReferralStats(userId);
    } catch (error) {
      console.error('更新推荐统计失败:', error);
      // 如果更新失败，返回当前统计信息而不抛出错误
      return await this.getReferralStats(userId);
    }
  }

  /**
   * 计算用户推荐人数（统计推荐人和销售人，去重后的总数）
   * @param {string} userId 用户ID
   * @returns {Promise<number>} 推荐人数
   */
  static async calculateReferralCount(userId) {
    try {
      console.log('计算推荐人数，用户ID:', userId);
      
      // 根据业务文档：统计推荐人是当前用户的用户和销售人是当前用户的用户，去重后的总数
      const result = await db.query(`
        SELECT COUNT(DISTINCT user_id) as count 
        FROM (
          SELECT user_id FROM users WHERE referrerId = ?
          UNION
          SELECT user_id FROM users WHERE salesman_id = ?
        ) as combined_referrals
      `, [userId, userId]);
      
      console.log('推荐人数查询结果:', result);
      return result[0].count || 0;
    } catch (error) {
      console.error('计算推荐人数失败:', error);
      // 如果查询失败，返回0
      return 0;
    }
  }

  /**
   * 计算用户门店数量
   * @param {string} userId 用户ID
   * @returns {Promise<number>} 门店数量
   */
  static async calculateStoreCount(userId) {
    try {
      console.log('计算门店数量，用户ID:', userId);
    const result = await db.query('SELECT COUNT(DISTINCT store_no) as count FROM partners WHERE user_id = ?', [userId]);
      console.log('门店数量查询结果:', result);
    return result[0].count || 0;
    } catch (error) {
      console.error('计算门店数量失败:', error);
      // 如果查询失败，返回0
      return 0;
    }
  }

  /**
   * 获取合伙人完整统计信息
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 合伙人统计信息
   */
  static async getPartnerStats(userId) {
    try {
      console.log('获取合伙人统计信息，用户ID:', userId);
      
    // 获取资金账户信息
    const fundAccount = await this.getOrCreate(userId);
      console.log('资金账户信息:', fundAccount);
    
    // 获取推荐统计信息
    const referralStats = await this.getReferralStats(userId);
      console.log('推荐统计信息:', referralStats);
    
    // 计算实时推荐人数和门店数量
    const referralCount = await this.calculateReferralCount(userId);
    const storeCount = await this.calculateStoreCount(userId);
      console.log('实时统计 - 推荐人数:', referralCount, '门店数量:', storeCount);
    
    // 更新推荐统计
    await this.updateReferralStats(userId, {
      referral_count: referralCount,
      store_count: storeCount
    });
    
    return {
      fundAccount,
      referralStats: {
        ...referralStats,
        referral_count: referralCount,
        store_count: storeCount
      }
    };
    } catch (error) {
      console.error('getPartnerStats 错误:', error);
      // 返回默认值而不是抛出错误
      return {
        fundAccount: {
          user_id: userId,
          account_balance: 0.00,
          pending_commission: 0.00,
          total_commission: 0.00,
          total_withdrawal: 0.00,
          total_dividend: 0.00
        },
        referralStats: {
          user_id: userId,
          referral_count: 0,
          store_count: 0
        }
      };
    }
  }
}

module.exports = UserFundAccount;