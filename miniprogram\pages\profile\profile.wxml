<!--pages/profile/profile.wxml-->
<view class="profile-container">
  <!-- 顶部个人信息 -->
  <view class="profile-header">
    <view class="user-info-row" wx:if="{{isLogin}}">
      <image class="avatar-large" src="{{userInfo.avatar || userInfo.avatarUrl || '../../images/icons2/男头像.png'}}"></image>
      <view class="user-info-content user-info-flex">
        <view class="user-info-main-row user-info-align-center">
          <view class="user-name">{{userInfo.nickname || userInfo.nickName || '用户'}}</view>
          <view class="switch-badge-group">
            <view class="vip-badge" wx:if="{{isLogin}}">
              <text bindtap="onIdentityLabelTap" style="cursor:pointer;">{{currentRole.role_name || identityLabel || '普通用户'}}</text>
              <image class="vip-icon" src="../../images/icons/vip.png" wx:if="{{(currentRole.role_name || identityLabel) && (currentRole.role_name || identityLabel) != '普通用户'}}"></image>
            </view>
            <image class="switch-role-icon" wx:if="{{showRoleSwitch}}" src="../../images/icons2/switch-role.png" mode="aspectFit" bindtap="onGoSwitchLoginPage"/>
          </view>
        </view>
        <view class="user-id user-id-bottom">ID号：{{userInfo.id || userInfo._id || ''}}</view>
      </view>
    </view>

    <view class="login-prompt" wx:else>
      <image class="avatar-large" src="../../images/icons2/男头像.png"></image>
      <view class="login-text">登录后查看个人信息</view>
      <view class="login-btn" bindtap="goToLogin">立即登录</view>
    </view>

    <!-- 钱包统计 -->
    <view class="wallet-section">
      <view class="wallet-header">
        <view class="wallet-title">我的钱包</view>
        <view class="wallet-detail" bindtap="viewWalletDetail">
          <text class="wallet-detail-text">钱包详情</text>
          <text class="wallet-detail-arrow">></text>
        </view>
      </view>
      <view class="wallet-stats">
        <view class="wallet-item" bindtap="viewBalance">
          <view class="wallet-num">{{isLogin ? (wallet.balance || 0) : 0}}</view>
          <view class="wallet-label">余额</view>
        </view>
        <view class="wallet-item" bindtap="viewRedPacket">
          <view class="wallet-num">{{isLogin ? (wallet.redPacket || 0) : 0}}</view>
          <view class="wallet-label">红包</view>
        </view>
        <view class="wallet-item" bindtap="viewCoupon">
          <view class="wallet-num">{{isLogin ? (wallet.coupon || 0) : 0}}</view>
          <view class="wallet-label">卡券</view>
        </view>
        <view class="wallet-item" bindtap="viewPoints">
          <view class="wallet-num">{{isLogin ? (wallet.points || 0) : 0}}</view>
          <view class="wallet-label">积分</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 轮播图 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}" indicator-color="rgba(255, 255, 255, 0.6)" indicator-active-color="#1E6A9E">
      <block wx:if="{{banners && banners.length > 0}}">
        <swiper-item wx:for="{{banners}}" wx:key="id" bindtap="onBannerTap" data-id="{{item.id}}" data-url="{{item.linkUrl}}" data-link-type="{{item.linkType}}">
          <image class="banner-image" src="{{item.imageUrl}}" mode="aspectFill" lazy-load="{{false}}"></image>
          <view class="banner-title" wx:if="{{item.title}}">{{item.title}}</view>
        </swiper-item>
      </block>
      <block wx:else>
        <!-- 默认轮播图内容 -->
        <swiper-item>
          <image class="banner-image" src="/images/lunbo/001.jpeg" mode="aspectFill"></image>
          <view class="banner-title">企业服务专场</view>
        </swiper-item>
        <swiper-item>
          <image class="banner-image" src="/images/lunbo/002.jpg" mode="aspectFill"></image>
          <view class="banner-title">知识产权保护与商标注册</view>
        </swiper-item>
        <swiper-item>
          <image class="banner-image" src="/images/lunbo/003.png" mode="aspectFill"></image>
          <view class="banner-title">企业并购与资产重组</view>
        </swiper-item>
      </block>
    </swiper>
  </view>

  <!-- 订单管理 -->
  <view class="content-card">
    <view class="order-header">
      <view class="order-title">我的订单</view>
      <view class="view-all" bindtap="viewAllOrders">
        <text class="view-all-text">全部订单</text>
        <text class="view-all-arrow">></text>
      </view>
    </view>

    <view class="order-grid">
      <view class="order-item" bindtap="viewUnpaidOrders">
        <image class="order-icon" src="/images/icons/order-unpaid.svg"></image>
        <view class="order-name">待付款</view>
        <view class="order-badge" wx:if="{{stats.unpaidCount > 0}}">{{stats.unpaidCount}}</view>
      </view>
      <view class="order-item" bindtap="viewPendingPickupOrders">
        <image class="order-icon" src="/images/icons/order-pickup.svg"></image>
        <view class="order-name">待自提</view>
        <view class="order-badge" wx:if="{{stats.pendingPickupCount > 0}}">{{stats.pendingPickupCount}}</view>
      </view>
      <view class="order-item" bindtap="viewUnreceivedOrders">
        <image class="order-icon" src="/images/icons/order-unreceived.svg"></image>
        <view class="order-name">待收货</view>
        <view class="order-badge" wx:if="{{stats.unreceivedCount > 0}}">{{stats.unreceivedCount}}</view>
      </view>
      <view class="order-item" bindtap="viewCompletedOrders">
        <image class="order-icon" src="/images/icons/order-completed.svg"></image>
        <view class="order-name">已完成</view>
        <view class="order-badge" wx:if="{{stats.completedCount > 0}}">{{stats.completedCount}}</view>
      </view>
      <view class="order-item" bindtap="viewUnratedOrders">
        <image class="order-icon" src="/images/icons/order-return.svg"></image>
        <view class="order-name">退换货</view>
        <view class="order-badge" wx:if="{{stats.unratedCount > 0}}">{{stats.unratedCount}}</view>
      </view>
    </view>

  </view>

  <!-- 分隔线 -->
  <view class="divider"></view>

  <!-- 其他功能 -->
  <view class="other-functions">
    <view class="other-title">其他功能</view>

    <view class="function-grid">
      <view class="function-item" bindtap="getRedPacket">
        <image class="function-icon" src="/images/icons/redpack.svg"></image>
        <view class="function-name">领取红包</view>
      </view>

      <view class="function-item" bindtap="goToVipCenter">
        <image class="function-icon" src="/images/icons/vip.svg"></image>
        <view class="function-name">会员中心</view>
      </view>

      <view class="function-item" bindtap="viewPoints">
        <image class="function-icon" src="/images/icons/points.svg"></image>
        <view class="function-name">积分中心</view>
      </view>

      <view class="function-item" bindtap="shareStore">
        <image class="function-icon" src="/images/icons/share_new.svg"></image>
        <view class="function-name">分享本店</view>
      </view>

      <view class="function-item" bindtap="joinPartner">
        <image class="function-icon" src="/images/icons/partner.svg"></image>
        <view class="function-name">加入合伙人</view>
      </view>

      <view class="function-item" bindtap="viewAddress">
        <image class="function-icon" src="/images/icons/address.svg"></image>
        <view class="function-name">收货地址</view>
      </view>

      <view class="function-item" bindtap="viewFavorites">
        <image class="function-icon" src="/images/icons/favorite.svg"></image>
        <view class="function-name">我的收藏</view>
      </view>

      <view class="function-item" bindtap="goToSettings">
        <image class="function-icon" src="/images/icons/settings.svg"></image>
        <view class="function-name">账号设置</view>
      </view>

      <view class="function-item" bindtap="goToAbout">
        <image class="function-icon" src="/images/icons/about.svg"></image>
        <view class="function-name">关于我们</view>
      </view>

      <view class="function-item" bindtap="contactService">
        <image class="function-icon" src="/images/icons/service.svg"></image>
        <view class="function-name">在线客服</view>
      </view>

      <view class="function-item" bindtap="viewFAQ">
        <image class="function-icon" src="/images/icons/faq.svg"></image>
        <view class="function-name">常见问题</view>
      </view>
    </view>
  </view>

  <!-- 开发者选项已移除 -->

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>

  <!-- 开发者选项触发区域已移除 -->
</view>
