/* partner/publish/publish.wxss */
.shelf-container {
  min-height: 100vh;
  background-color: #F7F7F7;
  padding-bottom: 258rpx; /* 为结算栏(120rpx)和底部导航(138rpx)留出空间 */
  padding-top: 0;
}

/* 标签页样式 - 连体划扣样式 */
.tab-header {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 0 20rpx;
  justify-content: center; /* 往中间靠近 */
}

.tab-item {
  flex: 0 0 auto; /* 不伸缩，保持固定宽度 */
  text-align: center;
  padding: 30rpx 40rpx; /* 增加左右内边距 */
  font-size: 32rpx;
  color: #666;
  position: relative;
  transition: all 0.3s;
  margin: 0 10rpx; /* 添加间距 */
  border-radius: 20rpx 20rpx 0 0; /* 顶部圆角 */
  background-color: transparent; /* 移除默认背景色 */
}

.tab-item.active {
  color: #5698c3;
  font-weight: 500;
  background-color: #fff; /* 选中时背景变白 */
  border-bottom: 3rpx solid #5698c3; /* 底部晴蓝色线条 */
  transform: translateY(-2rpx); /* 稍微上移 */
}

.tab-item.active::after {
  display: none; /* 移除原来的下划线 */
}

/* 采购卡片样式 */
.purchase-card {
  background-color: transparent;
  margin: 0;
  padding: 0;
}

/* 门店选择框样式 */
.store-selector {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.store-selector-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.selector-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.store-picker {
  flex: 1;
}

.picker-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 2rpx solid #e5e5e5;
}

.store-name {
  font-size: 30rpx;
  color: #333;
  flex: 1;
}

.dropdown-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}

/* 采购车列表样式 - 参照顾客端购物车 */
.purchase-list {
  padding: 12rpx 16rpx;
}

.purchase-item {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  position: relative;
  min-height: 160rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 复选框样式 - 参照顾客端购物车 */
.select-box {
  width: 44rpx;
  height: 44rpx;
  margin-right: 20rpx;
  margin-top: 8rpx;
  border-radius: 50%;
  border: 3rpx solid #333333;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
  box-sizing: border-box;
  position: relative;
  padding: 20rpx;
  margin-left: -20rpx;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.select-box.selected {
  border-color: #5698c3;
  background-color: #E6F7EF;
  transform: scale(1.05);
}

.select-box image {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.8;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.select-box.selected image {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  margin-top: 8rpx;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  overflow: hidden;
  margin-right: 20rpx;
  margin-top: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 160rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  line-height: 1.4;
}

.product-price {
  font-size: 26rpx;
  color: #5698c3;
  font-weight: 500;
  align-self: flex-start;
}

/* 右侧控件区域 */
.right-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  height: 160rpx;
  padding-top: 8rpx;
}

/* 数量控制样式 */
.quantity-control {
  display: flex;
  align-items: center;
  height: 48rpx;
  border: 2rpx solid #EEEEEE;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 0;
}

.quantity-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F5F5F5;
  font-size: 28rpx;
  color: #666666;
  font-weight: 500;
  transition: all 0.2s ease;
}

.quantity-btn:active {
  background-color: #E5E5E5;
}

.quantity-input {
  width: 64rpx;
  height: 48rpx;
  text-align: center;
  background-color: #fff;
  border-left: 2rpx solid #EEEEEE;
  border-right: 2rpx solid #EEEEEE;
  font-size: 24rpx;
  color: #333333;
}

/* 小计金额 */
.subtotal-price {
  font-size: 32rpx;
  color: #5698c3;
  font-weight: bold;
  margin-bottom: 16rpx;
}

/* 空采购车样式 */
.empty-purchase {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.add-product-btn {
  padding: 20rpx 40rpx;
  background-color: #5698c3;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
  transition: all 0.2s ease;
}

.add-product-btn:active {
  transform: scale(0.95);
}

/* 上架和移库卡片占位样式 */
.shelf-card,
.transfer-card {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  min-height: 400rpx;
}

.card-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-size: 28rpx;
}

/* 结算栏样式 - 参照顾客端购物车 */
.checkout-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 138rpx; /* 为底部导航留出空间，partner-tabbar高度是138rpx */
  height: 120rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  padding: 0 32rpx 20rpx 32rpx;
  box-shadow: 0 -4rpx 8rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}

/* 全选区域 */
.select-all {
  display: flex;
  align-items: center;
}

.select-all .select-box {
  width: 44rpx;
  height: 44rpx;
  margin: 0;
  margin-right: 0;
  padding: 20rpx;
  margin-left: -20rpx;
}

.select-all text {
  font-size: 28rpx;
  color: #333333;
  margin-left: 12rpx;
}

.total-info {
  flex: 1;
  margin-left: 40rpx;
}

.total-price {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
}

.total-price .price {
  color: #5698c3;
  font-weight: bold;
}

.total-desc {
  font-size: 24rpx;
  color: #999;
}

.checkout-btn {
  width: 240rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  border-radius: 36rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 结算按钮禁用状态 */
.checkout-btn.disabled {
  background-color: #CCCCCC;
  color: #999999;
  box-shadow: none;
  cursor: not-allowed;
}

/* 结算按钮激活状态 */
.checkout-btn.active {
  background-color: #5698c3;
  color: #FFFFFF;
  box-shadow: 0 4rpx 8rpx rgba(86, 152, 195, 0.3);
}

.checkout-btn.active:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(86, 152, 195, 0.2);
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background-color: #fff;
}