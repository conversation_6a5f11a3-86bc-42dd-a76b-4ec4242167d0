const db = require('../../config/db');

/**
 * 库存服务层
 * 处理库存检查、分配、扣减等通用逻辑
 */
class InventoryService {
  /**
   * 检查平台库存是否充足
   * 根据业务文档，检查products表的platform_stock字段
   */
  static async checkPlatformStock(items) {
    const insufficientItems = [];
    
    for (const item of items) {
      const stockQuery = 'SELECT platform_stock FROM products WHERE id = ?';
      const stockResult = await db.query(stockQuery, [item.product_id]);
      
      if (stockResult.length === 0) {
        insufficientItems.push({
          product_id: item.product_id,
          required: item.quantity,
          available: 0,
          message: '商品不存在'
        });
        continue;
      }
      
      const availableStock = parseInt(stockResult[0].platform_stock || 0);
      const requiredQuantity = parseInt(item.quantity || 0);
      
      if (availableStock < requiredQuantity) {
        insufficientItems.push({
          product_id: item.product_id,
          required: requiredQuantity,
          available: availableStock,
          message: `商品库存不足，请联系客服确认后再下单`
        });
      }
    }
    
    return {
      sufficient: insufficientItems.length === 0,
      insufficientItems
    };
  }

  /**
   * 扣减平台库存
   * 根据业务文档，扣减products表的platform_stock字段
   */
  static async deductPlatformStock(items, connection = null) {
    const useConnection = connection || db;
    
    for (const item of items) {
      await useConnection.execute(
        'UPDATE products SET platform_stock = platform_stock - ? WHERE id = ? AND platform_stock >= ?',
        [item.quantity, item.product_id, item.quantity]
      );
    }
  }

  /**
   * 回滚平台库存
   * 根据业务文档，回滚products表的platform_stock字段
   */
  static async rollbackPlatformStock(items, connection = null) {
    const useConnection = connection || db;
    
    for (const item of items) {
      await useConnection.execute(
        'UPDATE products SET platform_stock = platform_stock + ? WHERE id = ?',
        [item.quantity, item.product_id]
      );
    }
  }

  /**
   * 按优先级分配库存
   * 优先级：订阅门店 > 销售人门店 > 平台总部
   * 根据业务文档实现门店归属逻辑
   * @param {Array} items 商品列表
   * @param {string} userId 用户ID
   * @param {string} deliveryMethod 配送方式：express-快递，self-自提
   */
  static async allocateStockByPriority(items, userId, deliveryMethod = 'express') {
    const allocations = [];
    
    // 获取用户信息（包括销售人和订阅门店）
    const userQuery = 'SELECT subscribe_store_no, salesman_id FROM users WHERE user_id = ?';
    const userResult = await db.query(userQuery, [userId]);
    
    if (userResult.length === 0) {
      throw new Error('用户不存在');
    }
    
    const user = userResult[0];
    const subscribeStoreNo = user.subscribe_store_no;
    const salesmanId = user.salesman_id;
    
    // 获取订阅门店ID
    let subscriptionStoreId = null;
    if (subscribeStoreNo) {
      const storeQuery = 'SELECT id FROM stores WHERE store_no = ?';
      const storeResult = await db.query(storeQuery, [subscribeStoreNo]);
      if (storeResult.length > 0) {
        subscriptionStoreId = storeResult[0].id;
      }
    }
    
    // 获取销售人名下的门店列表
    let salesmanStores = [];
    if (salesmanId) {
      // 通过partners表查询销售人关联的门店
      const salesmanStoresQuery = `
        SELECT s.id, s.store_no 
        FROM stores s 
        INNER JOIN partners p ON s.store_no = p.store_no 
        WHERE p.user_id = ?
      `;
      const salesmanStoresResult = await db.query(salesmanStoresQuery, [salesmanId]);
      salesmanStores = salesmanStoresResult;
    }
    
    for (const item of items) {
        let remainingQuantity = parseInt(item.quantity);
        const itemAllocations = [];
        
        // 1. 优先从订阅门店分配
        if (subscriptionStoreId && remainingQuantity > 0) {
          const storeStock = await this.getStoreStock(subscribeStoreNo, item.product_id, deliveryMethod);
          const allocatedFromSubscription = Math.min(storeStock, remainingQuantity);
          
          if (allocatedFromSubscription > 0) {
            itemAllocations.push({
              store_no: subscribeStoreNo,
              product_id: item.product_id,
              quantity: allocatedFromSubscription,
              source: 'subscription_store'
            });
            remainingQuantity -= allocatedFromSubscription;
          }
        }
        
        // 2. 从销售人名下的其他门店逐一分配
        for (const store of salesmanStores) {
          // 跳过已经分配过的订阅门店
          if (store.id === subscriptionStoreId || remainingQuantity <= 0) {
            continue;
          }
          
          const storeStock = await this.getStoreStock(store.store_no, item.product_id, deliveryMethod);
          const allocatedFromStore = Math.min(storeStock, remainingQuantity);
          
          if (allocatedFromStore > 0) {
            itemAllocations.push({
              store_no: store.store_no,
              product_id: item.product_id,
              quantity: allocatedFromStore,
              source: 'salesman_store'
            });
            remainingQuantity -= allocatedFromStore;
          }
        }
        
        // 3. 从平台总部分配剩余数量
        if (remainingQuantity > 0) {
          itemAllocations.push({
            store_no: 'platform', // 平台总部
            product_id: item.product_id,
            quantity: remainingQuantity,
            source: 'platform'
          });
        }
        
        allocations.push({
          product_id: item.product_id,
          total_quantity: parseInt(item.quantity),
          allocations: itemAllocations
        });
      }
    
    return allocations;
  }

  /**
   * 将库存分配到指定门店（用于自提订单）
   * 根据业务文档：自提订单直接归属到指定门店
   * @param {Array} items 商品列表
   * @param {string} storeNo 指定门店编号
   * @param {string} deliveryMethod 配送方式
   */
  static async allocateStockToSpecificStore(items, storeNo, deliveryMethod = 'self') {
    const allocations = [];
    
    for (const item of items) {
      const totalQuantity = parseInt(item.quantity);
      
      // 检查指定门店的库存
      const storeStock = await this.getStoreStock(storeNo, item.product_id, deliveryMethod);
      
      if (storeStock >= totalQuantity) {
        // 门店库存充足，全部分配到该门店
        allocations.push({
          product_id: item.product_id,
          total_quantity: totalQuantity,
          allocations: [{
            store_no: storeNo,
            product_id: item.product_id,
            quantity: totalQuantity,
            source: 'specified_store'
          }]
        });
      } else {
        // 门店库存不足，部分分配到门店，剩余分配到平台
        const itemAllocations = [];
        
        if (storeStock > 0) {
          itemAllocations.push({
            store_no: storeNo,
            product_id: item.product_id,
            quantity: storeStock,
            source: 'specified_store'
          });
        }
        
        const remainingQuantity = totalQuantity - storeStock;
        if (remainingQuantity > 0) {
          itemAllocations.push({
            store_no: 'platform',
            product_id: item.product_id,
            quantity: remainingQuantity,
            source: 'platform'
          });
        }
        
        allocations.push({
          product_id: item.product_id,
          total_quantity: totalQuantity,
          allocations: itemAllocations
        });
      }
    }
    
    return allocations;
  }

  /**
   * 获取门店库存
   * 根据业务文档和配送方式选择对应的库存字段
   * @param {string} storeNo 门店编号
   * @param {string} productId 商品ID
   * @param {string} deliveryMethod 配送方式：express-快递（使用cloud_quantity），self-自提（使用offline_quantity）
   */
  static async getStoreStock(storeNo, productId, deliveryMethod = 'express') {
    // 根据配送方式选择库存字段
    const stockField = deliveryMethod === 'self' ? 'offline_quantity' : 'cloud_quantity';
    const stockQuery = `SELECT ${stockField} FROM store_inventory WHERE store_no = ? AND product_id = ?`;
    const stockResult = await db.query(stockQuery, [storeNo, productId]);
    
    return stockResult.length > 0 ? parseInt(stockResult[0][stockField] || 0) : 0;
  }

  /**
   * 扣减门店库存
   * 根据业务文档和配送方式选择对应的库存字段
   * @param {string} storeNo 门店编号
   * @param {string} productId 商品ID
   * @param {number} quantity 扣减数量
   * @param {object} connection 数据库连接
   * @param {string} deliveryMethod 配送方式：express-快递（扣减cloud_quantity），self-自提（扣减offline_quantity）
   */
  static async deductStoreStock(storeNo, productId, quantity, connection = null, deliveryMethod = 'express') {
    const useConnection = connection || db;
    
    // 根据配送方式选择库存字段
    const stockField = deliveryMethod === 'self' ? 'offline_quantity' : 'cloud_quantity';
    
    // 直接使用store_no更新库存
    await useConnection.execute(
      `UPDATE store_inventory SET ${stockField} = ${stockField} - ? WHERE store_no = ? AND product_id = ? AND ${stockField} >= ?`,
      [quantity, storeNo, productId, quantity]
    );
  }

  /**
   * 回滚门店库存
   * 根据业务文档和配送方式选择对应的库存字段
   * @param {string} storeNo 门店编号
   * @param {string} productId 商品ID
   * @param {number} quantity 回滚数量
   * @param {object} connection 数据库连接
   * @param {string} deliveryMethod 配送方式：express-快递（回滚cloud_quantity），self-自提（回滚offline_quantity）
   */
  static async rollbackStoreStock(storeNo, productId, quantity, connection = null, deliveryMethod = 'express') {
    const useConnection = connection || db;
    
    // 根据配送方式选择库存字段
    const stockField = deliveryMethod === 'self' ? 'offline_quantity' : 'cloud_quantity';
    
    // 直接使用store_no更新库存
    await useConnection.execute(
      `UPDATE store_inventory SET ${stockField} = ${stockField} + ? WHERE store_no = ? AND product_id = ?`,
      [quantity, storeNo, productId]
    );
  }

  /**
   * 批量扣减库存（根据分配结果）
   * 根据业务文档：归属门店的订单需要同时扣减门店库存和平台库存，归属平台总部的订单只扣减平台库存
   * @param {Array} allocations 库存分配结果
   * @param {object} connection 数据库连接
   * @param {string} deliveryMethod 配送方式
   */
  static async batchDeductStock(allocations, connection = null, deliveryMethod = 'express') {
    for (const allocation of allocations) {
      for (const item of allocation.allocations) {
        if (item.source === 'platform') {
          // 归属平台总部的订单：只扣减平台库存
          await this.deductPlatformStock([{
            product_id: item.product_id,
            quantity: item.quantity
          }], connection);
        } else {
          // 归属门店的订单：同时扣减门店库存和平台库存
          await this.deductStoreStock(item.store_no, item.product_id, item.quantity, connection, deliveryMethod);
          await this.deductPlatformStock([{
            product_id: item.product_id,
            quantity: item.quantity
          }], connection);
        }
      }
    }
  }

  /**
   * 批量回滚库存（根据分配结果）
   * 根据业务文档：归属门店的订单需要同时回滚门店库存和平台库存，归属平台总部的订单只回滚平台库存
   * @param {Array} allocations 库存分配结果
   * @param {object} connection 数据库连接
   * @param {string} deliveryMethod 配送方式
   */
  static async batchRollbackStock(allocations, connection = null, deliveryMethod = 'express') {
    for (const allocation of allocations) {
      for (const item of allocation.allocations) {
        if (item.source === 'platform') {
          // 归属平台总部的订单：只回滚平台库存
          await this.rollbackPlatformStock([{
            product_id: item.product_id,
            quantity: item.quantity
          }], connection);
        } else {
          // 归属门店的订单：同时回滚门店库存和平台库存
          await this.rollbackStoreStock(item.store_no, item.product_id, item.quantity, connection, deliveryMethod);
          await this.rollbackPlatformStock([{
            product_id: item.product_id,
            quantity: item.quantity
          }], connection);
        }
      }
    }
  }
}

module.exports = InventoryService;