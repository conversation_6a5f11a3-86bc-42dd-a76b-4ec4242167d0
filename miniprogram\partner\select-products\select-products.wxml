<!--pages/partner/select-products/select-products.wxml-->
<!--合伙人端选品页面-->
<view class="select-products-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
    <view class="navbar-content">
      <view class="navbar-center">
        <picker class="store-picker" mode="selector" range="{{storeList}}" range-key="name" bindchange="onStoreChange" wx:if="{{storeList.length > 0}}">
          <view class="store-selector">
            <text class="store-name">{{selectedStore.name}}（{{selectedStore.store_no}}）</text>
            <view class="dropdown-arrow">▼</view>
          </view>
        </picker>
        <view class="store-selector" wx:else>
          <text class="store-name">暂无门店数据</text>
        </view>
      </view>
      <view class="navbar-right">
        <image class="more-icon" src="/images/icons2/更多.svg" bindtap="showMore"></image>
        <image class="eye-icon" src="/images/icons2/眼睛.svg" bindtap="toggleView"></image>
      </view>
    </view>
  </view>

  <!-- 固定搜索栏 -->
  <view class="fixed-search-section" style="top:{{statusBarHeight + 42}}px">
    <view class="search-input-wrapper">
      <image class="search-icon" src="/images/icons2/搜索.png"></image>
      <input class="search-input" 
             placeholder="搜索商品名称、品牌、规格" 
             placeholder-class="search-placeholder"
             value="{{searchKeyword}}"
             bindinput="onSearchInput"
             bindconfirm="onSearchConfirm" />
    </view>
  </view>

  <!-- 占位，防止内容被固定搜索栏遮挡 -->
  <view class="search-placeholder" style="height:{{statusBarHeight + 102}}px"></view>

  <!-- 轮播广告 -->
  <view class="banner-section">
    <swiper class="banner-swiper" 
            indicator-dots="{{true}}" 
            autoplay="{{true}}" 
            interval="{{3000}}" 
            duration="{{500}}" 
            circular="{{true}}"
            indicator-color="rgba(255, 255, 255, 0.6)"
            indicator-active-color="#FF6B35">
      <swiper-item wx:for="{{banners}}" wx:key="id" bindtap="onBannerTap" data-index="{{index}}">
        <image src="{{item.imageUrl || '/images/mo/mogoods.jpg'}}" mode="aspectFill" class="banner-image"></image>
        <view class="banner-overlay">
          <view class="banner-title">{{item.title}}</view>
          <view class="banner-subtitle" wx:if="{{item.subtitle}}">{{item.subtitle}}</view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 快捷菜单 -->
  <view class="quick-menu-section">
    <view class="quick-menu-grid">
      <view class="quick-menu-item" 
            wx:for="{{quickMenus}}" 
            wx:key="id"
            bindtap="onQuickMenuTap"
            data-menu="{{item}}">
        <view class="quick-menu-icon-wrapper">
          <image class="quick-menu-icon" src="{{item.icon}}"></image>
        </view>
        <text class="quick-menu-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 新品上市横滑区域 -->
  <view class="new-products-section">
    <view class="new-products-title">新品上市</view>
    <scroll-view class="new-products-scroll" scroll-x="true" show-scrollbar="false" bindscroll="onNewProductsScroll">
      <view class="new-products-list">
        <view class="new-product-card" 
              wx:for="{{newProducts}}" 
              wx:key="id" 
              style="width: 110px; margin-right: 8px; display: inline-block;"
              bindtap="onNewProductTap"
              data-product="{{item}}">
          <image class="new-product-image" src="{{item.imageUrl || '/images/mo/mogoods.jpg'}}" mode="aspectFill" binderror="onGoodsImgError"></image>
          <view class="new-product-name">{{item.name}}</view>
          <view class="new-product-price">¥{{item.price}}</view>
          <!-- 采购价信息 -->
          <view class="new-product-purchase-price" wx:if="{{item.purchasePrice}}">
            <text class="purchase-price-label">采购价：</text>
            <text class="purchase-price-value">¥{{item.purchasePrice}}</text>
          </view>
          <!-- 加入采购车按钮 -->
          <view class="add-to-cart-btn-small" 
                catchtap="onAddToCart" 
                data-product="{{item}}">
            <text class="add-to-cart-text-small">采购</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>



  <!-- 热门商品横滑区域 -->
  <view class="hot-products-section" wx:if="{{hotProducts.length > 0}}">
    <view class="hot-products-title">热门推荐</view>
    <scroll-view class="hot-products-scroll" scroll-x="true" show-scrollbar="false">
      <view class="hot-products-list">
        <view class="hot-product-card" 
              wx:for="{{hotProducts}}" 
              wx:key="id" 
              style="width: 120px; margin-right: 8px; display: inline-block;"
              bindtap="onHotProductTap"
              data-product="{{item}}">
          <image class="hot-product-image" src="{{item.imageUrl || '/images/mo/mogoods.jpg'}}" mode="aspectFill" binderror="onGoodsImgError"></image>
          <view class="hot-product-name">{{item.name}}</view>
          <view class="hot-product-price">¥{{item.price}}</view>
          <!-- 采购价信息 -->
          <view class="hot-product-purchase-price" wx:if="{{item.purchasePrice}}">
            <text class="purchase-price-label">采购价：</text>
            <text class="purchase-price-value">¥{{item.purchasePrice}}</text>
          </view>
          <!-- 加入采购车按钮 -->
          <view class="add-to-cart-btn-small" 
                catchtap="onAddToCart" 
                data-product="{{item}}">
            <text class="add-to-cart-text-small">采购</text>
          </view>
          <view class="hot-product-sales" wx:if="{{item.sales}}">已售{{item.sales}}</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 商品列表区域 -->
  <view class="products-section">
    <!-- 商品筛选标签和筛选按钮 -->
    <view class="filter-section">
      <scroll-view class="product-tabs-scroll" scroll-x="true" show-scrollbar="false">
        <view class="product-tabs">
          <view class="product-tab {{currentProductTab === index ? 'active' : ''}}"
                wx:for="{{productTabs}}"
                wx:key="*this"
                bindtap="onProductTabTap"
                data-index="{{index}}">
            {{item}}
          </view>
        </view>
      </scroll-view>
      <view class="filter-btn" bindtap="onShowFilter">
        <image class="filter-icon" src="/images/icons2/筛选.svg"></image>
      </view>
    </view>
    
    <!-- 商品网格 -->
    <view class="products-grid">
      <view class="product-item"
            wx:for="{{products}}"
            wx:key="id"
            bindtap="onProductTap"
            data-id="{{item.id}}">
        <view class="product-image-wrapper">
          <image class="product-image" src="{{imgErrorMap[item.id] ? '/images/mo/mogoods.jpg' : (item.images && item.images[0] ? item.images[0] : (item.imageUrl || '/images/mo/mogoods.jpg'))}}" mode="aspectFill" binderror="onGoodsImgError" data-id="{{item.id}}"></image>
          <!-- 商品标签 -->
          <view class="product-tags" wx:if="{{item.tags && item.tags.length > 0}}">
            <text class="product-tag" wx:for="{{item.tags}}" wx:for-item="tag" wx:key="tag-{{index}}-{{tag}}">{{tag}}</text>
          </view>
          <!-- 新品标识 -->
          <view class="product-new-badge" wx:if="{{item.isNew}}">新品</view>
          <!-- 热销标识 -->
          <view class="product-hot-badge" wx:if="{{item.isHot}}">热销</view>
        </view>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-desc" wx:if="{{item.description}}">{{item.description}}</text>
          <view class="product-price-row">
            <view class="product-price">
              <text class="price-symbol">¥</text>
              <text class="price-value">{{item.price}}</text>
              <text class="price-unit" wx:if="{{item.unit}}">/{{item.unit}}</text>
            </view>
            <!-- 销量信息 -->
            <view class="product-sales" wx:if="{{item.sales}}">
              <text>已售{{item.sales}}</text>
            </view>
          </view>
          <!-- 采购价信息 -->
          <view class="product-purchase-price" wx:if="{{item.purchasePrice}}">
            <text class="purchase-price-label">采购价：</text>
            <text class="purchase-price-value">¥{{item.purchasePrice}}</text>
          </view>
          <!-- 库存信息 -->
          <view class="product-stock" wx:if="{{item.stock !== undefined}}">
            <text class="stock-text {{item.stock > 0 ? 'in-stock' : 'out-of-stock'}}">
              {{item.stock > 0 ? '有库存' : '无库存'}}
            </text>
          </view>
          <!-- 加入采购车按钮 -->
          <view class="add-to-cart-btn" 
                catchtap="onAddToCart" 
                data-product="{{item}}">
            <text class="add-to-cart-text">加入采购车</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading && !refreshing}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more-container" wx:if="{{!loading && !hasMore && products.length > 0}}">
    <view class="no-more-line"></view>
    <text class="no-more-text">没有更多商品了</text>
    <view class="no-more-line"></view>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!loading && products.length === 0}}">
    <image class="empty-icon" src="/images/icons2/暂无数据.svg"></image>
    <text class="empty-text">暂无商品数据</text>
    <view class="retry-btn" bindtap="initData">
      <text>重新加载</text>
    </view>
  </view>

  <!-- 回到顶部悬浮按钮 -->
  <view class="back-to-top" wx:if="{{showBackToTop}}" bindtap="onBackToTop">
    <image class="back-to-top-icon" src="/images/icons2/返回顶部.svg"></image>
  </view>

  <!-- 筛选抽屉 -->
  <filter-drawer 
    show="{{showFilterDrawer}}"
    filterOptions="{{filterOptions}}"
    bind:close="onHideFilter"
    bind:apply="onApplyFilter"
    bind:reset="onResetFilter">
  </filter-drawer>

  <!-- 底部间距 -->
  <view class="bottom-spacing"></view>
</view>

<!-- 合伙人底部导航栏 -->
<partner-tabbar selected="0" />