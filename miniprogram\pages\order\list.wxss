/* pages/order/list.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 30rpx;
  padding-top: 199rpx; /* 为搜索框和标签栏留出空间：111rpx(搜索框) + 88rpx(标签栏高度) = 199rpx */
}

/* 搜索框样式 */
.search-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  z-index: 1000; /* 确保搜索框在最顶层 */
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 50rpx;
  padding: 0 30rpx;
  height: 70rpx;
}

.search-icon {
  color: #999;
  margin-right: 20rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.clear-btn {
  padding: 10rpx;
  color: #999;
}

/* 顶部标签栏包裹，包含左右指示器 */
.tabs-wrapper {
  position: fixed !important; /* 强制固定定位，确保锁定效果 */
  top: 111rpx; /* 搜索框高度：20rpx(padding) + 70rpx(height) + 20rpx(padding) + 1rpx(border) = 111rpx */
  left: 0;
  right: 0;
  width: 100%; /* 明确设置宽度 */
  background-color: #fff;
  z-index: 999; /* 确保标签栏在搜索框下方但在内容上方 */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transform: translateZ(0); /* 启用硬件加速，增强固定效果 */
}

/* 标签栏样式 */
.tabs-container {
  width: 100%;
  background-color: #fff;
  white-space: nowrap;
  position: relative; /* 确保scroll-view不影响父容器的fixed定位 */
  z-index: 1; /* 确保内容在正确层级 */
}

.tabs {
  display: flex;
  padding: 0 20rpx;
  height: 88rpx;
  align-items: center;
  position: relative; /* 确保内部元素不影响父容器定位 */
  background-color: #fff; /* 确保背景色一致 */
}

.tab-item {
  position: relative;
  padding: 0 30rpx;
  height: 88rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
}

.tab-item.active {
  color: #ff6b00;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ff6b00;
  border-radius: 2rpx;
}

/* 订单列表样式 */
.order-list {
  padding: 5rpx; /* 减小左右边距到5像素 */
}

.order-item {
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  position: relative;
}

.order-item:active {
  transform: scale(0.995);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-no {
  font-size: 24rpx;
  color: #999;
  font-family: 'Courier New', monospace;
  letter-spacing: 1rpx;
}

.order-status {
  font-size: 26rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background-color: #f0f0f0;
  color: #666;
}

/* 不同状态的颜色 */
.order-status[data-status="pending_payment"] {
  background-color: #fff3e0;
  color: #ff6b00;
}

.order-status[data-status="pending_shipment"] {
  background-color: #e3f2fd;
  color: #2196f3;
}

.order-status[data-status="shipped"] {
  background-color: #f3e5f5;
  color: #9c27b0;
}

.order-status[data-status="completed"] {
  background-color: #e8f5e8;
  color: #4caf50;
}

.order-status[data-status="cancelled"] {
  background-color: #ffebee;
  color: #f44336;
}

.order-status[data-status="refund"] {
  background-color: #fafafa;
  color: #757575;
}

/* 订单商品 */
.order-products {
  padding: 0 24rpx;
}

.product-item {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  flex-shrink: 0;
  object-fit: cover;
  border: 1rpx solid #f0f0f0;
}

.product-info {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-specs {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.product-bottom {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: auto;
}

.product-price-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.product-unit-price {
  font-size: 24rpx;
  color: #666;
}

.product-qty {
  font-size: 24rpx;
  color: #999;
}

.product-total-price {
  font-size: 28rpx;
  color: #ff6b00;
  font-weight: 500;
  text-align: right;
}

/* 订单金额 */
.order-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

.total-quantity {
  font-size: 24rpx;
  color: #999;
}

.total-amount {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.total-label {
  font-size: 26rpx;
  color: #666;
}

.total-price {
  font-size: 32rpx;
  color: #ff6b00;
  font-weight: 600;
}

/* 订单操作按钮 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16rpx 20rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fafafa;
  min-height: 72rpx;
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
}

.action-btn {
  padding: 0 16rpx;
  height: 56rpx;
  line-height: 56rpx;
  border-radius: 28rpx;
  font-size: 24rpx;
  margin-left: 16rpx;
  background-color: #f5f5f5;
  color: #666;
  text-align: center;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s;
  min-width: 100rpx;
  flex-shrink: 0;
}

.action-btn:first-child {
  margin-left: 0;
}

.action-btn.primary {
  background-color: #ff6b00;
  color: #fff;
  border: none;
  font-weight: 500;
}

.action-btn.cancel {
  background-color: #fff;
  color: #999;
  border: 1rpx solid #e0e0e0;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 加载中 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading text {
  font-size: 26rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #999;
}

/* 顶部标签栏包裹，包含左右指示器 */
.tabs-wrapper {
  position: relative;
  background-color: #fff;
}

.left-indicator,
.right-indicator {
  position: absolute;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  padding: 0 10rpx;
  color: #bbb;
  font-size: 36rpx;
  z-index: 101;
  pointer-events: none; /* 指示器只用于提示，不拦截点击 */
}

.left-indicator {
  left: 0;
  background: linear-gradient(90deg, rgba(255,255,255,1) 30%, rgba(255,255,255,0));
}

.right-indicator {
  right: 0;
  background: linear-gradient(270deg, rgba(255,255,255,1) 30%, rgba(255,255,255,0));
}

.tab-help {
  margin-left: 12rpx;
  padding: 6rpx 12rpx;
  font-size: 22rpx;
  color: #666;
  background: #f5f5f5;
  border-radius: 16rpx;
  line-height: 1;
}