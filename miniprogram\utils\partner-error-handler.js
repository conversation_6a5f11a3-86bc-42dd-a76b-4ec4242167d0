/**
 * 合伙人端错误处理工具
 */

/**
 * 处理合伙人API错误
 * @param {Object} error - 错误对象
 * @param {Object} options - 选项
 * @param {string} options.title - 错误标题
 * @param {string} options.context - 错误上下文
 * @param {Function} options.onRetry - 重试回调
 * @param {Function} options.onCancel - 取消回调
 */
function handlePartnerApiError(error, options = {}) {
  const {
    title = '操作失败',
    context = '',
    onRetry = null,
    onCancel = null
  } = options;

  console.error(`${context} 错误:`, error);

  // 检查错误类型
  if (error.code === 401 || (error.response && error.response.status === 401)) {
    // 登录失效
    wx.showModal({
      title: '登录已失效',
      content: '请重新登录后再试',
      showCancel: false,
      success: () => {
        wx.reLaunch({
          url: '/pages/auth/auth'
        });
      }
    });
    return;
  }

  if (error.code === -1 || error.message.includes('网络') || error.code === 'ECONNREFUSED') {
    // 网络错误
    wx.showModal({
      title: '网络连接失败',
      content: '请检查网络连接后重试',
      confirmText: '重试',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm && onRetry) {
          onRetry();
        } else if (res.cancel && onCancel) {
          onCancel();
        }
      }
    });
    return;
  }

  // 其他错误
  const message = error.message || error.errMsg || '未知错误';
  wx.showModal({
    title: title,
    content: `${context ? context + ': ' : ''}${message}`,
    confirmText: onRetry ? '重试' : '确定',
    cancelText: onRetry ? '取消' : '',
    showCancel: !!onRetry,
    success: (res) => {
      if (res.confirm && onRetry) {
        onRetry();
      } else if (res.cancel && onCancel) {
        onCancel();
      }
    }
  });
}

/**
 * 处理门店数据加载错误
 * @param {Object} error - 错误对象
 * @param {Function} onRetry - 重试回调
 * @param {Function} onBack - 返回回调
 */
function handleStoreDataError(error, onRetry = null, onBack = null) {
  handlePartnerApiError(error, {
    title: '门店数据获取失败',
    context: '无法获取门店信息',
    onRetry: onRetry,
    onCancel: onBack || (() => {
      wx.navigateBack();
    })
  });
}

/**
 * 检查并处理门店数据状态
 * @param {Object} pageData - 页面数据
 * @param {Function} onRetry - 重试回调
 * @returns {boolean} - 是否有有效的门店数据
 */
function checkStoreData(pageData, onRetry = null) {
  const { storeList, selectedStore } = pageData;
  
  if (!storeList || storeList.length === 0) {
    wx.showModal({
      title: '暂无门店数据',
      content: '检测到门店数据为空，是否重新加载门店信息？',
      confirmText: '重新加载',
      cancelText: '返回',
      success: (res) => {
        if (res.confirm && onRetry) {
          onRetry();
        } else {
          wx.navigateBack();
        }
      }
    });
    return false;
  }

  if (!selectedStore) {
    wx.showToast({
      title: '请先选择门店',
      icon: 'none'
    });
    return false;
  }

  return true;
}

/**
 * 安全地获取门店编号
 * @param {Object} store - 门店对象
 * @returns {string|null} - 门店编号
 */
function getStoreNo(store) {
  if (!store) {
    console.warn('门店对象为空');
    return null;
  }
  
  return store.store_no || store.storeNo || null;
}

/**
 * 显示加载状态
 * @param {string} title - 加载提示文字
 */
function showLoading(title = '加载中...') {
  wx.showLoading({
    title: title,
    mask: true
  });
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
  wx.hideLoading();
}

/**
 * 显示成功提示
 * @param {string} message - 成功消息
 */
function showSuccess(message) {
  wx.showToast({
    title: message,
    icon: 'success',
    duration: 2000
  });
}

/**
 * 显示错误提示
 * @param {string} message - 错误消息
 */
function showError(message) {
  wx.showToast({
    title: message,
    icon: 'none',
    duration: 3000
  });
}

module.exports = {
  handlePartnerApiError,
  handleStoreDataError,
  checkStoreData,
  getStoreNo,
  showLoading,
  hideLoading,
  showSuccess,
  showError
};
