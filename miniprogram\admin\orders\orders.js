const app = getApp();
const { adminApi } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager');

Page({
  data: {
    // 一级分类
    firstLevelTabs: [
      { key: 'kuaidi', label: '顾客快递' },
      { key: 'ziti', label: '顾客自提' },
      { key: 'caigou', label: '门店采购' },
      { key: 'yiku', label: '门店移库' }
    ],
    // 二级分类映射
    secondLevelTabsMap: {
      kuaidi: [
        { key: 'daifukuan', label: '待付款' },
        { key: 'daifahuo', label: '待发货' },
        { key: 'daishouhuo', label: '待收货' },
        { key: 'yiwancheng', label: '已完成' },
        { key: 'tuihuo', label: '退换货/售后' },
        { key: 'yiquxiao', label: '已取消' }
      ],
      ziti: [
        { key: 'daifukuan', label: '待付款' },
        { key: 'daiziti', label: '待自提' },
        { key: 'yiwan<PERSON>', label: '已完成' },
        { key: 'tuihuo', label: '退换货/售后' },
        { key: 'yiquxiao', label: '已取消' }
      ],
      caigou: [
        { key: 'yixiadan', label: '待审核' },
        { key: 'yishenhe', label: '已审核' },
        { key: 'yijujue', label: '已拒绝' },
        { key: 'yiquxiao', label: '已取消' }
      ],
      yiku: [
        { key: 'yixiadan', label: '待审核' },
        { key: 'yifahuo', label: '已发货' },
        { key: 'yidaodian', label: '已到店' },
        { key: 'yijujue', label: '已拒绝' },
        { key: 'yiquxiao', label: '已取消' }
      ]
    },
    currentFirstTab: 'kuaidi',
    currentSecondTab: 'daifukuan',
    currentFirstTabLabel: '顾客快递',
    currentSecondTabLabel: '待付款',
    // 订单列表
    orderList: [],
    loading: false,
    hasMore: true,
    page: 1,
    limit: 20,
    // 发货相关数据
    showShipModal: false,
    currentOrderId: null,
    shipForm: {
      tracking_number: '',
      shipping_company: ''
    }
  },
  onLoad(options) {
    console.log('订单管理页面加载', options);
    
    // 检查登录状态
    this.checkLoginState();
    
    let tab = options && options.tab;
    if (!tab || !this.data.firstLevelTabs.some(t => t.key === tab)) {
      tab = this.data.currentFirstTab;
    }
    const firstTabObj = this.data.firstLevelTabs.find(t => t.key === tab);
    const secondTabObj = this.data.secondLevelTabsMap[tab][0];
    
    console.log('订单页面初始化 - 选项卡:', tab, '二级选项卡:', secondTabObj.key);
    
    this.setData({
      currentFirstTab: tab,
      currentSecondTab: secondTabObj.key,
      currentFirstTabLabel: firstTabObj.label,
      currentSecondTabLabel: secondTabObj.label
    });
    this.loadOrderList();
  },

  onShow() {
    // 检查登录状态
    this.checkLoginState();
    
    // 页面显示时刷新数据
    this.loadOrderList(true);
  },

  /**
   * 加载订单列表
   * @param {boolean} refresh 是否刷新数据
   */
  async loadOrderList(refresh = false) {
    if (this.data.loading) return;

    if (refresh) {
      this.setData({
        page: 1,
        orderList: [],
        hasMore: true
      });
    }

    this.setData({ loading: true });

    try {
      // 前端分类到后端参数的映射
      const categoryMap = {
        'kuaidi': 'express',    // 顾客快递
        'ziti': 'self',        // 顾客自提
        'caigou': 'purchase',   // 门店采购
        'yiku': 'transfer'      // 门店移库
      };

      // 前端二级分类到后端状态的映射
      // 根据当前一级分类确定状态映射
      let subcategoryMap = {};
      
      if (this.data.currentFirstTab === 'kuaidi' || this.data.currentFirstTab === 'ziti') {
        // 顾客订单状态映射 - 直接使用数据库中的中文状态
        subcategoryMap = {
          'daifukuan': '待付款',              // 待付款
          'daifahuo': '待发货',               // 待发货
          'daishouhuo': '已发货',             // 待收货（数据库中存储为"已发货"）
          'yiwancheng': '已完成',             // 已完成
          'tuihuo': '退换货/售后',            // 退换货/售后
          'yiquxiao': '已取消',               // 已取消
          'daiziti': '待自提'                 // 待自提
        };
      } else if (this.data.currentFirstTab === 'caigou') {
        // 门店采购订单状态映射 - 直接使用数据库中的中文状态
        subcategoryMap = {
          'yixiadan': '待审核',               // 待审核
          'yishenhe': '已审核',               // 已审核
          'yijujue': '已拒绝',                // 已拒绝
          'yiquxiao': '已取消'                // 已取消
        };
      } else if (this.data.currentFirstTab === 'yiku') {
        // 门店移库订单状态映射 - 直接使用数据库中的中文状态
        subcategoryMap = {
          'yixiadan': '待审核',               // 待审核
          'yifahuo': '已发货',                // 已发货
          'yidaodian': '已到店',              // 已到店
          'yijujue': '已拒绝',                // 已拒绝
          'yiquxiao': '已取消'                // 已取消
        };
      }

      // 构建查询参数
      const params = {
        page: this.data.page,
        limit: this.data.limit,
        category: categoryMap[this.data.currentFirstTab] || 'all',
        subcategory: subcategoryMap[this.data.currentSecondTab] || this.data.currentSecondTab
      };

      console.log('加载订单列表参数:', params);
      console.log('当前一级分类:', this.data.currentFirstTab, '-> 后端分类:', params.category);
      console.log('当前二级分类:', this.data.currentSecondTab, '-> 后端状态:', params.subcategory);
      
      // 特别记录门店采购订单的查询
      if (params.category === 'purchase') {
        console.log('正在查询门店采购订单，状态:', params.subcategory);
        console.log('完整API调用URL参数:', JSON.stringify(params));
      }

      const res = await adminApi.getAllOrders(params);
      console.log('订单列表响应:', res);
      
      // 特别记录门店采购订单的响应
      if (params.category === 'purchase') {
        console.log('门店采购订单查询结果:', res.data?.orders?.length || 0, '条订单');
        console.log('门店采购订单详细数据:', res.data?.orders);
      }

      if (res.success && res.data) {
        const newOrders = res.data.orders || res.data.list || [];
        const total = res.data.total || 0;
        
        // 格式化订单数据
        const formattedOrders = newOrders.map(order => {
          
          // 统一订单类型字段：后端可能返回type或order_type
          const orderType = order.order_type || order.type;
          
          // 为顾客订单的商品项目预计算小计
          if (orderType === 'customer' && order.items) {
            order.items = order.items.map(item => ({
              ...item,
              subtotal: (item.price * item.quantity).toFixed(2)
            }));
          }
          
          // 处理门店采购订单的商品图片
          if (orderType === 'purchase' && order.product_image) {
            try {
              // 如果product_image是JSON字符串，解析第一张图片
              const images = JSON.parse(order.product_image);
              order.product_image = Array.isArray(images) && images.length > 0 ? images[0] : order.product_image;
            } catch (e) {
              // 如果解析失败，保持原值
              console.log('商品图片解析失败:', order.product_image);
            }
          }
          
          return {
            ...order,
            // 统一设置订单类型
            order_type: orderType,
            // 格式化时间显示
            formatted_time: this.formatDateTime(order.created_at),
            // 添加中文状态显示
            status_text: this.getStatusText(order.status, orderType),
            // 门店采购订单使用后端计算的采购价格和总金额
            subtotal: orderType === 'purchase' ? order.total_amount || (order.purchase_price || order.price) * order.quantity : null
          };
        });
        
        this.setData({
          orderList: refresh ? formattedOrders : [...this.data.orderList, ...formattedOrders],
          hasMore: this.data.orderList.length + formattedOrders.length < total,
          page: this.data.page + 1
        });
      } else {
        wx.showToast({
          title: res.message || '获取订单数据失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载订单列表失败:', error);
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginState() {
    // 恢复登录状态到全局
    loginStateManager.restoreLoginStateToGlobal();
    
    const app = getApp();
    const globalData = app.globalData || {};
    
    // 检查是否已登录且有管理员权限
    if (!globalData.isLogin || !globalData.userInfo) {
      console.warn('管理端订单页面：用户未登录，跳转到登录页面');
      wx.redirectTo({
        url: '/pages/auth/auth'
      });
      return false;
    }
    
    // 检查是否有管理员权限
    let hasAdminRole = false;
    if (globalData.userRoles && Array.isArray(globalData.userRoles)) {
      hasAdminRole = globalData.userRoles.some(role => role.role_type === 'admin');
    } else if (globalData.currentRole) {
      hasAdminRole = globalData.currentRole.role_type === 'admin';
    } else if (globalData.userInfo.role_type) {
      hasAdminRole = globalData.userInfo.role_type === 'admin';
    }
    
    if (!hasAdminRole) {
      console.warn('管理端订单页面：用户没有管理员权限');
      wx.showModal({
        title: '权限不足',
        content: '您没有访问管理端的权限',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/home/<USER>'
          });
        }
      });
      return false;
    }
    
    return true;
  },

  // 一级分类切换
  onFirstTabChange(e) {
    const key = e.currentTarget.dataset.key;
    const secondTabs = this.data.secondLevelTabsMap[key];
    const firstTabObj = this.data.firstLevelTabs.find(t => t.key === key);
    const secondTabObj = secondTabs[0];
    this.setData({
      currentFirstTab: key,
      currentSecondTab: secondTabObj.key,
      currentFirstTabLabel: firstTabObj.label,
      currentSecondTabLabel: secondTabObj.label
    });
    // 切换分类时重新加载数据
    this.loadOrderList(true);
  },
  // 二级分类切换
  onSecondTabChange(e) {
    const key = e.currentTarget.dataset.key;
    const secondTabObj = this.data.secondLevelTabsMap[this.data.currentFirstTab].find(t => t.key === key);
    this.setData({
      currentSecondTab: key,
      currentSecondTabLabel: secondTabObj.label
    });
    // 切换分类时重新加载数据
    this.loadOrderList(true);
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadOrderList(true).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadOrderList();
    }
  },

  /**
   * 查看订单详情
   */
  onViewOrderDetail(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('查看订单详情:', orderId);
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${orderId}`
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation(e) {
    // 阻止事件冒泡，防止触发订单卡片的点击事件
  },

  /**
   * 提醒顾客付款
   */
  onRemindPayment(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('提醒顾客付款:', orderId);
    
    wx.showModal({
      title: '提醒付款',
      content: '确定要发送付款提醒消息给顾客吗？',
      success: (res) => {
        if (res.confirm) {
          this.sendPaymentReminder(orderId);
        }
      }
    });
  },

  /**
   * 发送付款提醒消息
   */
  async sendPaymentReminder(orderId) {
    try {
      wx.showLoading({ title: '发送提醒中...' });
      
      const res = await adminApi.sendPaymentReminder(orderId);
      
      if (res.success) {
        wx.showToast({
          title: '提醒消息已发送',
          icon: 'success'
        });
        console.log('付款提醒已发送给订单:', orderId);
      } else {
        wx.showToast({
          title: res.message || '发送失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('发送付款提醒失败:', error);
      wx.showToast({
        title: '发送失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 发货订单
   */
  onShipOrder(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('发货订单:', orderId);
    
    // 显示发货信息输入弹窗
    this.setData({
      showShipModal: true,
      currentOrderId: orderId,
      shipForm: {
        tracking_number: '',
        shipping_company: ''
      }
    });
  },

  /**
   * 处理发货表单输入
   */
  onShipFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [`shipForm.${field}`]: value
    });
  },

  /**
   * 取消发货
   */
  onCancelShip() {
    this.setData({
      showShipModal: false,
      currentOrderId: null,
      shipForm: {
        tracking_number: '',
        shipping_company: ''
      }
    });
  },

  /**
   * 确认发货
   */
  onConfirmShip() {
    const { currentOrderId, shipForm } = this.data;
    
    // 物流单号是必填的
    if (!shipForm.tracking_number.trim()) {
      wx.showToast({
        title: '请输入物流单号',
        icon: 'none'
      });
      return;
    }
    
    // 快递公司是必填的
    if (!shipForm.shipping_company.trim()) {
      wx.showToast({
        title: '请输入快递公司',
        icon: 'none'
      });
      return;
    }
    
    this.shipOrder(currentOrderId, shipForm);
  },

  /**
   * 执行发货订单
   */
  async shipOrder(orderId, shipData = {}) {
    try {
      wx.showLoading({ title: '发货中...' });
      
      const res = await adminApi.shipOrder(orderId, shipData);
      
      if (res.success) {
        wx.showToast({
          title: '发货成功',
          icon: 'success'
        });
        
        // 关闭弹窗
        this.onCancelShip();
        
        // 刷新订单列表
        this.loadOrderList(true);
      } else {
        wx.showToast({
          title: res.message || '发货失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('发货订单失败:', error);
      wx.showToast({
        title: '发货失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 获取状态中文显示
   * @param {string} status - 订单状态
   * @param {string} orderType - 订单类型
   * @returns {string} 中文状态
   */
  getStatusText(status, orderType) {
    // 数据库现在直接存储中文状态，无需转换
    return status;
  },



  /**
   * 审核采购订单
   */
  onApproveOrder(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('审核采购订单:', orderId);
    
    wx.showModal({
      title: '确认审核',
      content: '确定要审核通过这个采购订单吗？审核后将增加门店库存。',
      success: (res) => {
        if (res.confirm) {
          this.approveOrder(orderId);
        }
      }
    });
  },

  /**
   * 拒绝采购订单
   */
  onRejectOrder(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('拒绝采购订单:', orderId);
    
    wx.showModal({
      title: '确认拒绝',
      content: '确定要拒绝这个采购订单吗？拒绝后将回滚门店支付的股本金。',
      success: (res) => {
        if (res.confirm) {
          this.rejectOrder(orderId);
        }
      }
    });
  },

  /**
   * 执行审核订单
   */
  async approveOrder(orderId) {
    try {
      wx.showLoading({ title: '审核中...' });
      
      const res = await adminApi.approveOrder(orderId);
      
      if (res.success) {
        wx.showToast({
          title: '审核成功',
          icon: 'success'
        });
        
        // 刷新订单列表
        this.loadOrderList(true);
      } else {
        wx.showToast({
          title: res.message || '审核失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('审核订单失败:', error);
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 执行拒绝订单
   */
  async rejectOrder(orderId) {
    try {
      wx.showLoading({ title: '处理中...' });
      
      const res = await adminApi.rejectOrder(orderId);
      
      if (res.success) {
        wx.showToast({
          title: '拒绝成功',
          icon: 'success'
        });
        
        // 刷新订单列表
        this.loadOrderList(true);
      } else {
        wx.showToast({
          title: res.message || '拒绝失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('拒绝订单失败:', error);
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 管理端确认收货（替代顾客操作）
   */
  onConfirmReceipt(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('管理端确认收货:', orderId);
    
    wx.showModal({
      title: '确认收货',
      content: '确定要替代顾客确认收货吗？确认后订单将变为已完成状态。',
      success: (res) => {
        if (res.confirm) {
          this.confirmReceipt(orderId);
        }
      }
    });
  },

  /**
   * 执行确认收货
   */
  async confirmReceipt(orderId) {
    try {
      wx.showLoading({ title: '确认收货中...' });
      
      const res = await adminApi.confirmReceipt(orderId);
      
      if (res.success) {
        wx.showToast({
          title: '确认收货成功',
          icon: 'success'
        });
        
        // 刷新订单列表
        this.loadOrderList(true);
      } else {
        wx.showToast({
          title: res.message || '确认收货失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('确认收货失败:', error);
      wx.showToast({
        title: '确认收货失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 格式化日期时间
   * @param {string} dateTimeStr - 日期时间字符串
   * @returns {string} 格式化后的时间字符串
   */
  formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';
    
    try {
      const date = new Date(dateTimeStr);
      
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return dateTimeStr; // 如果无法解析，返回原始字符串
      }
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
      console.error('时间格式化失败:', error);
      return dateTimeStr; // 出错时返回原始字符串
    }
  }
});