const db = require('../../config/db');

/**
 * 支付服务层
 * 处理各种支付方式的通用逻辑
 */
class PaymentService {
  /**
   * 处理余额支付
   */
  static async processBalancePayment(userId, orderAmount, orderId, orderType = 'customer') {
    // 查询用户余额
    const balanceQuery = 'SELECT account_balance FROM user_fund_accounts WHERE user_id = ?';
    const balanceResult = await db.query(balanceQuery, [userId]);
    
    let balance = 0;
    if (balanceResult.length === 0) {
      // 如果用户没有资金账户，创建一个
      await db.query('INSERT INTO user_fund_accounts (user_id, account_balance, created_at, updated_at) VALUES (?, ?, ?, ?)', 
        [userId, 0, Date.now(), Date.now()]);
    } else {
      balance = parseFloat(balanceResult[0].account_balance || 0);
    }
    
    const orderAmountNum = parseFloat(orderAmount || 0);
    
    // 检查余额是否足够
    if (balance < orderAmountNum) {
      throw new Error('余额不足');
    }
    
    // 开始事务
    const connection = await db.getConnection();
    try {
      await connection.beginTransaction();

      // 扣减余额
      await connection.execute('UPDATE user_fund_accounts SET account_balance = account_balance - ?, updated_at = ? WHERE user_id = ?',
        [orderAmountNum, Date.now(), userId]);

      // 添加资金变动记录
      await connection.execute(
        'INSERT INTO user_fund_records (user_id, type, amount, balance, description, order_id, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [userId, 'payment', -orderAmountNum, balance - orderAmountNum, '订单支付', orderId, '已完成', Date.now()]
      );

      // 注意：订单状态更新已移至controller层处理，以便根据配送方式设置正确状态
      
      // 提交事务
      await connection.commit();
      return { success: true, message: '支付成功' };
    } catch (error) {
      // 回滚事务
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 处理余额支付（使用外部事务连接）
   * 用于在已有事务中进行余额支付操作
   */
  static async processBalancePaymentWithConnection(userId, orderAmount, orderId, orderType = 'customer', connection) {
    // 查询用户余额
    const [balanceRows] = await connection.execute('SELECT account_balance FROM user_fund_accounts WHERE user_id = ?', [userId]);

    let balance = 0;
    if (balanceRows.length === 0) {
      // 如果用户没有资金账户，创建一个
      await connection.execute('INSERT INTO user_fund_accounts (user_id, account_balance, created_at, updated_at) VALUES (?, ?, ?, ?)',
        [userId, 0, Date.now(), Date.now()]);
    } else {
      balance = parseFloat(balanceRows[0].account_balance || 0);
    }

    const orderAmountNum = parseFloat(orderAmount || 0);

    // 检查余额是否足够
    if (balance < orderAmountNum) {
      return { success: false, message: '余额不足' };
    }

    // 扣减余额
    await connection.execute('UPDATE user_fund_accounts SET account_balance = account_balance - ?, updated_at = ? WHERE user_id = ?',
      [orderAmountNum, Date.now(), userId]);

    // 添加资金变动记录
    await connection.execute(
      'INSERT INTO user_fund_records (user_id, type, amount, balance, description, order_id, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [userId, 'payment', -orderAmountNum, balance - orderAmountNum, '订单支付', orderId, '已完成', Date.now()]
    );

    // 注意：订单状态更新已移至controller层处理，以便根据配送方式设置正确状态
    return { success: true, message: '支付成功' };
  }

  /**
   * 检查用户余额
   */
  static async getUserBalance(userId) {
    const balanceQuery = 'SELECT account_balance FROM user_fund_accounts WHERE user_id = ?';
    const balanceResult = await db.query(balanceQuery, [userId]);

    if (balanceResult.length === 0) {
      return 0;
    }

    return parseFloat(balanceResult[0].account_balance || 0);
  }

  /**
   * 创建用户资金账户
   */
  static async createUserFundAccount(userId) {
    const existingQuery = 'SELECT id FROM user_fund_accounts WHERE user_id = ?';
    const existing = await db.query(existingQuery, [userId]);
    
    if (existing.length === 0) {
      await db.query('INSERT INTO user_fund_accounts (user_id, account_balance, created_at, updated_at) VALUES (?, ?, ?, ?)', 
        [userId, 0, Date.now(), Date.now()]);
    }
  }

  /**
   * 处理退款
   */
  static async processRefund(orderId, refundAmount, orderType = 'customer', connection = null) {
    const useConnection = connection || db;
    
    // 获取订单信息和用户ID
    let orderQuery, orderResult;
    if (orderType === 'customer') {
      orderQuery = 'SELECT user_id, total_amount FROM customer_orders WHERE id = ?';
    } else {
      orderQuery = 'SELECT user_id, total_amount FROM store_orders WHERE id = ?';
    }
    
    orderResult = await useConnection.query(orderQuery, [orderId]);
    if (orderResult.length === 0) {
      throw new Error('订单不存在');
    }
    
    const order = orderResult[0];
    const userId = order.user_id;
    const refundAmountNum = parseFloat(refundAmount || 0);
    
    // 获取用户当前余额
    const balanceQuery = 'SELECT account_balance FROM user_fund_accounts WHERE user_id = ?';
    const balanceResult = await useConnection.query(balanceQuery, [userId]);
    
    let currentBalance = 0;
    if (balanceResult.length > 0) {
      currentBalance = parseFloat(balanceResult[0].account_balance || 0);
    } else {
      // 如果用户没有资金账户，创建一个
      await useConnection.execute('INSERT INTO user_fund_accounts (user_id, account_balance, created_at, updated_at) VALUES (?, ?, ?, ?)', 
        [userId, 0, Date.now(), Date.now()]);
    }
    
    // 退款到用户余额
    await useConnection.execute('UPDATE user_fund_accounts SET account_balance = account_balance + ?, updated_at = ? WHERE user_id = ?',
      [refundAmountNum, Date.now(), userId]);
    
    // 记录退款记录
    await useConnection.execute(
      'INSERT INTO user_fund_records (user_id, type, amount, balance, description, order_id, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [userId, 'refund', refundAmountNum, currentBalance + refundAmountNum, '订单退款', orderId, '已完成', Date.now()]
    );
    
    return { success: true, message: '退款成功' };
  }

  /**
   * 记录资金变动
   */
  static async recordFundChange(userId, type, amount, balance, description, orderId = null, status = '已完成') {
    await db.query(
      'INSERT INTO user_fund_records (user_id, type, amount, balance, description, order_id, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [userId, type, amount, balance, description, orderId, status, Date.now()]
    );
  }
}

module.exports = PaymentService;