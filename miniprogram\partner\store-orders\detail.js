// partner/store-orders/detail.js
// 门店订单详情页面
const { orderApi } = require('../../utils/api');

Page({
  data: {
    // 页面状态
    loading: true,
    error: false,
    errorMsg: '',
    
    // 订单数据
    orderId: null,
    orderType: null,
    orderDetail: null
  },

  onLoad: function(options) {
    console.log('门店订单详情页面加载，参数:', options);
    
    // 获取订单ID和类型
    const orderId = options.id;
    const orderType = options.type || 'purchase';
    
    if (!orderId) {
      this.setData({
        loading: false,
        error: true,
        errorMsg: '订单ID不能为空'
      });
      return;
    }
    
    this.setData({
      orderId,
      orderType
    });
    
    // 加载订单详情
    this.loadOrderDetail();
  },

  /**
   * 加载订单详情
   */
  loadOrderDetail: function() {
    this.setData({
      loading: true,
      error: false
    });
    
    // 调用API获取订单详情
    orderApi.getStoreOrderDetail({
      orderId: this.data.orderId
    }).then(res => {
      console.log('获取订单详情成功:', res);
      
      if (res.success && res.data) {
        this.setData({
          orderDetail: this.processOrderDetail(res.data),
          loading: false
        });
      } else {
        this.setData({
          loading: false,
          error: true,
          errorMsg: res.message || '获取订单详情失败'
        });
      }
    }).catch(err => {
      console.error('获取订单详情异常:', err);
      this.setData({
        loading: false,
        error: true,
        errorMsg: '网络异常，请重试'
      });
    });
  },

  /**
   * 处理订单详情数据
   */
  processOrderDetail: function(data) {
    if (!data) return null;
    
    // 使用后端已经处理好的状态文本
    const statusText = data.status_text || '未知状态';
    
    // 处理订单项目，确保金额计算正确
    const items = data.items || [];
    let totalQuantity = 0;
    
    items.forEach(item => {
      // 如果后端已经计算了金额，直接使用；否则重新计算
      if (!item.amount) {
        item.amount = (parseFloat(item.price || 0) * parseInt(item.quantity || 0)).toFixed(2);
      }
      totalQuantity += parseInt(item.quantity || 0);
    });
    
    // 使用后端格式化的日期时间，如果没有则使用原始数据
    const createdAt = data.created_at_formatted || 
                     (data.created_at ? this.formatDateTime(new Date(data.created_at)) : '未知时间');
    const updatedAt = data.updated_at_formatted || 
                     (data.updated_at ? this.formatDateTime(new Date(data.updated_at)) : '');
    
    return {
      ...data,
      status_text: statusText,
      items: items,
      total_quantity: totalQuantity,
      created_at: createdAt,
      updated_at: updatedAt,
      // 确保门店和操作人信息存在
      store_name: data.store_name || '未知门店',
      store_address: data.store_address || '',
      store_phone: data.store_phone || '',
      operator_name: data.operator_name || '未知用户',
      operator_phone: data.operator_phone || ''
    };
  },

  /**
   * 格式化日期时间
   */
  formatDateTime: function(date) {
    if (!date) return '';
    
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  /**
   * 获取模拟订单详情数据（仅用于开发测试）
   */
  getMockOrderDetail: function() {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    // 根据订单类型返回不同的模拟数据
    if (this.data.orderType === 'purchase') {
      return {
        id: this.data.orderId,
        order_no: 'PO' + this.data.orderId.padStart(8, '0'),
        order_type: 'purchase',
        status: 'pending_review',
        status_text: '未审核',
        created_at: this.formatDateTime(yesterday),
        store_no: 'S001',
        store_name: '上海徐汇店',
        operator_name: '张店长',
        total_amount: '1280.00',
        items: [
          {
            id: '1',
            product_id: '101',
            product_name: '有机红富士苹果',
            product_image: '/images/icons2/默认商品.png',
            specs: '500g/袋',
            price: '15.80',
            quantity: 50,
            amount: '790.00'
          },
          {
            id: '2',
            product_id: '102',
            product_name: '进口香蕉',
            product_image: '/images/icons2/默认商品.png',
            specs: '2.5kg/箱',
            price: '35.00',
            quantity: 14,
            amount: '490.00'
          }
        ],
        total_quantity: 64
      };
    } else {
      return {
        id: this.data.orderId,
        order_no: 'TO' + this.data.orderId.padStart(8, '0'),
        order_type: 'transfer',
        status: 'pending_shipment',
        status_text: '未发货',
        created_at: this.formatDateTime(yesterday),
        store_no: 'S002',
        store_name: '北京朝阳店',
        operator_name: '李经理',
        logistics_status: '待发货',
        total_amount: '960.00',
        items: [
          {
            id: '1',
            product_id: '201',
            product_name: '有机西红柿',
            product_image: '/images/icons2/默认商品.png',
            specs: '1kg/箱',
            price: '12.00',
            quantity: 30,
            amount: '360.00'
          },
          {
            id: '2',
            product_id: '202',
            product_name: '新鲜黄瓜',
            product_image: '/images/icons2/默认商品.png',
            specs: '500g/袋',
            price: '6.00',
            quantity: 100,
            amount: '600.00'
          }
        ],
        total_quantity: 130
      };
    }
  },

  // 审核订单功能已移除 - 审核权限仅限管理端，合伙人端不应有此功能

  /**
   * 发货
   */
  shipOrder: function() {
    wx.showModal({
      title: '确认发货',
      content: '是否确认该订单已发货？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
            mask: true
          });
          
          // 模拟API调用
          setTimeout(() => {
            wx.hideLoading();
            wx.showToast({
              title: '发货成功',
              icon: 'success'
            });
            
            // 更新订单状态
            const orderDetail = this.data.orderDetail;
            orderDetail.status = 'shipped';
            orderDetail.status_text = '已发货';
            orderDetail.shipped_time = this.formatDateTime(new Date());
            orderDetail.logistics_status = '运输中';
            
            this.setData({
              orderDetail: orderDetail
            });
          }, 1500);
        }
      }
    });
  },

  /**
   * 确认到店
   */
  receiveOrder: function() {
    wx.showModal({
      title: '确认到店',
      content: '是否确认商品已到店？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
            mask: true
          });
          
          // 模拟API调用
          setTimeout(() => {
            wx.hideLoading();
            wx.showToast({
              title: '确认成功',
              icon: 'success'
            });
            
            // 更新订单状态
            const orderDetail = this.data.orderDetail;
            orderDetail.status = 'received';
            orderDetail.status_text = '已到店';
            orderDetail.received_time = this.formatDateTime(new Date());
            orderDetail.logistics_status = '已送达';
            
            this.setData({
              orderDetail: orderDetail
            });
          }, 1500);
        }
      }
    });
  }
});