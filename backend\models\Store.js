const db = require('../config/db');

class Store {
  // 自动生成门店编号：M+行政代码+顺序号
  static async generateStoreNo(regionCode) {
    // 查找该行政区下最大顺序号
    const sql = 'SELECT store_no FROM stores WHERE store_no LIKE ? ORDER BY store_no DESC LIMIT 1';
    const prefix = `M${regionCode}`;
    const rows = await db.query(sql, [`${prefix}%`]);
    let seq = 1;
    if (rows.length > 0) {
      const lastNo = rows[0].store_no;
      const lastSeq = parseInt(lastNo.slice(-3));
      seq = lastSeq + 1;
    }
    return `${prefix}${seq.toString().padStart(3, '0')}`;
  }

  static async getRegionCodeByName(province, city, district) {
    // 优先区县，找不到降级到市、省
    let sql = 'SELECT code FROM regions WHERE name = ? AND level = 3';
    let rows = await db.query(sql, [district]);
    if (rows.length > 0) return rows[0].code;
    
    // 如果没有找到区县级代码，查找市级代码
    sql = 'SELECT code FROM regions WHERE name = ? AND level = 2';
    rows = await db.query(sql, [city]);
    if (rows.length > 0) {
      let cityCode = rows[0].code;
      // 对于特殊的地级市（如东莞市、中山市、儋州市等），确保有6位数的行政区域代码
      // 这些城市没有区级行政单位，直接管辖街道/镇，需要补充到6位数
      if (cityCode.length === 4) {
        // 对于4位市级代码，补充"00"使其成为6位数，确保门店编号位数一致
        cityCode = cityCode + '00';
      }
      return cityCode;
    }
    
    // 最后查找省级代码
    sql = 'SELECT code FROM regions WHERE name = ? AND level = 1';
    rows = await db.query(sql, [province]);
    if (rows.length > 0) {
      let provinceCode = rows[0].code;
      // 对于省级代码，也需要补充到6位数
      if (provinceCode.length === 2) {
        provinceCode = provinceCode + '0000';
      }
      return provinceCode;
    }
    
    return null;
  }

  // 创建门店
  static async create({ name, level, level_title, province, city, district, contact_person, phone, image }) {
    // 查找行政区代码
    const regionCode = await this.getRegionCodeByName(province, city, district);
    if (!regionCode) throw new Error('未找到对应的行政区代码');
    
    // 生成门店编号
    const store_no = await this.generateStoreNo(regionCode);
    
    // 如果没有提供level_title，根据level自动生成
    let finalLevelTitle = level_title;
    if (!finalLevelTitle) {
      const levelTitleMap = {
        'L1': '一星门店',
        'L2': '二星门店',
        'L3': '三星门店',
        'L4': '四星门店',
        'L5': '五星门店'
      };
      finalLevelTitle = levelTitleMap[level] || '三星门店';
    }
    
    const now = Date.now();
    const sql = `INSERT INTO stores (store_no, name, level, level_title, province, city, district, contact_person, phone, image, create_time, update_time)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    const params = [store_no, name, level, finalLevelTitle, province, city, district, contact_person, phone, image, now, now];
    const result = await db.query(sql, params);
    return { success: true, id: result.insertId, store_no };
  }

  // 累加门店股本金
  static async addCapital(store_no, amount) {
    const sql = 'UPDATE stores SET capital = capital + ? WHERE store_no = ?';
    await db.query(sql, [amount, store_no]);
  }

  // 根据门店编号累加股本金
  static async addCapitalByStoreNo(store_no, amount) {
    const sql = 'UPDATE stores SET capital = capital + ? WHERE store_no = ?';
    await db.query(sql, [amount, store_no]);
  }

  // 根据门店编号获取门店信息
  static async getStoreByStoreNo(store_no) {
    const sql = 'SELECT * FROM stores WHERE store_no = ?';
    const rows = await db.query(sql, [store_no]);
    return rows.length > 0 ? rows[0] : null;
  }
}

module.exports = Store;