// 获取设备信息，避免使用已弃用的API
let device = { windowWidth: 375 }; // 默认值
try {
  device = wx.getSystemInfoSync();
} catch (e) {
  console.warn('获取系统信息失败，使用默认值:', e);
}
const width = device.windowWidth
const height = width // 1:1裁剪

Page({
  data: {
    src: '',
    cropWidth: width,
    cropHeight: width,
    imgInfo: null,
    scale: 1,
    minScale: 1,
    maxScale: 4,
    offsetX: 0,
    offsetY: 0,
    lastX: 0,
    lastY: 0,
    lastDist: 0,
    mode: '' // 'move' or 'scale'
  },
  onLoad(options) {
    if (options && options.src) {
      let src = decodeURIComponent(options.src)
      this.setData({ src })
    }
  },
  onReady() {
    this.initImage()
  },
  initImage() {
    const { src, cropWidth, cropHeight } = this.data
    if (!src) return
    wx.getImageInfo({
      src,
      success: res => {
        // 计算初始缩放比例和偏移，保证图片完整显示在裁剪区
        let scale = Math.max(cropWidth / res.width, cropHeight / res.height)
        let offsetX = (cropWidth - res.width * scale) / 2
        let offsetY = (cropHeight - res.height * scale) / 2
        this.setData({
          imgInfo: res,
          scale,
          minScale: scale,
          offsetX,
          offsetY
        }, this.drawToCanvas)
      },
      fail: () => {
        wx.showToast({ title: '图片加载失败', icon: 'none' })
      }
    })
  },
  drawToCanvas() {
    const { imgInfo, cropWidth, cropHeight, scale, offsetX, offsetY } = this.data
    if (!imgInfo) return
    const ctx = wx.createCanvasContext('cropperCanvas', this)
    ctx.clearRect(0, 0, cropWidth, cropHeight)
    ctx.drawImage(imgInfo.path, offsetX, offsetY, imgInfo.width * scale, imgInfo.height * scale)
    ctx.draw()
  },
  // 触摸事件
  touchStart(e) {
    if (e.touches.length === 1) {
      this.setData({
        mode: 'move',
        lastX: e.touches[0].x,
        lastY: e.touches[0].y
      })
    } else if (e.touches.length === 2) {
      const dist = this.getTouchDist(e.touches)
      this.setData({
        mode: 'scale',
        lastDist: dist
      })
    }
  },
  touchMove(e) {
    const { mode, lastX, lastY, lastDist, scale, minScale, maxScale, offsetX, offsetY, imgInfo, cropWidth, cropHeight } = this.data
    if (!imgInfo) return
    if (mode === 'move' && e.touches.length === 1) {
      let dx = e.touches[0].x - lastX
      let dy = e.touches[0].y - lastY
      let newOffsetX = offsetX + dx
      let newOffsetY = offsetY + dy
      // 限制拖动范围，保证裁剪区不会出现空白
      const imgW = imgInfo.width * scale
      const imgH = imgInfo.height * scale
      if (newOffsetX > 0) newOffsetX = 0
      if (newOffsetY > 0) newOffsetY = 0
      if (newOffsetX + imgW < cropWidth) newOffsetX = cropWidth - imgW
      if (newOffsetY + imgH < cropHeight) newOffsetY = cropHeight - imgH
      this.setData({
        offsetX: newOffsetX,
        offsetY: newOffsetY,
        lastX: e.touches[0].x,
        lastY: e.touches[0].y
      }, this.drawToCanvas)
    } else if (mode === 'scale' && e.touches.length === 2) {
      const dist = this.getTouchDist(e.touches)
      let newScale = scale * (dist / lastDist)
      newScale = Math.max(minScale, Math.min(maxScale, newScale))
      // 缩放时保持图片中心不变
      const centerX = cropWidth / 2
      const centerY = cropHeight / 2
      const imgW = imgInfo.width * scale
      const imgH = imgInfo.height * scale
      const imgW2 = imgInfo.width * newScale
      const imgH2 = imgInfo.height * newScale
      let newOffsetX = offsetX - (imgW2 - imgW) * (centerX - offsetX) / imgW
      let newOffsetY = offsetY - (imgH2 - imgH) * (centerY - offsetY) / imgH
      // 限制缩放后图片不能出现空白
      if (newOffsetX > 0) newOffsetX = 0
      if (newOffsetY > 0) newOffsetY = 0
      if (newOffsetX + imgW2 < cropWidth) newOffsetX = cropWidth - imgW2
      if (newOffsetY + imgH2 < cropHeight) newOffsetY = cropHeight - imgH2
      this.setData({
        scale: newScale,
        offsetX: newOffsetX,
        offsetY: newOffsetY,
        lastDist: dist
      }, this.drawToCanvas)
    }
  },
  touchEnd() {
    this.setData({ mode: '' })
  },
  getTouchDist(touches) {
    const dx = touches[0].x - touches[1].x
    const dy = touches[0].y - touches[1].y
    return Math.sqrt(dx * dx + dy * dy)
  },
  doCrop() {
    const { cropWidth, cropHeight } = this.data

    // 显示加载中提示
    wx.showLoading({
      title: '正在裁剪...',
      mask: true
    });

    wx.canvasToTempFilePath({
      canvasId: 'cropperCanvas',
      width: cropWidth,
      height: cropHeight,
      destWidth: cropWidth,
      destHeight: cropHeight,
      fileType: 'jpg',  // 指定保存格式为JPG，压缩文件大小
      quality: 0.8,     // 设置较高的质量，但不是最高，减小文件大小
      success: res => {
        // 隐藏加载中提示
        wx.hideLoading();

        console.log('图片裁剪成功，临时路径：', res.tempFilePath);

        // 获取上一个页面实例
        const pages = getCurrentPages()
        const prevPage = pages[pages.length - 2]

        // 检查上一个页面是否存在
        if (prevPage) {
          try {
            // 回传裁剪后图片
            prevPage.setData({
              coverLocalPath: res.tempFilePath,
              coverUrl: res.tempFilePath
            });

            wx.showToast({
              title: '裁剪成功',
              icon: 'success',
              duration: 1000
            });

            setTimeout(() => {
              wx.navigateBack();
            }, 1000);
          } catch (error) {
            console.error('设置上一页数据失败:', error);
            this.handleCropError('返回数据失败');
          }
        } else {
          console.error('找不到上一页面实例');
          this.handleCropError('找不到上一页面');
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('裁剪失败:', err);
        this.handleCropError('裁剪失败');
      }
    }, this)
  },

  // 处理裁剪错误
  handleCropError(message) {
    wx.showToast({
      title: message || '裁剪失败',
      icon: 'none'
    });

    // 尝试返回上一页
    setTimeout(() => {
      try {
        wx.navigateBack();
      } catch (e) {
        console.error('返回上一页失败:', e);
      }
    }, 1500);
  }
})