/* partner/publish/checkout/checkout.wxss */

.checkout-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 门店信息 */
.store-section {
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
}

.store-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 0;
}

.store-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.store-name {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
}

.store-no {
  font-size: 28rpx;
  color: #666666;
}

/* 商品列表 */
.products-section {
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
}

.products-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  gap: 20rpx;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  line-height: 1.4;
}

.product-price {
  font-size: 26rpx;
  color: #5698c3;
  font-weight: 500;
}

.product-quantity {
  font-size: 24rpx;
  color: #666666;
}

.product-subtotal {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
}

.subtotal-label {
  font-size: 24rpx;
  color: #666666;
}

.subtotal-price {
  font-size: 32rpx;
  color: #5698c3;
  font-weight: bold;
}

/* 支付方式 */
.payment-section {
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
}

.payment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
}

.payment-item:last-child {
  border-bottom: none;
}

.payment-item.selected {
  background-color: #f0f8ff;
  border-radius: 8rpx;
  padding: 24rpx;
  margin: 0 -24rpx;
}

.payment-item.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.payment-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.payment-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.payment-amount {
  font-size: 24rpx;
  color: #666666;
}

.payment-checkbox {
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.payment-checkbox image {
  width: 32rpx;
  height: 32rpx;
}

/* 长方形开关按钮样式 */
.payment-switch {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.switch-container {
  width: 80rpx;
  height: 40rpx;
  border-radius: 20rpx;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
}

.switch-off {
  background-color: #e5e5e5;
}

.switch-on {
  background-color: #5698c3;
}

.switch-button {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #ffffff;
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.switch-on .switch-button {
  left: 42rpx;
}

/* 订单提交栏 */
.submit-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;
}

.total-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.total-label {
  font-size: 28rpx;
  color: #333333;
}

.total-price {
  font-size: 36rpx;
  color: #5698c3;
  font-weight: bold;
}

.submit-btn {
  padding: 20rpx 48rpx;
  background-color: #5698c3;
  color: #ffffff;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.submit-btn:active {
  transform: scale(0.95);
  background-color: #1a8ba8;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 120rpx;
}