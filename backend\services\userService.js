/**
 * 用户服务
 */
const User = require('../models/User');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const config = require('../config');
const db = require('../config/db');
const Follow = require('../models/Follow');

class UserService {
  async login(username, password) {
    try {
      // 查找用户
      const user = await User.findByUsername(username);

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      // 验证密码
      console.log('验证密码:', password);
      console.log('用户数据:', user);

      // 尝试验证不同的密码字段
      let passwordValid = false;

      // 1. 尝试验证MD5哈希的password字段
      if (user.password) {
        const hashedPasswordMD5 = crypto.createHash('md5').update(password).digest('hex');
        passwordValid = user.password === hashedPasswordMD5;
        console.log('MD5密码验证:', passwordValid);
      }

      // 2. 尝试验证SHA-256哈希的account_password字段
      if (!passwordValid && user.account_password) {
        const hashedPasswordSHA256 = crypto.createHash('sha256').update(password + (user.password_salt || '')).digest('hex');
        passwordValid = user.account_password === hashedPasswordSHA256;
        console.log('SHA-256密码验证:', passwordValid);
      }

      if (!passwordValid) {
        return {
          success: false,
          message: '密码错误'
        };
      }

      // 生成token
      const token = jwt.sign(
        { 
          userId: user.user_id, // 使用业务ID
          username: user.username
        },
        config.jwtSecret,
        { expiresIn: config.jwtExpiration }
      );

      // 获取用户统计信息
      const stats = await User.getUserStats(user.user_id); // 使用业务ID

      return {
        success: true,
        data: {
          token,
          user: {
            id: user.user_id, // 返回业务ID作为id
            username: user.username,
            nickname: user.nickname,
            avatar: user.avatar,
            phone: user.phone,
            subscribe_store_no: user.subscribe_store_no, // 添加订阅门店编号
            ...stats
          }
        },
        message: '登录成功'
      };
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }

  async loginByPhone(phone, code, referrerId) {
    try {
      console.log('手机号登录:', phone, code, '推荐人ID:', referrerId);
      // 1. 校验验证码（5分钟内未用）
      const now = Date.now();
      const validTime = 5 * 60 * 1000;
      const verifyRes = await db.query(
        'SELECT * FROM verifycodes WHERE phone = ? AND code = ? AND used = 0 ORDER BY createTime DESC LIMIT 1',
        [phone, code]
      );
      if (!verifyRes.length || now - verifyRes[0].createTime > validTime) {
        return { success: false, message: '验证码错误或已过期' };
      }
      // 标记验证码为已用
      await db.query('UPDATE verifycodes SET used = 1 WHERE id = ?', [verifyRes[0].id]);
      // 2. 查找用户
      let user = await User.findByPhone(phone);
      if (!user) {
        const userData = {
          phone,
          username: `user_${phone.substring(phone.length - 4)}`,
          nickname: `用户${phone.substring(phone.length - 4)}`,
          avatar: '/images/icons2/男头像.png',
          // 如果有推荐人ID，添加到用户数据中
          referrerId: referrerId || null
        };
        user = await User.create(userData);
      }
      // 3. 生成token
      const token = jwt.sign(
        { 
          userId: user.user_id, // 使用业务ID
          username: user.username
        },
        config.jwtSecret,
        { expiresIn: config.jwtExpiration }
      );
      // 4. 获取用户统计信息
      const stats = await User.getUserStats(user.user_id); // 使用业务ID
      return {
        success: true,
        data: {
          token,
          user: {
            id: user.user_id, // 返回业务ID作为id
            username: user.username,
            nickname: user.nickname,
            avatar: user.avatar,
            phone: user.phone,
            subscribe_store_no: user.subscribe_store_no,
            ...stats
          }
        },
        message: '登录成功'
      };
    } catch (error) {
      console.error('手机登录失败:', error);
      throw error;
    }
  }

  async loginByWechat(code) {
    try {
      console.log('微信登录开始，code:', code);
      
      // 1. 通过微信API获取openid和session_key
      const wxLoginResult = await this.getWxSessionKey(code);
      if (!wxLoginResult.success) {
        return wxLoginResult;
      }
      
      const { openid, session_key, unionid } = wxLoginResult.data;
      console.log('获取到微信用户信息:', { openid, unionid });
      
      // 2. 查找或创建用户
      let user = await User.findByOpenid(openid);
      
      if (!user) {
        // 新用户，创建用户记录
        const userData = {
          openid,
          unionid: unionid || null,
          username: `wx_${openid.substring(openid.length - 8)}`,
          nickname: `微信用户${openid.substring(openid.length - 4)}`,
          avatar: '/images/icons2/男头像.png',
          // 检查是否有推荐人ID
          referrerId: null
        };
        
        // 检查本地存储的推荐人ID
        // 注意：这里无法直接获取前端存储的数据，需要前端在登录时传递
        // 暂时不处理推荐人逻辑，后续可以通过其他方式实现
        
        user = await User.create(userData);
        console.log('创建新微信用户:', user.user_id);
      } else {
        console.log('找到现有微信用户:', user.user_id);
      }
      
      // 3. 生成token
      const token = jwt.sign(
        { 
          userId: user.user_id, // 使用业务ID
          username: user.username
        },
        config.jwtSecret,
        { expiresIn: config.jwtExpiration }
      );
      
      // 4. 获取用户统计信息
      const stats = await User.getUserStats(user.user_id); // 使用业务ID
      
      return {
        success: true,
        data: {
          token,
          user: {
            id: user.user_id, // 返回业务ID作为id
            username: user.username,
            nickname: user.nickname,
            avatar: user.avatar,
            phone: user.phone,
            openid: user.openid,
            unionid: user.unionid,
            subscribe_store_no: user.subscribe_store_no,
            ...stats
          }
        },
        message: '微信登录成功'
      };
    } catch (error) {
      console.error('微信登录失败:', error);
      return {
        success: false,
        message: '微信登录失败，请重试'
      };
    }
  }

  async getWxSessionKey(code) {
    try {
      const appId = process.env.WX_APPID;
      const appSecret = process.env.WX_SECRET;
      
      if (!appId || !appSecret) {
        console.error('微信小程序配置缺失');
        return {
          success: false,
          message: '微信服务配置错误'
        };
      }
      
      if (appSecret === '请替换为正确的AppSecret' || appSecret.includes('请替换')) {
        console.error('微信AppSecret未正确配置');
        return {
          success: false,
          message: '微信服务配置错误'
        };
      }
      
      const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${appId}&secret=${appSecret}&js_code=${code}&grant_type=authorization_code`;
      
      console.log('请求微信登录API:', url.replace(appSecret, '***'));
      
      const axios = require('axios');
      const response = await axios.get(url, {
        timeout: 10000
      });
      
      const data = response.data;
      console.log('微信API响应:', data);
      
      if (data.errcode) {
        console.error('微信API错误:', data);
        return {
          success: false,
          message: `微信登录失败: ${data.errmsg}`
        };
      }
      
      if (!data.openid) {
        console.error('微信API返回数据异常:', data);
        return {
          success: false,
          message: '微信登录失败，未获取到用户信息'
        };
      }
      
      return {
        success: true,
        data: {
          openid: data.openid,
          session_key: data.session_key,
          unionid: data.unionid
        }
      };
    } catch (error) {
      console.error('请求微信API失败:', error);
      return {
        success: false,
        message: '网络错误，请重试'
      };
    }
  }

  async register(userData) {
    try {
      // 检查用户名是否已存在
      const existingUser = await User.findByUsername(userData.username);
      if (existingUser) {
        return {
          success: false,
          message: '用户名已存在'
        };
      }

      // 检查手机号是否已存在
      if (userData.phone) {
        const existingPhone = await User.findByPhone(userData.phone);
        if (existingPhone) {
          return {
            success: false,
            message: '手机号已被注册'
          };
        }
      }

      // 自动写入推荐人字段
      if (userData.referrerId) {
        userData.referrerId = userData.referrerId;
      } else if (userData.scene) {
        userData.referrerId = userData.scene;
      }

      // 自动写入订阅门店
      if (userData.subscribe_store_no) {
        // 优先用前端传递的门店参数
        userData.subscribe_store_no = userData.subscribe_store_no;
      } else if (userData.referrerId) {
        // 查推荐人
        const refUser = await User.findById(userData.referrerId);
        if (refUser && refUser.subscribe_store_no) {
          userData.subscribe_store_no = refUser.subscribe_store_no;
        } else {
          userData.subscribe_store_no = null;
        }
      } else {
        userData.subscribe_store_no = null;
      }

      // 创建用户
      const user = await User.create(userData);

      // 生成token
      const token = jwt.sign(
        { userId: user.user_id, username: user.username }, // 使用业务ID
        config.jwtSecret,
        { expiresIn: config.jwtExpiration }
      );

      return {
        success: true,
        data: {
          token,
          user: {
            id: user.user_id, // 返回业务ID作为id
            username: user.username,
            nickname: user.nickname,
            avatar: user.avatar,
            phone: user.phone,
            subscribe_store_no: user.subscribe_store_no
          }
        },
        message: '注册成功'
      };
    } catch (error) {
      console.error('注册失败:', error);
      throw error;
    }
  }

  async getUserInfo(userId) {
    try {
      // 优先通过user_id查找用户
      let user = await User.findByUserId(userId);
      
      // 如果没找到，尝试通过自增id查找（向后兼容）
      if (!user) {
        user = await User.findById(userId);
      }

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      // 获取用户统计信息
      const stats = await User.getUserStats(user.user_id);

      // 获取用户资金账户信息
      const UserFundAccount = require('../models/UserFundAccount');
      const fundAccount = await UserFundAccount.getOrCreate(user.user_id);

      return {
        success: true,
        data: {
          id: user.user_id, // 返回业务ID作为id
          _id: user.id, // 返回自增ID作为_id
          username: user.username,
          nickname: user.nickname,
          avatar: user.avatar,
          phone: user.phone,
          email: user.email,
          gender: user.gender,
          country: user.country,
          province: user.province,
          city: user.city,
          openid: user.openid,
          role: user.role,
          createTime: user.createTime,
          referrerId: user.referrerId,
          salesmanId: user.salesman_id,
          subscribedStore: user.subscribe_store_no,
          accountBalance: fundAccount.account_balance,
          ...stats
        },
        message: '获取用户信息成功'
      };
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }

  async updateUserInfo(userId, userData) {
    try {
      // 优先通过user_id查找用户
      let user = await User.findByUserId(userId);
      
      // 如果没找到，尝试通过自增id查找（向后兼容）
      if (!user) {
        user = await User.findById(userId);
      }

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      // 如果要更新用户名，检查用户名是否已存在
      if (userData.username && userData.username !== user.username) {
        const existingUser = await User.findByUsername(userData.username);
        if (existingUser && existingUser.user_id !== user.user_id) {
          return {
            success: false,
            message: '用户名已存在，请选择其他用户名'
          };
        }
      }

      // 如果要更新密码，直接更新（用户已通过手机验证码验证身份）
      if (userData.newPassword) {
        const crypto = require('crypto');
        
        // 加密新密码
        userData.password = crypto.createHash('md5').update(userData.newPassword).digest('hex');
        
        // 删除临时字段
        delete userData.oldPassword;
        delete userData.newPassword;
      }

      // 如果有手机号和验证码，需要验证验证码
      if (userData.phone && userData.verifyCode) {
        // 在实际环境中，这里应该验证验证码是否正确
        // 测试环境下，验证码123456始终通过
        if (userData.verifyCode !== '123456') {
          return {
            success: false,
            message: '验证码错误'
          };
        }

        // 验证通过后，删除验证码字段，不保存到数据库
        delete userData.verifyCode;
      }

      // 更新用户信息
      const updatedUser = await User.update(user.user_id, userData);

      return {
        success: true,
        data: updatedUser,
        message: '更新用户信息成功'
      };
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  }

  // 管理员更新用户信息（包括余额调整）
  async adminUpdateUser(userId, userData) {
    try {
      // 优先通过user_id查找用户
      let user = await User.findByUserId(userId);
      
      // 如果没找到，尝试通过自增id查找（向后兼容）
      if (!user) {
        user = await User.findById(userId);
      }

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      // 处理余额调整
      if (userData.balanceAdjustment) {
        const UserFundAccount = require('../models/UserFundAccount');
        const { amount, reason } = userData.balanceAdjustment;
        
        // 获取当前账户信息
        const account = await UserFundAccount.getOrCreate(user.user_id);
        const newBalance = parseFloat(account.account_balance) + parseFloat(amount);
        
        // 更新账户余额
        await UserFundAccount.update(user.user_id, {
          account_balance: newBalance
        });
        
        // 添加资金变动记录
        await UserFundAccount.addFundRecord({
          user_id: user.user_id,
          type: amount > 0 ? 'admin_recharge' : 'admin_deduct',
          amount: Math.abs(amount),
          balance: newBalance,
          description: `管理员调整: ${reason}`
        });
        
        delete userData.balanceAdjustment; // 从userData中移除，避免更新users表
      }

      // 更新用户基本信息
      const updateFields = {};
      if (userData.referrerId !== undefined) updateFields.referrerId = userData.referrerId;
      if (userData.salesmanId !== undefined) updateFields.salesman_id = userData.salesmanId;
      if (userData.subscribedStore !== undefined) updateFields.subscribe_store_no = userData.subscribedStore;

      if (Object.keys(updateFields).length > 0) {
        await User.update(user.user_id, updateFields);
      }

      // 获取更新后的用户信息
      const updatedUser = await User.findById(user.user_id);
      const stats = await User.getUserStats(user.user_id);

      return {
        success: true,
        data: {
          id: updatedUser.user_id, // 使用业务ID
          username: updatedUser.username,
          nickname: updatedUser.nickname,
          avatar: updatedUser.avatar,
          phone: updatedUser.phone,
          email: updatedUser.email,
          gender: updatedUser.gender,
          country: updatedUser.country,
          province: updatedUser.province,
          city: updatedUser.city,
          openid: updatedUser.openid,
          role: updatedUser.role,
          createTime: updatedUser.createTime,
          referrerId: updatedUser.referrerId,
          salesmanId: updatedUser.salesman_id,
          subscribedStore: updatedUser.subscribe_store_no,
          ...stats
        },
        message: '更新用户信息成功'
      };
    } catch (error) {
      console.error('管理员更新用户信息失败:', error);
      throw error;
    }
  }

  async sendVerificationCode(phone) {
    try {
      // 生成6位随机验证码
      const code = Math.floor(100000 + Math.random() * 900000).toString();
      const now = Date.now();

      // 检查是否存在验证码表
      try {
        await db.query('SELECT 1 FROM verifycodes LIMIT 1');
      } catch (error) {
        // 如果表不存在，创建表
        console.log('验证码表不存在，创建表');
        await db.query(`
          CREATE TABLE IF NOT EXISTS verifycodes (
            id INTEGER PRIMARY KEY AUTO_INCREMENT,
            phone VARCHAR(20) NOT NULL,
            code VARCHAR(10) NOT NULL,
            createTime BIGINT NOT NULL,
            used BOOLEAN DEFAULT 0
          )
        `);
      }

      // 保存验证码到数据库
      await db.query(
        'INSERT INTO verifycodes (phone, code, createTime) VALUES (?, ?, ?)',
        [phone, code, now]
      );

      console.log('验证码已生成:', code);

      // 在实际环境中，这里应该调用短信服务发送验证码
      // 但在测试环境中，我们直接返回验证码
      return {
        success: true,
        data: { code },
        message: '验证码发送成功'
      };
    } catch (error) {
      console.error('发送验证码失败:', error);
      throw error;
    }
  }

  async updatePassword(userId, oldPassword, newPassword) {
    try {
      // 优先通过user_id查找用户
      let user = await User.findByUserId(userId);
      
      // 如果没找到，尝试通过自增id查找（向后兼容）
      if (!user) {
        user = await User.findById(userId);
      }

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      // 验证旧密码
      const oldPasswordHash = crypto.createHash('md5').update(oldPassword).digest('hex');
      if (user.password !== oldPasswordHash && user.account_password !== oldPasswordHash) {
        return {
          success: false,
          message: '原密码错误'
        };
      }

      // 生成新密码哈希
      const newPasswordHash = crypto.createHash('md5').update(newPassword).digest('hex');
      const passwordSalt = crypto.randomBytes(16).toString('hex');

      // 更新密码
      const db = require('../config/db');
      const sql = 'UPDATE users SET password = ?, account_password = ?, password_salt = ? WHERE user_id = ?';
      await db.query(sql, [newPasswordHash, newPasswordHash, passwordSalt, user.user_id]);

      return {
        success: true,
        message: '密码修改成功'
      };
    } catch (error) {
      console.error('修改密码失败:', error);
      throw error;
    }
  }

  async bindWechat(userId, code, userInfo) {
    try {
      // 查找用户
      const user = await User.findById(userId);

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      // 在实际环境中，这里应该调用微信API获取openid
      // 这里简化处理，直接使用code作为openid
      const openid = code;

      // 更新用户信息
      await User.update(userId, {
        openid,
        // 如果用户没有头像，使用微信头像
        avatar: user.avatar || userInfo.avatarUrl,
        // 如果用户没有昵称，使用微信昵称
        nickname: user.nickname || userInfo.nickName
      });

      return {
        success: true,
        data: { openid },
        message: '微信绑定成功'
      };
    } catch (error) {
      console.error('绑定微信失败:', error);
      throw error;
    }
  }

  async unbindWechat(userId) {
    try {
      // 查找用户
      const user = await User.findById(userId);

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      // 更新用户信息
      await User.update(userId, { openid: null });

      return {
        success: true,
        message: '微信解绑成功'
      };
    } catch (error) {
      console.error('解绑微信失败:', error);
      throw error;
    }
  }

  async sendVerifyCode(phone) {
    try {
      console.log('发送验证码到手机:', phone);

      // 允许新手机号直接发送验证码，不再校验用户是否已注册
      // const user = await User.findByPhone(phone);
      // if (!user) {
      //   return {
      //     success: false,
      //     message: '该手机号未注册'
      //   };
      // }

      // 生成6位随机验证码
      const code = Math.floor(100000 + Math.random() * 900000).toString();
      const now = Date.now();

      // 检查是否存在验证码表
      try {
        await db.query('SELECT 1 FROM verifycodes LIMIT 1');
      } catch (error) {
        // 如果表不存在，创建表
        console.log('验证码表不存在，创建表');
        await db.query(`
          CREATE TABLE IF NOT EXISTS verifycodes (
            id INTEGER PRIMARY KEY AUTO_INCREMENT,
            phone VARCHAR(20) NOT NULL,
            code VARCHAR(10) NOT NULL,
            createTime BIGINT NOT NULL,
            used BOOLEAN DEFAULT 0
          )
        `);
      }

      // 保存验证码到数据库
      await db.query(
        'INSERT INTO verifycodes (phone, code, createTime) VALUES (?, ?, ?)',
        [phone, code, now]
      );

      console.log('验证码已生成:', code);

      // 在实际环境中，这里应该调用短信服务发送验证码
      // 但在测试环境中，我们直接返回验证码
      return {
        success: true,
        message: '验证码已发送',
        data: {
          code: code // 仅在测试环境返回验证码
        }
      };
    } catch (error) {
      console.error('发送验证码失败:', error);
      throw error;
    }
  }

  async resetPasswordByPhone(phone, code, newPassword) {
    try {
      console.log('通过手机号重置密码:', phone);

      // 验证手机号是否存在
      const user = await User.findByPhone(phone);
      if (!user) {
        return {
          success: false,
          message: '该手机号未注册'
        };
      }

      // 验证验证码
      // 在实际环境中，应该验证验证码是否正确
      // 测试环境下，验证码123456始终通过
      if (code !== '123456') {
        // 查询验证码表
        const verifyCodeResult = await db.query(
          'SELECT * FROM verifycodes WHERE phone = ? ORDER BY createTime DESC LIMIT 1',
          [phone]
        );

        let isCodeValid = false;
        if (verifyCodeResult.length > 0) {
          const verifyCode = verifyCodeResult[0];
          // 验证码有效期为5分钟
          const validTime = 5 * 60 * 1000;
          const now = Date.now();
          const createTime = verifyCode.createTime;

          if (now - createTime <= validTime && verifyCode.code === code) {
            isCodeValid = true;

            // 标记验证码为已使用
            await db.query(
              'UPDATE verifycodes SET used = 1 WHERE phone = ? AND code = ?',
              [phone, code]
            );
          }
        }

        if (!isCodeValid) {
          return {
            success: false,
            message: '验证码错误或已过期'
          };
        }
      }

      // 更新密码 - 同时更新两个密码字段
      const hashedNewPasswordMD5 = crypto.createHash('md5').update(newPassword).digest('hex');

      // 生成随机盐值
      const salt = crypto.randomBytes(16).toString('hex');

      // 使用SHA-256哈希新密码
      const hashedNewPasswordSHA256 = crypto.createHash('sha256')
        .update(newPassword + salt)
        .digest('hex');

      // 直接使用SQL更新，确保更新成功
      const sql = 'UPDATE users SET password = ?, account_password = ?, password_salt = ? WHERE user_id = ?';
      const params = [hashedNewPasswordMD5, hashedNewPasswordSHA256, salt, user.user_id];

      console.log('重置密码 - 执行SQL更新:', sql);
      console.log('重置密码 - 参数:', params);

      await db.query(sql, params);

      console.log('重置密码 - 密码已更新');

      return {
        success: true,
        message: '密码重置成功'
      };
    } catch (error) {
      console.error('重置密码失败:', error);
      throw error;
    }
  }

  async followUser(followerId, followingId) {
    try {
      // 检查用户是否存在
      const followingUser = await User.findById(followingId);
      if (!followingUser) {
        return {
          success: false,
          message: '被关注的用户不存在'
        };
      }
      return await Follow.follow(followerId, followingId);
    } catch (error) {
      console.error('关注用户失败:', error);
      return {
        success: false,
        message: '服务异常: ' + (error && error.message ? error.message : String(error))
      };
    }
  }

  async unfollowUser(followerId, followingId) {
    try {
      return await Follow.unfollow(followerId, followingId);
    } catch (error) {
      console.error('取消关注失败:', error);
      throw error;
    }
  }

  async checkFollowStatus(followerId, followingId) {
    try {
      // 参数校验，未登录或参数缺失时直接返回未关注
      if (!followerId || !followingId) {
        return {
          success: true,
          data: { isFollowing: false },
          message: '未登录或参数缺失，默认未关注'
        };
      }
      const isFollowing = await Follow.isFollowing(followerId, followingId);
      return {
        success: true,
        data: { isFollowing },
        message: '获取关注状态成功'
      };
    } catch (error) {
      console.error('获取关注状态失败:', error);
      // 异常时也返回未关注，避免前端报错
      return {
        success: true,
        data: { isFollowing: false },
        message: '获取关注状态异常，默认未关注'
      };
    }
  }

  async getUserStats(userId) {
    try {
      // 优先通过user_id查找用户
      let user = await User.findByUserId(userId);
      
      // 如果没找到，尝试通过自增id查找（向后兼容）
      if (!user) {
        user = await User.findById(userId);
      }

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      const stats = await User.getUserStats(user.user_id);

      return {
        success: true,
        data: stats,
        message: '获取用户统计信息成功'
      };
    } catch (error) {
      console.error('获取用户统计信息失败:', error);
      throw error;
    }
  }

  async getMyFollowList(userId) {
    try {
      // 使用 JOIN 查询直接获取关注用户的详细信息
      const users = await db.query(`
        SELECT
          u.id,
          u.nickname,
          u.nickName,
          u.avatar,
          u.username,
          u.phone,
          u.createTime
        FROM follows f
        INNER JOIN users u ON f.followingId = u.user_id
        WHERE f.followerId = ?
        ORDER BY f.createTime DESC
      `, [userId]);

      return {
        success: true,
        data: users.map(user => ({
          ...user,
          nickname: user.nickname || user.nickName || '未知用户' // 确保昵称字段统一
        }))
      };
    } catch (error) {
      console.error('获取我的关注列表失败:', error);
      return {
        success: false,
        message: '服务异常: ' + (error && error.message ? error.message : String(error))
      };
    }
  }

  async getMyFansList(userId) {
    try {
      // 使用 JOIN 查询直接获取粉丝用户的详细信息
      const users = await db.query(`
        SELECT
          u.id,
          u.nickname,
          u.nickName,
          u.avatar,
          u.username,
          u.phone,
          u.createTime
        FROM follows f
        INNER JOIN users u ON f.followerId = u.user_id
        WHERE f.followingId = ?
        ORDER BY f.createTime DESC
      `, [userId]);

      return {
        success: true,
        data: users.map(user => ({
          ...user,
          nickname: user.nickname || user.nickName || '未知用户'
        }))
      };
    } catch (error) {
      console.error('获取我的粉丝列表失败:', error);
      return {
        success: false,
        message: '服务异常: ' + (error && error.message ? error.message : String(error))
      };
    }
  }

  async getMyPromotionList(userId) {
    try {
      const users = await db.query(`
        SELECT id, nickname, nickName, avatar, createTime
        FROM users
        WHERE referrerId = ?
        ORDER BY createTime DESC
      `, [userId]);
      return {
        success: true,
        data: users.map(user => ({
          ...user,
          nickname: user.nickname || user.nickName || '未知用户'
        }))
      };
    } catch (error) {
      console.error('获取我的推广用户列表失败:', error);
      return {
        success: false,
        message: '服务异常: ' + (error && error.message ? error.message : String(error))
      };
    }
  }







  async getUserRoles(userId, clientType = null) {
    try {
      const db = require('../config/db');
      
      console.log('[后端调试] 开始获取用户角色，用户ID:', userId, '客户端类型:', clientType);
      
      // 优先通过user_id查找用户
      let user = await User.findByUserId(userId);
      
      // 如果没找到，尝试通过自增id查找（向后兼容）
      if (!user) {
        user = await User.findById(userId);
      }

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      const actualUserId = user.user_id;
      
      // 根据业务文档：通过users表的isPartner和isAdmin字段判断多重身份
      console.log('[后端调试] 用户基本信息:', {
        user_id: user.user_id,
        isPartner: user.isPartner,
        isAdmin: user.isAdmin,
        role_type: user.role_type
      });
      
      // 查询用户的所有角色（从user_roles表）
      const roles = await db.query('SELECT role_type, role_name FROM user_roles WHERE user_id = ?', [actualUserId]);
      console.log('[后端调试] user_roles表中的角色数据:', roles);
      
      // 构建完整的角色列表
      const formattedRoles = [];
      
      // 1. 顾客身份是每个用户的天然身份，无需判断
      // 查找顾客角色的具体级别（从user_roles表）
      const customerRole = roles.find(r => r.role_type === 'customer');
      if (customerRole) {
        formattedRoles.push({
          role_type: 'customer',
          role_name: customerRole.role_name || 'customer' // 如果role_name为空，使用role_type作为备用
        });
      } else {
        console.warn(`[警告] 用户 ${actualUserId} 在user_roles表中缺少customer角色记录`);
        // 添加默认顾客角色，使用role_type作为备用
        formattedRoles.push({
          role_type: 'customer',
          role_name: 'customer'
        });
      }
      
      // 2. 根据users表的isPartner字段判断是否具有合伙人身份
      if (user.isPartner === 1 || user.isPartner === true) {
        // 查找合伙人角色的具体级别
        const partnerRole = roles.find(r => r.role_type === 'partner');
        if (partnerRole) {
          formattedRoles.push({
            role_type: 'partner',
            role_name: partnerRole.role_name || 'partner' // 如果role_name为空，使用role_type作为备用
          });
          console.log('[后端调试] 用户具有合伙人身份');
        } else {
          console.warn(`[警告] 用户 ${actualUserId} 在users表中标记为合伙人，但在user_roles表中缺少partner角色记录`);
          // 添加默认合伙人角色，使用role_type作为备用
          formattedRoles.push({
            role_type: 'partner',
            role_name: 'partner'
          });
        }
      }
      
      // 3. 根据users表的isAdmin字段判断是否具有管理员身份
      if (user.isAdmin === 1 || user.isAdmin === true) {
        // 查找管理员角色的具体级别
        const adminRole = roles.find(r => r.role_type === 'admin');
        if (adminRole) {
          formattedRoles.push({
            role_type: 'admin',
            role_name: adminRole.role_name || 'admin' // 如果role_name为空，使用role_type作为备用
          });
          console.log('[后端调试] 用户具有管理员身份');
        } else {
          console.warn(`[警告] 用户 ${actualUserId} 在users表中标记为管理员，但在user_roles表中缺少admin角色记录`);
          // 添加默认管理员角色，使用role_type作为备用
          formattedRoles.push({
            role_type: 'admin',
            role_name: 'admin'
          });
        }
      }
      
      // 4. 处理其他可能的角色（如VIP等）
      roles.forEach(role => {
        if (!['customer', 'partner', 'admin'].includes(role.role_type)) {
          formattedRoles.push({
            role_type: role.role_type,
            role_name: role.role_name || role.role_type
          });
        }
      });
      
      // 5. 如果用户表中有role_type字段且不在已有角色中，也加入
      if (user.role_type && !formattedRoles.find(r => r.role_type === user.role_type)) {
        // 从user_roles表中查找对应的role_name，如果找不到则使用role_type作为备用
        const userTableRole = roles.find(r => r.role_type === user.role_type);
        if (userTableRole) {
          formattedRoles.push({
            role_type: user.role_type,
            role_name: userTableRole.role_name || user.role_type // 如果role_name为空，使用role_type作为备用
          });
        } else {
          console.warn(`[警告] 用户 ${actualUserId} 在users表中有role_type=${user.role_type}，但在user_roles表中找不到对应记录`);
          // 添加角色，使用role_type作为备用
          formattedRoles.push({
            role_type: user.role_type,
            role_name: user.role_type
          });
        }
      }
      
      console.log('[后端调试] 最终格式化后的角色数据:', formattedRoles);
      
      // 根据客户端类型确定当前角色
      let currentRole = null;
      
      if (clientType) {
        // 根据客户端类型选择对应的角色
        if (clientType === 'admin') {
          // 管理端：显示管理员身份
          const adminRole = formattedRoles.find(r => r.role_type === 'admin');
          if (adminRole) {
            currentRole = adminRole;
            console.log('[后端调试] 管理端：选择管理员角色作为当前角色');
          } else {
            console.log('[后端调试] 管理端：用户没有管理员权限');
            return {
              success: false,
              message: '用户没有管理员权限'
            };
          }
        } else if (clientType === 'partner') {
          // 合伙人端：显示合伙人身份
          const partnerRole = formattedRoles.find(r => r.role_type === 'partner');
          if (partnerRole) {
            currentRole = partnerRole;
            console.log('[后端调试] 合伙人端：选择合伙人角色作为当前角色');
          } else {
            console.log('[后端调试] 合伙人端：用户没有合伙人权限');
            return {
              success: false,
              message: '用户没有合伙人权限'
            };
          }
        } else if (clientType === 'customer') {
          // 顾客端：显示顾客身份（如果有多个级别，显示最新的级别）
          const customerRole = formattedRoles.find(r => r.role_type === 'customer');
          currentRole = customerRole;
          console.log('[后端调试] 顾客端：选择顾客角色作为当前角色');
        }
      }
      
      // 如果没有指定客户端类型或没有找到对应角色，使用默认逻辑（向后兼容）
      if (!currentRole) {
        // 优先级：管理员 > 合伙人 > 其他角色 > 顾客
        if (formattedRoles.find(r => r.role_type === 'admin')) {
          currentRole = formattedRoles.find(r => r.role_type === 'admin');
          console.log('[后端调试] 默认逻辑：自动选择管理员角色作为当前角色');
        } else if (formattedRoles.find(r => r.role_type === 'partner')) {
          currentRole = formattedRoles.find(r => r.role_type === 'partner');
          console.log('[后端调试] 默认逻辑：自动选择合伙人角色作为当前角色');
        } else {
          // 选择第一个非顾客角色，如果没有则选择顾客
          const nonCustomerRole = formattedRoles.find(r => r.role_type !== 'customer');
          currentRole = nonCustomerRole || formattedRoles.find(r => r.role_type === 'customer');
          console.log('[后端调试] 默认逻辑：自动选择角色作为当前角色:', currentRole?.role_type);
        }
      }
      
      console.log('[后端调试] 最终返回的数据:', {
        userId: actualUserId,
        roles: formattedRoles,
        currentRole: currentRole
      });
      
      return {
        success: true,
        data: {
          userId: actualUserId,
          roles: formattedRoles,
          currentRole: currentRole
        },
        message: '获取用户多重身份成功'
      };
    } catch (error) {
      console.error('获取用户多重身份失败:', error);
      throw error;
    }
  }

  async getUserWallet(userId) {
    try {
      const db = require('../config/db');
      
      // 获取用户积分
      const pointsResult = await db.query('SELECT points FROM user_points WHERE user_id = ?', [userId]);
      const points = pointsResult.length > 0 ? pointsResult[0].points : 0;
      
      // 获取用户资金账户信息
      const fundResult = await db.query('SELECT account_balance FROM user_fund_accounts WHERE user_id = ?', [userId]);
      const balance = fundResult.length > 0 ? parseFloat(fundResult[0].account_balance) : 0;
      
      // 获取红包数量（暂时设为0，后续可以扩展）
      const redPacketCount = 0;
      
      // 获取卡券数量（暂时设为0，后续可以扩展）
      const couponCount = 0;
      
      return {
        success: true,
        data: {
          balance: balance,
          redPacket: redPacketCount,
          coupon: couponCount,
          points: points
        },
        message: '获取用户钱包信息成功'
      };
    } catch (error) {
      console.error('获取用户钱包信息失败:', error);
      throw error;
    }
  }

  async getUserFundRecords(userId, limit = 20, offset = 0) {
    try {
      const db = require('../config/db');
      
      // 获取用户资金变动记录
      const records = await db.query(
        'SELECT * FROM user_fund_records WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?',
        [userId, limit, offset]
      );

      // 格式化记录数据
      const formattedRecords = records.map(record => ({
        id: record.id,
        type: record.type,
        amount: parseFloat(record.amount || 0),
        balance: parseFloat(record.balance || 0),
        description: record.description,
        order_id: record.order_id,
        store_no: record.store_no,
        status: record.status,
        created_at: record.created_at
      }));

      return {
        success: true,
        data: formattedRecords,
        message: '获取用户资金记录成功'
      };
    } catch (error) {
      console.error('获取用户资金记录失败:', error);
      throw error;
    }
  }

  // 新增：变更销售人并同步订阅门店
  async changeSalesmanAndStore(userId, newSalesmanId) {
    if (!newSalesmanId) {
      // 没有销售人，订阅门店也设为null
      await User.update(userId, { salesman_id: null, subscribe_store_no: null });
      return { success: true, newSalesmanId: null, newStoreNo: null };
    }
    // 查找新销售人最早加入的门店store_no
    const rows = await db.query('SELECT store_no FROM partners WHERE user_id = ? ORDER BY created_at ASC LIMIT 1', [newSalesmanId]);
    let newStoreNo = null;
    if (rows.length > 0) {
      newStoreNo = rows[0].store_no;
    }
    // 更新用户的销售人和订阅门店
    const updateData = { salesman_id: newSalesmanId };
    if (newStoreNo) updateData.subscribe_store_no = newStoreNo;
    else updateData.subscribe_store_no = null;
    await User.update(userId, updateData);
    return { success: true, newSalesmanId, newStoreNo };
  }
}

module.exports = new UserService();
