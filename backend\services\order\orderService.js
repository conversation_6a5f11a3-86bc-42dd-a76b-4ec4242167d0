const db = require('../../config/db');

/**
 * 订单通用服务层
 * 提供各端控制器共用的业务逻辑
 */
class OrderService {
  /**
   * 生成顾客订单号
   * 顾客订单编号规则：字母"XS"+年月日时分（12位数）+顺序号（4位数）
   */
  static generateCustomerOrderNo() {
    const now = new Date();

    // 格式化年月日时分：YYYYMMDDHHMM
    const year = now.getFullYear().toString();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hour = now.getHours().toString().padStart(2, '0');
    const minute = now.getMinutes().toString().padStart(2, '0');
    const dateTimeStr = year + month + day + hour + minute; // 12位数

    // 生成4位顺序号（基于毫秒数的后4位）
    const sequence = (now.getMilliseconds() + Math.floor(Math.random() * 1000)).toString().slice(-4).padStart(4, '0');

    return `XS${dateTimeStr}${sequence}`;
  }

  /**
   * 生成子订单号
   * 子订单编号规则：字母"ZD"+年月日时分（12位数）+顺序号（4位数）
   */
  static generateSubOrderNo() {
    const now = new Date();

    // 格式化年月日时分：YYYYMMDDHHMM
    const year = now.getFullYear().toString();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hour = now.getHours().toString().padStart(2, '0');
    const minute = now.getMinutes().toString().padStart(2, '0');
    const dateTimeStr = year + month + day + hour + minute; // 12位数

    // 生成4位顺序号（基于毫秒数的后4位）
    const sequence = (now.getMilliseconds() + Math.floor(Math.random() * 1000)).toString().slice(-4).padStart(4, '0');

    return `ZD${dateTimeStr}${sequence}`;
  }

  /**
   * 生成门店订单号
   * 采购订单：CG + 年月日时分 + 4位随机数
   * 移库订单：YK + 年月日时分 + 4位随机数
   */
  static generateStoreOrderNo(type) {
    const now = new Date();
    const dateStr = now.getFullYear().toString() + 
                    (now.getMonth() + 1).toString().padStart(2, '0') + 
                    now.getDate().toString().padStart(2, '0') + 
                    now.getHours().toString().padStart(2, '0') + 
                    now.getMinutes().toString().padStart(2, '0');
    
    const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    
    const prefix = type === 'purchase' ? 'CG' : 'YK';
    return prefix + dateStr + randomNum;
  }

  /**
   * 安全解析支付方式JSON
   */
  static parsePaymentMethods(paymentMethodsStr) {
    if (!paymentMethodsStr) {
      return [];
    }
    
    try {
      // 如果已经是数组，直接返回
      if (Array.isArray(paymentMethodsStr)) {
        return paymentMethodsStr;
      }
      
      // 尝试解析JSON
      const parsed = JSON.parse(paymentMethodsStr);
      return Array.isArray(parsed) ? parsed : [parsed];
    } catch (error) {
      console.warn('支付方式JSON解析失败:', paymentMethodsStr, error.message);
      // 如果解析失败，返回默认值
      return ['微信支付'];
    }
  }

  /**
   * 获取支付方式文本
   */
  static getPaymentMethodText(paymentMethodsStr) {
    const methods = this.parsePaymentMethods(paymentMethodsStr);
    return methods.length > 0 ? methods.join(', ') : '微信支付';
  }

  /**
   * 格式化日期
   */
  static formatDate(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return `${date.getFullYear()}-${this.padZero(date.getMonth() + 1)}-${this.padZero(date.getDate())} ${this.padZero(date.getHours())}:${this.padZero(date.getMinutes())}:${this.padZero(date.getSeconds())}`;
  }

  /**
   * 补零
   */
  static padZero(num) {
    return num < 10 ? `0${num}` : num;
  }

  /**
   * 获取订单状态码映射
   */
  static getStatusCodeMap() {
    return {
      '待支付': 'pending_payment', // 修改为与前端一致的文本
      '待发货': 'pending_shipment',
      '待收货': 'shipped',
      '已完成': 'completed',
      '已取消': 'cancelled',
      '退款/售后': 'refund',
      '待审核': 'pending_review'
    };
  }

  /**
   * 获取订单状态文本
   * @param {string} status - 订单状态码
   * @returns {string} 状态文本
   */
  static getStatusText(status) {
    const statusMap = {
      '待支付': '待支付', // 数据库中文状态直接返回
      'pending_payment': '待支付', // 兼容英文状态码
      'pending_shipment': '待发货', 
      'shipped': '待收货',
      'completed': '已完成',
      'cancelled': '已取消',
      'refund': '退款/售后',
      'pending_review': '待审核'
    };
    return statusMap[status] || status || '未知状态';
  }

  /**
   * 获取用户信息
   */
  static async getUserInfo(userId) {
    const User = require('../../models/User');
    return await User.findByUserId(userId);
  }

  /**
   * 验证订单权限
   */
  static async validateOrderPermission(orderId, userId, orderType = 'customer') {
    let query;
    if (orderType === 'customer') {
      query = 'SELECT * FROM customer_orders WHERE id = ? AND user_id = ?';
    } else if (orderType === 'store') {
      query = 'SELECT * FROM store_orders WHERE id = ? AND user_id = ?';
    } else {
      throw new Error('无效的订单类型');
    }
    
    const orders = await db.query(query, [orderId, userId]);
    return orders.length > 0 ? orders[0] : null;
  }
}

module.exports = OrderService;