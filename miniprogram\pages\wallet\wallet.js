// pages/wallet/wallet.js
const { userApi } = require('../../utils/api');

Page({
  data: {
    loading: true,
    walletInfo: {
      balance: 0,
      redPacket: 0,
      coupon: 0,
      points: 0
    },
    activeTab: 'balance', // 当前选中的标签：balance, redPacket, coupon, card, points
    tabs: [
      { key: 'balance', name: '余额', value: 0 },
      { key: 'redPacket', name: '红包', value: 0 },
      { key: 'card', name: '卡', value: 0 },
      { key: 'coupon', name: '优惠券', value: 0 },
      { key: 'points', name: '积分', value: 0 }
    ],
    fundRecords: [], // 资金变动记录
    loadingRecords: false,
    hasMoreRecords: true,
    page: 1,
    pageSize: 20
  },

  onLoad: function(options) {
    console.log('钱包页面加载');
    this.loadWalletInfo();
    this.loadFundRecords();
  },

  onShow: function() {
    // 页面显示时刷新数据
    this.loadWalletInfo();
  },

  // 加载钱包信息
  loadWalletInfo: function() {
    this.setData({ loading: true });
    
    userApi.getUserWallet().then(res => {
      if (res.success && res.data) {
        const walletInfo = res.data;
        const tabs = this.data.tabs.map(tab => {
          let value = 0;
          switch(tab.key) {
            case 'balance':
              value = walletInfo.balance || 0;
              break;
            case 'redPacket':
              value = walletInfo.redPacket || 0;
              break;
            case 'card':
              value = walletInfo.card || 0;
              break;
            case 'coupon':
              value = walletInfo.coupon || 0;
              break;
            case 'points':
              value = walletInfo.points || 0;
              break;
          }
          return { ...tab, value };
        });

        this.setData({
          walletInfo,
          tabs,
          loading: false
        });
      } else {
        this.setData({ loading: false });
        wx.showToast({
          title: '获取钱包信息失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取钱包信息失败:', err);
      this.setData({ loading: false });
      wx.showToast({
        title: '获取钱包信息失败',
        icon: 'none'
      });
    });
  },

  // 加载资金记录
  loadFundRecords: function(refresh = false) {
    if (refresh) {
      this.setData({ 
        fundRecords: [], 
        page: 1, 
        hasMoreRecords: true 
      });
    }

    if (!this.data.hasMoreRecords || this.data.loadingRecords) {
      return;
    }

    this.setData({ loadingRecords: true });

    const offset = (this.data.page - 1) * this.data.pageSize;
    
    userApi.getUserFundRecords(this.data.pageSize, offset).then(res => {
      console.log('获取资金记录响应:', res);
      if (res.success && res.data) {
        // 格式化记录数据，特别是时间格式
        const formattedRecords = res.data.map(record => ({
          ...record,
          created_at: this.formatTime(record.created_at),
          amount: parseFloat(record.amount || 0).toFixed(2),
          balance: parseFloat(record.balance || 0).toFixed(2)
        }));
        
        const newRecords = refresh ? formattedRecords : [...this.data.fundRecords, ...formattedRecords];
        
        this.setData({
          fundRecords: newRecords,
          loadingRecords: false,
          hasMoreRecords: res.data.length === this.data.pageSize,
          page: this.data.page + 1
        });
      } else {
        console.error('获取资金记录失败 - 响应格式错误:', res);
        this.setData({
          loadingRecords: false,
          hasMoreRecords: false
        });
        wx.showToast({
          title: res.message || '获取资金记录失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取资金记录失败:', err);
      this.setData({
        loadingRecords: false,
        hasMoreRecords: false
      });
      wx.showToast({
        title: '获取资金记录失败',
        icon: 'none'
      });
    });
  },

  // 切换标签
  switchTab: function(e) {
    const tabKey = e.currentTarget.dataset.key;
    this.setData({ activeTab: tabKey });
  },

  // 充值
  recharge: function() {
    wx.showModal({
      title: '充值',
      content: '暂未开放，敬请期待',
      showCancel: false
    });
  },

  // 提现
  withdraw: function() {
    wx.showModal({
      title: '提现',
      content: '暂未开放，敬请期待',
      showCancel: false
    });
  },

  // 绑定银行卡
  bindBankCard: function() {
    wx.showModal({
      title: '绑定银行卡',
      content: '银行卡绑定功能开发中，敬请期待',
      showCancel: false
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadWalletInfo();
    this.loadFundRecords(true);
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom: function() {
    this.loadFundRecords();
  },

  // 格式化金额
  formatAmount: function(amount) {
    return parseFloat(amount || 0).toFixed(2);
  },

  // 格式化时间
  formatTime: function(timeStr) {
    if (!timeStr) return '';
    const date = new Date(timeStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    return `${year}年${month}月${day}日${hour}:${minute}`;
  },

  // 获取记录类型显示文本
  getRecordTypeText: function(type) {
    const typeMap = {
      'dividend': '分红',
      'recharge': '充值',
      'commission': '分佣',
      'payment': '支付',
      'withdraw': '提现',
      'refund': '退款'
    };
    return typeMap[type] || '其他';
  }
});