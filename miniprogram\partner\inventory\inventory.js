// partner/inventory/inventory.js
// 合伙人端门店库存页面
const { productApi, partnerApi, storeApi } = require('../../utils/api');
const request = require('../../utils/request');

Page({
  data: {
    // 门店相关
    storeNo: null,
    storeName: '',
    storeList: [],
    selectedStore: null,
    
    // 搜索相关
    searchKeyword: '',
    
    // 商品数据
    products: [],
    
    // 加载状态
    loading: true,
    refreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // 排序筛选
    sortOptions: [
      { id: 'default', name: '综合' },
      { id: 'inventory_asc', name: '库存↑' },
      { id: 'inventory_desc', name: '库存↓' }
    ],
    currentSort: 'default',
    
    // 筛选相关
    showFilterDrawer: false,
    filterOptions: {
      priceRange: [0, 1000],
      categoryId: null,
      inventoryRange: [0, 1000],
      sortBy: 'default'
    },
    categories: [],
    
    // 库存编辑相关
    hasModifiedInventory: false
  },

  /**
   * 页面加载
   */
  onLoad: function (options) {
    console.log('门店库存页面加载，参数:', options);
    
    // 获取门店信息
    
    if (options.storeNo) {
      this.setData({ storeNo: options.storeNo });
    }
    
    // 从本地存储获取门店列表和选中的门店
    this.syncStoreData();
    
    // 获取分类数据
    this.getCategories();
    
    // 添加全局图片错误处理
    this.setupImageErrorHandler();
    
    // 获取库存商品数据
    this.getInventoryProducts();
  },
  
  /**
   * 设置全局图片错误处理
   */
  setupImageErrorHandler: function() {
    // 监听全局图片加载错误事件
    wx.onError(err => {
      console.log('全局错误捕获:', err);
      // 检查是否是图片加载错误
      if (err && typeof err === 'string' && 
          (err.includes('Failed to load image') || 
           err.includes('failed to load') || 
           err.includes('MOGOODS'))) {
        console.warn('捕获到图片加载错误:', err);
      }
    });
  },

  /**
   * 页面显示
   */
  onShow: function() {
    console.log('门店库存页面显示');
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    console.log('下拉刷新');
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true
    });
    
    this.getInventoryProducts().finally(() => {
      wx.stopPullDownRefresh();
      this.setData({ refreshing: false });
    });
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }
    
    console.log('上拉加载更多商品');
    this.loadMoreProducts();
  },



  /**
   * 同步门店数据
   */
  syncStoreData: function() {
    // 从本地存储获取门店数据
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    console.log('同步门店数据，当前本地存储:', { storedSelectedStore, storedStoreList });
    
    // 如果本地存储中有门店信息，使用本地存储数据
    if (storedStoreList && storedStoreList.length > 0) {
      // 如果有storeNo参数，优先使用参数指定的门店
      let selectedStore = storedSelectedStore;
      
      if (this.data.storeNo) {
        const matchedStore = storedStoreList.find(store => 
          store.store_no == this.data.storeNo
        );
        
        if (matchedStore) {
          selectedStore = matchedStore;
          console.log('根据参数找到匹配的门店:', selectedStore);
        }
      }
      
      this.setData({
        storeList: storedStoreList,
        selectedStore: selectedStore,
        storeName: selectedStore ? selectedStore.name : ''
      });
      
      console.log('从本地存储同步门店信息:', {
        storeList: storedStoreList,
        selectedStore: selectedStore
      });
      return;
    }
    
    // 否则重新获取门店数据
    console.log('本地存储中没有门店信息，重新获取');
    this.getPartnerStores();
  },

  /**
   * 获取合伙人门店列表
   */
  getPartnerStores: function() {
    // 显示加载状态
    wx.showLoading({
      title: '获取门店数据...',
      mask: true
    });
    
    // 同时调用两个获取门店的API
    return Promise.all([
      partnerApi.getPartnerStores(),
      partnerApi.getPartnerJoinedStores()
    ]).then(([storesRes, joinedStoresRes]) => {
      // 合并两个API的门店数据，并去重
      let allStores = [];
      let hasApiError = false;
      
      if (storesRes && storesRes.success && storesRes.data) {
        allStores = [...storesRes.data];
      } else {
        console.error('获取合伙人门店列表失败:', storesRes);
        hasApiError = true;
      }
      
      if (joinedStoresRes && joinedStoresRes.success && joinedStoresRes.data) {
        // 将joinedStores中的门店添加到allStores中，避免重复
        joinedStoresRes.data.forEach(store => {
          // 检查是否已存在相同store_no的门店
          const existingIndex = allStores.findIndex(s => s.store_no === store.store_no);
          if (existingIndex === -1) {
            // 不存在则添加
            allStores.push(store);
          }
        });
      } else {
        console.error('获取合伙人加入的门店列表失败:', joinedStoresRes);
        hasApiError = true;
      }
      
      console.log('合并后的门店列表数据:', allStores);
      
      if (allStores.length > 0) {
        // 选择第一个门店或匹配参数指定的门店
        let selectedStore = null;
        
        // 获取本地存储的选中门店，用于后续保持用户选择
        const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
        
        // 优先使用URL参数指定的门店
        if (this.data.storeNo) {
          selectedStore = allStores.find(store => 
            store.store_no == this.data.storeNo
          );
        }
        
        // 如果URL没有指定门店，尝试保持之前选择的门店
        if (!selectedStore && storedSelectedStore) {
          selectedStore = allStores.find(store => 
            store.id === storedSelectedStore.id || store.store_no === storedSelectedStore.store_no
          );
        }
        
        // 如果没有找到匹配的门店，选择第一个
        if (!selectedStore && allStores.length > 0) {
          selectedStore = allStores[0];
        }
        
        this.setData({
          storeList: allStores,
          selectedStore: selectedStore,
          storeName: selectedStore ? selectedStore.name : ''
        });
        
        // 保存到本地存储
        wx.setStorageSync('partnerStoreList', allStores);
        wx.setStorageSync('partnerSelectedStore', selectedStore);
        
        // 门店数据加载完成后，重新加载商品数据
        if (selectedStore) {
          this.getInventoryProducts();
        }
        
        return selectedStore;
      } else {
        // 没有门店数据的处理
        if (hasApiError) {
          // API调用失败
          wx.showModal({
            title: '门店数据获取失败',
            content: '无法获取门店信息，请检查网络连接。是否重新获取？',
            confirmText: '重新获取',
            cancelText: '返回上页',
            success: (res) => {
              if (res.confirm) {
                this.getPartnerStores();
              } else {
                wx.navigateBack();
              }
            }
          });
        } else {
          // API成功但没有门店数据
          wx.showModal({
            title: '暂无门店数据',
            content: '您还没有关联的门店，请联系管理员或返回上一页。',
            confirmText: '返回上页',
            showCancel: false,
            success: () => {
              wx.navigateBack();
            }
          });
        }
        return null;
      }
    }).catch(err => {
      console.error('获取门店列表失败:', err);
      
      // 根据错误类型显示不同的提示
      if (err.code === -1 || (err.message && err.message.includes('网络'))) {
        wx.showModal({
          title: '网络连接失败',
          content: '请检查网络连接后重试',
          confirmText: '重新获取',
          cancelText: '返回上页',
          success: (res) => {
            if (res.confirm) {
              this.getPartnerStores();
            } else {
              wx.navigateBack();
            }
          }
        });
      } else if (err.code === 401) {
        // 登录失效，不需要额外处理，request.js已经处理了
        console.log('登录失效，已由request.js处理');
      } else {
        wx.showToast({
          title: err.message || '获取门店列表失败',
          icon: 'none',
          duration: 3000
        });
      }
      throw err;
    }).finally(() => {
      wx.hideLoading();
    });
  },

  /**
   * 门店选择
   */
  onStoreChange: function(e) {
    const index = e.detail.value;
    const selectedStore = this.data.storeList[index];
    
    this.setData({ 
      selectedStore,
      storeName: selectedStore.name,
      page: 1,
      hasMore: true,
      products: []
    });
    
    // 保存到本地存储，供其他页面读取
    wx.setStorageSync('partnerSelectedStore', selectedStore);
    
    // 重新加载库存数据
    this.getInventoryProducts();
  },

  /**
   * 获取分类数据
   */
  getCategories: function() {
    return request({
      url: '/api/products/categories',
      method: 'GET'
    }).then(res => {
      if (res.success && Array.isArray(res.data)) {
        console.log('获取分类成功:', res.data.length, '个分类');
        this.setData({ categories: res.data });
      } else {
        console.warn('获取分类失败:', res);
        this.setData({ categories: [] });
      }
    }).catch(err => {
      console.error('获取分类失败:', err);
      this.setData({ categories: [] });
    });
  },

  /**
   * 获取库存商品数据
   */
  getInventoryProducts: function() {
    if (!this.data.selectedStore) {
      console.log('未选择门店，无法获取库存数据');
      this.setData({ loading: false, products: [] });
      return Promise.resolve();
    }
    
    const params = {
      page: this.data.page,
      pageSize: this.data.pageSize,
      storeNo: this.data.selectedStore.store_no,
      withInventory: true, // 只获取有库存记录的商品
      minInventory: 1 // 确保库存至少为1
    };
    
    // 添加搜索关键词
    if (this.data.searchKeyword) {
      params.keyword = this.data.searchKeyword;
    }
    
    // 添加排序方式
    if (this.data.currentSort !== 'default') {
      params.sortBy = this.data.currentSort;
    }
    
    // 添加筛选条件
    if (this.data.filterOptions.categoryId) {
      params.categoryId = this.data.filterOptions.categoryId;
    }
    
    if (this.data.filterOptions.priceRange && 
        (this.data.filterOptions.priceRange[0] > 0 || 
         this.data.filterOptions.priceRange[1] < 1000)) {
      params.minPrice = this.data.filterOptions.priceRange[0];
      params.maxPrice = this.data.filterOptions.priceRange[1];
    }
    
    if (this.data.filterOptions.inventoryRange && 
        (this.data.filterOptions.inventoryRange[0] > 0 || 
         this.data.filterOptions.inventoryRange[1] < 1000)) {
      params.minInventory = Math.max(1, this.data.filterOptions.inventoryRange[0]); // 确保库存至少为1
      params.maxInventory = this.data.filterOptions.inventoryRange[1];
    }
    
    console.log('获取库存商品参数:', params);
    
    this.setData({ loading: true });
    
    return storeApi.getInventoryProducts(params).then(res => {
      if (res.success && res.data && res.data.list && Array.isArray(res.data.list)) {
        console.log('获取库存商品列表成功:', res.data);
        let productsList = res.data.list;
        
        // 处理商品图片URL
        productsList = productsList.map(product => {
          // 确保imageUrl字段存在
          if (!product.imageUrl && product.images) {
            if (typeof product.images === 'string') {
              try {
                const imagesArray = JSON.parse(product.images);
                if (imagesArray && imagesArray.length > 0) {
                  // 确保图片URL格式正确
                  let imageUrl = imagesArray[0];
                  // 检查图片URL是否为相对路径，如果是则添加前缀
                  if (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('/')) {
                    imageUrl = '/' + imageUrl;
                  }
                  product.imageUrl = imageUrl;
                }
              } catch (e) {
                console.error('解析商品图片失败:', e, '商品ID:', product.id);
                // 解析失败时使用默认图片
                product.imageUrl = '/images/mo/mogoods.jpg';
              }
            } else if (Array.isArray(product.images) && product.images.length > 0) {
              // 确保图片URL格式正确
              let imageUrl = product.images[0];
              // 检查图片URL是否为相对路径，如果是则添加前缀
              if (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('/')) {
                imageUrl = '/' + imageUrl;
              }
              product.imageUrl = imageUrl;
            }
          }
          
          // 如果没有图片，使用默认图片
          if (!product.imageUrl) {
            product.imageUrl = '/images/mo/mogoods.jpg';
          }
          
          // 检查图片URL是否包含不存在的路径
          if (product.imageUrl.includes('/MOGOODS/') || product.imageUrl.includes('/mogoods/')) {
            console.warn('检测到可能错误的图片路径:', product.imageUrl, '商品ID:', product.id);
            product.imageUrl = '/images/mo/mogoods.jpg';
          }
          
          return product;
        });
        
        let products;
        let hasMore = true;
        
        if (this.data.page === 1) {
          products = productsList;
        } else {
          // 合并商品列表
          products = [...this.data.products, ...productsList];
        }
        
        // 判断是否有更多数据
        if (res.data.total !== undefined && res.data.total !== null) {
          hasMore = products.length < res.data.total;
        } else if (productsList.length < this.data.pageSize) {
          hasMore = false;
        }
        
        this.setData({
          products: products,
          hasMore: hasMore,
          loading: false
        });
      } else {
        this.setData({
          hasMore: false,
          loading: false
        });
        
        if (this.data.page === 1) {
          this.setData({ products: [] });
        }
      }
    }).catch(err => {
      console.error('获取库存商品列表失败:', err);
      this.setData({ 
        hasMore: false,
        loading: false 
      });
      
      if (this.data.page === 1) {
        this.setData({ products: [] });
      }
    });
  },

  /**
   * 加载更多商品
   */
  loadMoreProducts: function() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }
    
    console.log('加载更多库存商品，当前页:', this.data.page, '-> 下一页:', this.data.page + 1);
    this.setData({
      page: this.data.page + 1,
      loading: true
    });
    
    this.getInventoryProducts();
  },

  /**
   * 搜索输入
   */
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 搜索确认
   */
  onSearchConfirm: function() {
    this.setData({
      page: 1,
      hasMore: true,
      products: []
    });
    
    this.getInventoryProducts();
  },

  /**
   * 排序方式切换
   */
  onSortChange: function(e) {
    const sortType = e.currentTarget.dataset.sort;
    console.log('切换排序方式:', sortType);
    
    if (sortType === this.data.currentSort) {
      return;
    }
    
    this.setData({
      currentSort: sortType,
      page: 1,
      hasMore: true,
      products: []
    });
    
    this.getInventoryProducts();
  },
  
  /**
   * 切换库存排序（升序/降序）
   */
  onToggleInventorySort: function() {
    let newSortType;
    
    // 如果当前是库存升序，切换为降序
    if (this.data.currentSort === 'inventory_asc') {
      newSortType = 'inventory_desc';
    } 
    // 如果当前是库存降序或其他排序方式，切换为升序
    else {
      newSortType = 'inventory_asc';
    }
    
    console.log('切换库存排序方式:', this.data.currentSort, '->', newSortType);
    
    this.setData({
      currentSort: newSortType,
      page: 1,
      hasMore: true,
      products: []
    });
    
    this.getInventoryProducts();
  },

  /**
   * 显示筛选抽屉
   */
  onShowFilter: function() {
    this.setData({
      showFilterDrawer: true
    });
  },

  /**
   * 隐藏筛选抽屉
   */
  onHideFilter: function() {
    this.setData({
      showFilterDrawer: false
    });
  },

  /**
   * 应用筛选
   */
  onApplyFilter: function(e) {
    const filterOptions = e.detail;
    console.log('应用筛选:', filterOptions);
    
    this.setData({
      filterOptions: filterOptions,
      page: 1,
      hasMore: true,
      products: []
    });
    
    this.getInventoryProducts();
    this.onHideFilter();
  },

  /**
   * 重置筛选
   */
  onResetFilter: function() {
    this.setData({
      filterOptions: {
        priceRange: [0, 1000],
        categoryId: null,
        inventoryRange: [0, 1000],
        sortBy: 'default'
      },
      page: 1,
      hasMore: true,
      products: []
    });
    
    this.getInventoryProducts();
    this.onHideFilter();
  },

  /**
   * 商品点击
   */
  onProductTap: function(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('点击商品:', productId);
    
    wx.navigateTo({
      url: `/pages/product/detail?id=${productId}`
    });
  },
  
  /**
   * 阻止事件冒泡
   */
  stopPropagation: function(e) {
    // 阻止事件冒泡
    return false;
  },
  
  /**
   * 减少库存
   */
  onDecreaseInventory: function(e) {
    const { id, index } = e.currentTarget.dataset;
    const products = this.data.products;
    const product = products[index];
    
    // 如果editInventory未定义，则初始化为当前库存值
    let currentValue = product.editInventory !== undefined ? product.editInventory : (product.cloudInventory || 0);
    
    // 减少库存，最小为0
    currentValue = Math.max(0, currentValue - 1);
    
    // 更新数据
    products[index].editInventory = currentValue;
    
    // 标记为已修改
    if (products[index].editInventory !== products[index].cloudInventory) {
      products[index].inventoryModified = true;
    } else {
      products[index].inventoryModified = false;
    }
    
    this.checkModifiedInventory(products);
    this.setData({ products });
  },
  
  /**
   * 增加库存
   */
  onIncreaseInventory: function(e) {
    const { id, index } = e.currentTarget.dataset;
    const products = this.data.products;
    const product = products[index];
    
    // 如果editInventory未定义，则初始化为当前库存值
    let currentValue = product.editInventory !== undefined ? product.editInventory : (product.cloudInventory || 0);
    
    // 增加库存
    currentValue = currentValue + 1;
    
    // 更新数据
    products[index].editInventory = currentValue;
    
    // 标记为已修改
    if (products[index].editInventory !== products[index].cloudInventory) {
      products[index].inventoryModified = true;
    } else {
      products[index].inventoryModified = false;
    }
    
    this.checkModifiedInventory(products);
    this.setData({ products });
  },
  
  /**
   * 库存输入
   */
  onInventoryInput: function(e) {
    const { id, index } = e.currentTarget.dataset;
    const value = parseInt(e.detail.value) || 0;
    const products = this.data.products;
    
    // 更新数据
    products[index].editInventory = value;
    
    // 标记为已修改
    if (products[index].editInventory !== products[index].cloudInventory) {
      products[index].inventoryModified = true;
      this.checkModifiedInventory(products);
    } else {
      products[index].inventoryModified = false;
      this.checkModifiedInventory(products);
    }
    
    this.setData({ products });
  },
  
  /**
   * 检查是否有修改过的库存
   */
  checkModifiedInventory: function(products) {
    const hasModifiedInventory = products.some(product => product.inventoryModified);
    this.setData({ hasModifiedInventory });
  },
  
  /**
   * 保存库存
   */
  onSaveInventory: function(e) {
    const { id, index } = e.currentTarget.dataset;
    const products = this.data.products;
    const product = products[index];
    
    // 如果没有修改，直接返回
    if (product.editInventory === undefined || product.editInventory === product.cloudInventory) {
      return;
    }
    
    // 显示加载提示
    wx.showLoading({ title: '保存中...' });
    
    // 调用API更新库存
    storeApi.updateInventory(
      this.data.selectedStore.store_no,
      id,
      product.editInventory
    ).then(res => {
      if (res.success) {
        // 更新成功
        wx.showToast({
          title: '库存更新成功',
          icon: 'success'
        });
        
        // 更新本地数据
        products[index].cloudInventory = product.editInventory;
        products[index].editInventory = undefined;
        products[index].inventoryModified = false;
        this.checkModifiedInventory(products);
        this.setData({ products });
      } else {
        wx.showToast({
          title: res.message || '更新失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('更新库存失败:', err);
      wx.showToast({
        title: '更新失败，请重试',
        icon: 'none'
      });
    }).finally(() => {
      wx.hideLoading();
    });
  },
  
  /**
   * 批量保存库存
   */
  onBatchSaveInventory: function() {
    // 如果没有修改过的库存，直接返回
    if (!this.data.hasModifiedInventory) {
      return;
    }
    
    // 获取所有修改过的商品
    const modifiedProducts = this.data.products.filter(product => product.inventoryModified);
    
    if (modifiedProducts.length === 0) {
      return;
    }
    
    // 显示加载提示
    wx.showLoading({ title: '批量保存中...' });
    
    // 准备批量更新数据
    const updateData = modifiedProducts.map(product => ({
      storeNo: this.data.selectedStore.store_no,
      productId: product.id,
      quantity: product.editInventory
    }));
    
    // 调用接口批量更新库存
    storeApi.batchUpdateInventory(
      this.data.selectedStore.store_no,
      updateData
    ).then(res => {
      if (res.success) {
        // 更新成功，更新本地数据
        const products = this.data.products.map(product => {
          if (product.inventoryModified) {
            product.cloudInventory = product.editInventory;
            product.editInventory = undefined;
            product.inventoryModified = false;
          }
          return product;
        });
        
        this.setData({ 
          products,
          hasModifiedInventory: false
        });
        
        wx.showToast({
          title: '批量更新成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: res.message || '批量保存失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('批量更新库存失败:', err);
      wx.showToast({
        title: '批量更新失败，请重试',
        icon: 'none'
      });
    }).finally(() => {
      wx.hideLoading();
    });
  },

  /**
   * 图片加载错误处理
   */
  onImageError: function(e) {
    console.log('图片加载错误:', e);
    // 获取当前图片索引
    const index = e.currentTarget.dataset.index || e.target.dataset.index;
    const productId = e.currentTarget.dataset.id || e.target.dataset.id;
    
    // 如果能够确定是哪个商品的图片加载失败，就只更新该商品的图片
    if (productId) {
      const products = this.data.products;
      const productIndex = products.findIndex(item => item.id === productId);
      
      if (productIndex !== -1) {
        // 使用setData的对象表示法只更新特定商品的图片URL
        const updatePath = `products[${productIndex}].imageUrl`;
        this.setData({
          [updatePath]: '/images/mo/mogoods.jpg'
        });
        console.log(`已将商品ID ${productId} 的图片替换为默认图片`);
      }
    } else {
      // 如果无法确定是哪个商品，则在控制台输出错误信息
      console.warn('无法确定哪个商品的图片加载失败');
    }
  }

});