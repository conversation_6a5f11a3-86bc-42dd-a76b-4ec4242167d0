<view class="product-admin-page">
  <!-- 搜索栏 -->
  <view class="user-mgr-header">
    <view class="user-mgr-search-bar">
      <view class="user-mgr-search-input-container">
        <input class="user-mgr-search-input" placeholder="请输入搜索内容" value="{{searchValue}}" bindinput="onSearchInput" confirm-type="search" bindconfirm="onSearch" />
        <view class="user-mgr-search-btn" bindtap="onSearch">
          <image src="/images/icons2/搜索.png"></image>
        </view>
      </view>
    </view>
  </view>
  <!-- 操作按钮栏 -->
  <view class="user-mgr-actions">
    <button class="user-mgr-btn" bindtap="onCreateProduct">创建商品</button>
    <button class="user-mgr-btn" bindtap="onSelectAll">全选/取消</button>
    <button class="user-mgr-btn" bindtap="onBatchUp">批量上架</button>
    <button class="user-mgr-btn" bindtap="onBatchDown">批量下架</button>
  </view>
  <!-- 分类标签栏 -->
  <scroll-view class="category-tabs" scroll-x="true" show-scrollbar="false">
    <view class="category-tab-list">
      <block wx:for="{{tabs}}" wx:key="*this">
        <view class="category-tab {{activeTab === index ? 'active' : ''}}" data-index="{{index}}" bindtap="onTabChange">{{item}}</view>
      </block>
    </view>
  </scroll-view>
  <!-- 排序筛选栏 -->
  <view class="sort-filter-bar">
    <!-- 排序按钮 -->
    <view class="sort-buttons">
      <view class="sort-btn {{sortBy === 'name' ? 'active' : ''}}" data-sort="name" bindtap="onSortChange">
        <text>名称</text>
        <view class="sort-icon" wx:if="{{sortBy === 'name' && sortOrder}}">
          <text class="{{sortOrder === 'asc' ? 'asc' : 'desc'}}">{{sortOrder === 'asc' ? '↑' : '↓'}}</text>
        </view>
      </view>
      <view class="sort-btn {{sortBy === 'price' ? 'active' : ''}}" data-sort="price" bindtap="onSortChange">
        <text>价格</text>
        <view class="sort-icon" wx:if="{{sortBy === 'price' && sortOrder}}">
          <text class="{{sortOrder === 'asc' ? 'asc' : 'desc'}}">{{sortOrder === 'asc' ? '↑' : '↓'}}</text>
        </view>
      </view>
    </view>
    <!-- 筛选按钮 -->
    <view class="filter-btn {{filterApplied ? 'applied' : ''}}" bindtap="onToggleFilter">
      <text>筛选</text>
      <text class="filter-icon">{{showFilter ? '▲' : '▼'}}</text>
    </view>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel {{showFilter ? 'show' : ''}}">
    <!-- 价格区间 -->
    <view class="filter-section">
      <view class="filter-title">价格区间</view>
      <view class="price-range">
        <input class="price-input" placeholder="最低价" type="digit" value="{{priceMin}}" bindinput="onPriceMinInput" />
        <text class="price-separator">-</text>
        <input class="price-input" placeholder="最高价" type="digit" value="{{priceMax}}" bindinput="onPriceMaxInput" />
      </view>
    </view>
    
    <!-- 商品状态 -->
    <view class="filter-section">
      <view class="filter-title">商品状态</view>
      <view class="status-options">
        <view class="status-option {{statusFilter.includes('on') ? 'selected' : ''}}" data-status="on" bindtap="onStatusFilterChange">
          <text>已上架</text>
        </view>
        <view class="status-option {{statusFilter.includes('off') ? 'selected' : ''}}" data-status="off" bindtap="onStatusFilterChange">
          <text>已下架</text>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="filter-actions">
      <button class="filter-clear-btn" bindtap="onClearFilter">清除</button>
      <button class="filter-apply-btn" bindtap="onApplyFilter">应用</button>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="product-list">
    <block wx:for="{{products}}" wx:key="id">
      <view class="product-mgr-item">
        <view class="product-mgr-status product-mgr-status-{{item.statusClass}}">{{item.statusText}}</view>
        <view class="product-mgr-checkbox {{item.checked ? 'selected' : ''}}" bindtap="onCheckProduct" data-index="{{index}}">
          <image src="{{item.checked ? '/images/icons/checked-box.svg' : '/images/icons/unchecked-box.svg'}}"></image>
        </view>
        <image class="product-mgr-img" src="{{item.image}}" />
        <view class="product-mgr-info">
          <view class="product-mgr-title-row">
            <view class="product-mgr-title">{{item.name}}</view>
          </view>
          <view class="product-mgr-spec">{{item.spec}}</view>
          <view class="product-mgr-price">￥{{item.price}}</view>
        </view>
        <view class="product-mgr-edit-btn" bindtap="onEditProduct" data-index="{{index}}">编辑</view>
      </view>
    </block>
    <!-- 底部统计区，移到商品列表末尾 -->
    <view class="user-mgr-footer">
      共计：{{products.length}}个商品
      <text wx:if="{{selectedCount > 0}}" class="selected-count">（已选中{{selectedCount}}个）</text>
    </view>
    <view class="safe-area"></view>
  </view>

  <admin-tabbar current="products" />
  <product-edit-drawer visible="{{showEditDrawer}}" product="{{editProduct}}" bind:cancel="onEditDrawerCancel" bind:confirm="onEditDrawerConfirm" />
  <product-create-drawer visible="{{showCreateDrawer}}" bind:cancel="onCreateDrawerCancel" bind:confirm="onCreateDrawerConfirm" />
</view>