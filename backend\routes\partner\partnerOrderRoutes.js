const express = require('express');
const router = express.Router();
const PartnerOrderController = require('../../controllers/partner/partnerOrderController');

/**
 * 合伙人端订单路由
 * 基础路径: /api/partner/orders
 */

// 创建采购订单
router.post('/purchase', PartnerOrderController.createPurchaseOrder);

// 创建移库订单
router.post('/transfer', PartnerOrderController.createTransferOrder);

// 获取门店订单列表
router.get('/', PartnerOrderController.getStoreOrders);

// 获取门店订单详情
router.get('/:orderId', PartnerOrderController.getStoreOrderDetail);

// 更新订单状态
router.put('/:orderId/status', PartnerOrderController.updateOrderStatus);

// 处理门店订单支付
router.post('/:orderId/pay', PartnerOrderController.processStoreOrderPayment);

// 获取门店库存
router.get('/inventory', PartnerOrderController.getStoreInventory);

module.exports = router;