/* 合伙人端 - 我的推荐页面样式 */

.my-referrals-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  /* 注释：移除底部padding，因为此页面不显示底部导航栏 */
  /* padding-bottom: 120rpx; */
}

/* 统计区域 */
.stats-section {
  background: white;
  margin-bottom: 20rpx;
}

.tab-header {
  display: flex;
  height: 120rpx;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  background: white;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: #5698c3; /* 使用睛蓝色 */
  color: white;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: white;
  border-radius: 3rpx;
}

.tab-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.tab-count {
  font-size: 28rpx;
  font-weight: bold;
}

.tab-item:not(.active) .tab-title {
  color: #666;
}

.tab-item:not(.active) .tab-count {
  color: #333;
}

/* 排序筛选栏 - 参照门店订单页样式 */
.sort-filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  padding: 0 30rpx;
  height: 80rpx;
  margin-bottom: 1px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.sort-buttons {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.sort-btn {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  transition: all 0.3s ease;
}

.sort-btn.active {
  color: #5698c3; /* 使用睛蓝色 */
  font-weight: 500;
  background-color: #f0f8ff;
}

.sort-btn-text {
  font-size: 28rpx;
  margin-right: 6rpx;
}

.sort-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 6rpx;
}

.filter-btn {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  transition: all 0.3s ease;
}

.filter-btn:active {
  background-color: #f0f0f0;
}

.filter-btn-text {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.filter-icon {
  width: 28rpx;
  height: 28rpx;
  margin-left: 8rpx;
}

/* 用户列表 */
.user-list {
  padding: 0 32rpx;
}

.user-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.user-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  overflow: hidden;
  margin-right: 24rpx;
  background: #f0f0f0;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.user-info {
  flex: 1;
}

.user-basic {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.user-nickname {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-right: 16rpx;
}

.user-id {
  font-size: 24rpx;
  color: #999;
  background: #f8f9fa;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.user-details {
  margin-bottom: 12rpx;
}

.user-store,
.user-type,
.register-date,
.total-consumption {
  font-size: 26rpx;
  color: #666;
  margin-right: 24rpx;
  display: inline-block;
  margin-bottom: 8rpx;
}

.user-meta {
  display: flex;
  align-items: center;
}

.join-date {
  font-size: 24rpx;
  color: #999;
}

.user-arrow {
  font-size: 32rpx;
  color: #ccc;
  margin-left: 16rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 40rpx 0;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

/* 没有更多数据 */
.no-more-state {
  text-align: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 24rpx;
  color: #ccc;
}

/* 排序选择器弹窗（备用，当前已改为直接按钮切换） */
/* .sort-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.sort-picker {
  background: white;
  width: 100%;
  border-radius: 24rpx 24rpx 0 0;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.sort-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #eee;
}

.sort-picker-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.sort-picker-close {
  font-size: 48rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sort-picker-content {
  padding: 0 32rpx 32rpx;
}

.sort-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.sort-option:last-child {
  border-bottom: none;
}

.sort-option-text {
  font-size: 30rpx;
  color: #333;
}

.sort-option-check {
  font-size: 32rpx;
  color: #667eea;
  font-weight: bold;
} */

/* 响应式设计 */
@media (max-width: 375px) {
  .tab-title {
    font-size: 28rpx;
  }
  
  .tab-count {
    font-size: 24rpx;
  }
  
  .user-nickname {
    font-size: 28rpx;
  }
}