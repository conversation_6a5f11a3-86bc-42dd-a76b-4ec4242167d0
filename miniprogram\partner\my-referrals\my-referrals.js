// 合伙人端 - 我的推荐页面
const { partnerApi } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager');

Page({
  data: {
    // 当前选中的标签页 0-我推荐的合伙人 1-我推荐的顾客
    activeTab: 0,
    
    // 我推荐的合伙人数据
    partnerReferrals: [],
    partnerTotal: 0,
    partnerLoading: false,
    partnerPage: 1,
    partnerHasMore: true,
    
    // 我推荐的顾客数据
    customerReferrals: [],
    customerTotal: 0,
    customerLoading: false,
    customerPage: 1,
    customerHasMore: true,
    
    // 排序选项
    partnerSortOptions: [
      { key: 'partner_type', label: '合伙人类型' },
      { key: 'join_date', label: '加入日期' }
    ],
    customerSortOptions: [
      { key: 'register_date', label: '注册日期' },
      { key: 'user_id', label: 'ID号' },
      { key: 'total_consumption', label: '累计消费' }
    ],
    
    // 当前排序（支持三种状态：asc升序、desc降序、null取消排序）
    partnerSortBy: 'join_date',
    partnerSortOrder: 'desc',
    customerSortBy: 'register_date',
    customerSortOrder: 'desc',
    
    // 显示排序选择器
    showSortPicker: false,
    currentSortOptions: [],
    
    // 用户信息
    userInfo: null
  },

  onLoad() {
    console.log('=== 我的推荐页面 onLoad ===');
    this.checkLoginAndLoadData();
  },

  onShow() {
    // 每次显示页面时，只有在已有用户信息的情况下才刷新数据
    if (this.data.userInfo) {
      this.refreshCurrentTab();
    }
  },

  // 检查登录状态并加载数据
  async checkLoginAndLoadData() {
    try {
      // 使用validateLoginState方法检查登录状态
      const loginResult = await loginStateManager.validateLoginState();
      if (!loginResult.isValid || !loginResult.userInfo) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      // 设置用户信息到页面数据中
      this.setData({ userInfo: loginResult.userInfo });
      await this.loadReferralData();
    } catch (error) {
      console.error('检查登录状态失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 切换标签页
  switchTab(e) {
    const { tab } = e.currentTarget.dataset;
    if (tab === this.data.activeTab) return;
    
    this.setData({ activeTab: tab });
    
    // 切换标签页时，只有在对应标签页数据为空时才重新加载
    if (tab === 0 && this.data.partnerReferrals.length === 0) {
      this.loadPartnerReferrals();
    } else if (tab === 1 && this.data.customerReferrals.length === 0) {
      this.loadCustomerReferrals();
    }
  },

  // 加载推荐数据
  async loadReferralData() {
    // 初始加载时，同时获取两个标签页的统计数据
    if (this.data.partnerTotal === 0 && this.data.customerTotal === 0) {
      await Promise.all([
        this.loadPartnerReferrals(),
        this.loadCustomerReferrals()
      ]);
    } else {
      // 后续切换标签页时，只加载当前标签页的数据
      if (this.data.activeTab === 0) {
        await this.loadPartnerReferrals();
      } else {
        await this.loadCustomerReferrals();
      }
    }
  },

  // 加载我推荐的合伙人
  async loadPartnerReferrals(isRefresh = true) {
    if (this.data.partnerLoading) return;
    
    // 确保用户已登录
    if (!this.data.userInfo) {
      console.log('用户未登录，无法加载合伙人推荐数据');
      return;
    }
    
    const page = isRefresh ? 1 : this.data.partnerPage;
    
    this.setData({ partnerLoading: true });
    
    try {
      // 构建请求参数，如果排序字段为null则不传递排序参数
      const params = {
        page,
        limit: 10
      };
      
      if (this.data.partnerSortBy && this.data.partnerSortOrder) {
        params.sortBy = this.data.partnerSortBy;
        params.sortOrder = this.data.partnerSortOrder;
      }
      
      const response = await partnerApi.getMyPartnerReferrals(params);
      
      if (response.success) {
        const newData = response.data.list || [];
        const partnerReferrals = isRefresh ? newData : [...this.data.partnerReferrals, ...newData];
        
        this.setData({
          partnerReferrals,
          partnerTotal: response.data.total || 0,
          partnerPage: page + 1,
          partnerHasMore: newData.length >= 10,
          partnerLoading: false
        });
      } else {
        throw new Error(response.message || '加载失败');
      }
    } catch (error) {
      console.error('加载合伙人推荐数据失败:', error);
      this.setData({ partnerLoading: false });
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  // 加载我推荐的顾客
  async loadCustomerReferrals(isRefresh = true) {
    if (this.data.customerLoading) return;
    
    // 确保用户已登录
    if (!this.data.userInfo) {
      console.log('用户未登录，无法加载顾客推荐数据');
      return;
    }
    
    const page = isRefresh ? 1 : this.data.customerPage;
    
    this.setData({ customerLoading: true });
    
    try {
      // 构建请求参数，如果排序字段为null则不传递排序参数
      const params = {
        page,
        limit: 10
      };
      
      if (this.data.customerSortBy && this.data.customerSortOrder) {
        params.sortBy = this.data.customerSortBy;
        params.sortOrder = this.data.customerSortOrder;
      }
      
      const response = await partnerApi.getMyCustomerReferrals(params);
      
      if (response.success) {
        const newData = response.data.list || [];
        const customerReferrals = isRefresh ? newData : [...this.data.customerReferrals, ...newData];
        
        this.setData({
          customerReferrals,
          customerTotal: response.data.total || 0,
          customerPage: page + 1,
          customerHasMore: newData.length >= 10,
          customerLoading: false
        });
      } else {
        throw new Error(response.message || '加载失败');
      }
    } catch (error) {
      console.error('加载顾客推荐数据失败:', error);
      this.setData({ customerLoading: false });
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  // 刷新当前标签页数据
  refreshCurrentTab() {
    // 确保用户已登录才执行刷新
    if (!this.data.userInfo) {
      console.log('用户未登录，跳过数据刷新');
      return;
    }
    
    // 刷新时同时更新两个标签页的统计数据，确保统计数据始终是最新的
    Promise.all([
      this.loadPartnerReferrals(true),
      this.loadCustomerReferrals(true)
    ]).catch(error => {
      console.error('刷新数据失败:', error);
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshCurrentTab();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.activeTab === 0 && this.data.partnerHasMore) {
      this.loadPartnerReferrals(false);
    } else if (this.data.activeTab === 1 && this.data.customerHasMore) {
      this.loadCustomerReferrals(false);
    }
  },

  // 切换排序（三种状态：升序、降序、取消排序）
  toggleSort(e) {
    const { field } = e.currentTarget.dataset;
    
    if (this.data.activeTab === 0) {
      // 合伙人排序逻辑
      let newSortBy = field;
      let newSortOrder = 'desc'; // 默认降序
      
      if (this.data.partnerSortBy === field) {
        // 同一字段：降序 -> 升序 -> 取消排序
        if (this.data.partnerSortOrder === 'desc') {
          newSortOrder = 'asc';
        } else if (this.data.partnerSortOrder === 'asc') {
          newSortBy = null;
          newSortOrder = null;
        }
      }
      
      this.setData({
        partnerSortBy: newSortBy,
        partnerSortOrder: newSortOrder
      });
      this.loadPartnerReferrals(true);
    } else {
      // 顾客排序逻辑
      let newSortBy = field;
      let newSortOrder = 'desc'; // 默认降序
      
      if (this.data.customerSortBy === field) {
        // 同一字段：降序 -> 升序 -> 取消排序
        if (this.data.customerSortOrder === 'desc') {
          newSortOrder = 'asc';
        } else if (this.data.customerSortOrder === 'asc') {
          newSortBy = null;
          newSortOrder = null;
        }
      }
      
      this.setData({
        customerSortBy: newSortBy,
        customerSortOrder: newSortOrder
      });
      this.loadCustomerReferrals(true);
    }
  },

  // 显示筛选器（占位功能）
  showFilter() {
    if (this.data.activeTab === 0) {
      // 合伙人筛选器（暂时为空）
      wx.showToast({
        title: '筛选功能开发中',
        icon: 'none'
      });
    } else {
      // 顾客筛选器（支持消费金额区间筛选）
      wx.showToast({
        title: '消费金额筛选功能开发中',
        icon: 'none'
      });
    }
  },

  // 显示排序选择器（保留原有功能，备用）
  showSortOptions() {
    const currentSortOptions = this.data.activeTab === 0 
      ? this.data.partnerSortOptions 
      : this.data.customerSortOptions;
    
    this.setData({
      showSortPicker: true,
      currentSortOptions
    });
  },

  // 隐藏排序选择器
  hideSortPicker() {
    this.setData({ showSortPicker: false });
  },

  // 选择排序方式（保留原有功能，备用）
  selectSort(e) {
    const { key, label } = e.currentTarget.dataset;
    
    if (this.data.activeTab === 0) {
      this.setData({
        partnerSortBy: key,
        showSortPicker: false
      });
      this.loadPartnerReferrals(true);
    } else {
      this.setData({
        customerSortBy: key,
        showSortPicker: false
      });
      this.loadCustomerReferrals(true);
    }
  },

  // 查看用户详情
  viewUserDetail(e) {
    const { userId } = e.currentTarget.dataset;
    // TODO: 跳转到用户详情页面
    console.log('查看用户详情:', userId);
  },

  // 格式化日期
  formatDate(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  },

  // 格式化金额
  formatAmount(amount) {
    if (!amount) return '0.00';
    return parseFloat(amount).toFixed(2);
  }
});