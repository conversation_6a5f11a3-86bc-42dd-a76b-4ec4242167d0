/**
 * 管理员控制器
 */
const db = require('../config/db');

/**
 * 获取退款申请列表
 * GET /admin/refunds
 */
exports.getRefundList = async (req, res) => {
  try {
    // 获取查询参数
    const { status, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;
    
    // 构建查询条件
    let whereClause = '';
    let params = [];
    
    if (status) {
      whereClause = 'WHERE status = ?';
      params.push(status);
    }
    
    // 查询退款申请总数
    const countSql = `SELECT COUNT(*) as total FROM order_refunds ${whereClause}`;
    const countResult = await db.query(countSql, params);
    const total = countResult[0].total;
    
    // 查询退款申请列表
    const sql = `
      SELECT r.*, o.order_no, u.nickname, u.avatar 
      FROM order_refunds r
      LEFT JOIN customer_orders o ON r.order_id = o.id
      LEFT JOIN users u ON r.user_id = u.user_id
      ${whereClause}
      ORDER BY r.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    params.push(parseInt(limit), offset);
    const refunds = await db.query(sql, params);
    
    // 处理图片数据
    refunds.forEach(refund => {
      if (refund.images && typeof refund.images === 'string') {
        try {
          refund.images = JSON.parse(refund.images);
        } catch (e) {
          refund.images = [];
        }
      } else {
        refund.images = [];
      }
    });
    
    res.json({
      success: true,
      data: {
        list: refunds,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取退款申请列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取退款申请列表失败',
      error: error.message
    });
  }
};

/**
 * 获取退款申请详情
 * GET /admin/refunds/:id
 */
exports.getRefundDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查询退款申请详情
    const sql = `
      SELECT r.*, o.order_no, o.total_amount, o.status as order_status, u.nickname, u.avatar, u.phone
      FROM order_refunds r
      LEFT JOIN customer_orders o ON r.order_id = o.id
      LEFT JOIN users u ON r.user_id = u.user_id
      WHERE r.id = ?
    `;
    
    const refunds = await db.query(sql, [id]);
    
    if (refunds.length === 0) {
      return res.status(404).json({
        success: false,
        message: '退款申请不存在'
      });
    }
    
    const refund = refunds[0];
    
    // 处理图片数据
    if (refund.images && typeof refund.images === 'string') {
      try {
        refund.images = JSON.parse(refund.images);
      } catch (e) {
        refund.images = [];
      }
    } else {
      refund.images = [];
    }
    
    res.json({
      success: true,
      data: refund
    });
  } catch (error) {
    console.error('获取退款申请详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取退款申请详情失败',
      error: error.message
    });
  }
};

/**
 * 处理退款申请
 * POST /admin/refunds/:id/process
 */
exports.processRefund = async (req, res) => {
  try {
    const { id } = req.params;
    const { action, remark } = req.body;
    
    if (!action || !['approve', 'reject'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: '无效的操作类型'
      });
    }
    
    // 查询退款申请
    const refundSql = 'SELECT * FROM order_refunds WHERE id = ?';
    const refunds = await db.query(refundSql, [id]);
    
    if (refunds.length === 0) {
      return res.status(404).json({
        success: false,
        message: '退款申请不存在'
      });
    }
    
    const refund = refunds[0];
    
    // 检查退款申请状态
    if (refund.status !== '处理中') {
      return res.status(400).json({
        success: false,
        message: '该退款申请已处理'
      });
    }
    
    // 开始数据库事务
    const conn = await db.getConnection();
    await conn.beginTransaction();
    
    try {
      // 更新退款申请状态
      const status = action === 'approve' ? '已退款' : '已拒绝';
      const updateSql = 'UPDATE order_refunds SET status = ?, admin_remark = ?, updated_at = NOW() WHERE id = ?';
      await conn.query(updateSql, [status, remark || null, id]);
      
      // 如果是批准退款，更新订单状态为已退款
      if (action === 'approve') {
        const orderSql = 'UPDATE store_orders SET status = "已退款", updated_at = NOW() WHERE id = ?';
        await conn.query(orderSql, [refund.order_id]);
        
        // 这里可以添加退款金额处理逻辑，例如更新用户余额或调用支付接口进行退款
        // TODO: 实现实际的退款逻辑
      }
      
      await conn.commit();
      
      res.json({
        success: true,
        message: action === 'approve' ? '退款申请已批准' : '退款申请已拒绝'
      });
    } catch (error) {
      await conn.rollback();
      throw error;
    } finally {
      conn.release();
    }
  } catch (error) {
    console.error('处理退款申请失败:', error);
    res.status(500).json({
      success: false,
      message: '处理退款申请失败',
      error: error.message
    });
  }
};