// partner/store-partners/store-partners.js
const { partnerApi, storeApi } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager');

Page({
  data: {
    // 门店相关
    storeList: [],
    selectedStore: null,
    
    // 当前用户在该门店的状况
    userStatus: {
      capital: 0, // 股本金
      role: '',   // 角色
      percent: 0  // 股份比例
    },
    
    // 合伙人列表
    partnerList: [],
    loadingPartners: false,
    partnerCount: 0,
    
    // 用户信息
    userInfo: null,

    // 数据加载状态
    storeDataLoaded: false,
    storeApiError: false
  },

  onLoad(options) {
    // 页面加载时恢复全局登录状态
    loginStateManager.restoreLoginStateToGlobal();
    
    // 获取用户信息
    const app = getApp();
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo
      });
    }
    
    // 优先从本地存储读取门店信息，与合伙人页面保持同步
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    console.log('门店合伙人页面 - 本地门店数据检查:', {
      storedStoreList: !!storedStoreList,
      storeCount: storedStoreList ? storedStoreList.length : 0,
      storedSelectedStore: !!storedSelectedStore
    });
    
    if (storedStoreList && storedStoreList.length > 0) {
      const selectedStore = storedSelectedStore || storedStoreList[0];
      this.setData({
        storeList: storedStoreList,
        selectedStore: selectedStore
      });
      console.log('门店合伙人页面 - 从本地存储读取门店信息:', selectedStore);
      
      // 加载该门店的合伙人数据
      this.loadPartnerData(selectedStore);
    } else {
      // 如果没有本地存储的门店数据，则从API获取
      if (options.storeNo) {
        this.loadStoreByNo(options.storeNo);
      } else {
        this.loadStoreList();
      }
    }
  },

  onShow() {
    this.checkUserLogin();
    
    // 每次显示页面时，都从本地存储同步门店选择状态
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    if (storedStoreList && storedStoreList.length > 0) {
      const selectedStore = storedSelectedStore || storedStoreList[0];
      
      // 检查当前选择的门店是否与存储的门店不同（使用store_no作为唯一标识）
      if (!this.data.selectedStore || 
          this.data.selectedStore.store_no !== selectedStore.store_no) {
        
        this.setData({
          storeList: storedStoreList,
          selectedStore: selectedStore
        });
        
        console.log('门店合伙人页面 - 同步门店选择状态:', selectedStore);
        
        // 重新加载该门店的合伙人数据
        this.loadPartnerData(selectedStore);
      }
    }
  },

  checkUserLogin() {
    const app = getApp();
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '请先登录',
        content: '查看门店合伙人信息需要先登录',
        showCancel: true,
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({ url: '/pages/auth/auth' });
          }
        }
      });
      return;
    }
  },

  // 门店选择变化
  onStoreChange(e) {
    const index = e.detail.value;
    this.selectStoreByIndex(index);
  },

  // 根据索引选择门店
  selectStoreByIndex(index) {
    if (index >= 0 && index < this.data.storeList.length) {
      const store = this.data.storeList[index];
      this.selectStoreByData(store);
    }
  },

  // 根据门店数据选择门店
  selectStoreByData(store) {
    console.log('选择门店:', store);
    
    this.setData({
      selectedStore: store
    });
    
    // 保存到本地存储
    wx.setStorageSync('partnerSelectedStore', store);
    
    // 加载该门店的合伙人数据
    this.loadPartnerData(store);
  },

  // 加载门店列表
  loadStoreList() {
    // 显示加载状态
    wx.showLoading({
      title: '加载门店数据...',
      mask: true
    });
    
    let hasApiError = false; // 标记API调用是否失败
    
    // 修复：使用合伙人专用的门店API，同时调用两个API获取完整的门店数据
    Promise.all([
      partnerApi.getPartnerStores().catch(err => {
        console.error('获取合伙人门店失败:', err);
        hasApiError = true;
        return { success: false, data: [] };
      }),
      partnerApi.getPartnerJoinedStores().catch(err => {
        console.error('获取加盟门店失败:', err);
        hasApiError = true;
        return { success: false, data: [] };
      })
    ]).then(([storesRes, joinedStoresRes]) => {
      // 合并两个API的门店数据，并去重
      let allStores = [];
      
      if (storesRes && storesRes.success && storesRes.data) {
        allStores = [...storesRes.data];
      }
      
      if (joinedStoresRes && joinedStoresRes.success && joinedStoresRes.data) {
        // 将joinedStores中的门店添加到allStores中，避免重复
        joinedStoresRes.data.forEach(store => {
          // 检查是否已存在相同store_no的门店
          const existingIndex = allStores.findIndex(s => s.store_no === store.store_no);
          if (existingIndex === -1) {
            // 不存在则添加
            allStores.push(store);
          }
        });
      }
      
      console.log('获取门店列表成功:', allStores);
      
      // 如果没有门店数据，根据是否有API错误显示不同提示
      if (allStores.length === 0) {
        if (hasApiError) {
          // API调用失败，显示重试选项
          wx.showModal({
            title: '门店数据获取失败',
            content: '无法获取门店信息，请检查网络连接后重试',
            confirmText: '重新获取',
            cancelText: '返回上页',
            success: (res) => {
              if (res.confirm) {
                // 用户选择重新获取，递归调用
                this.loadStoreList();
              } else {
                // 用户选择返回上页
                wx.navigateBack();
              }
            }
          });
        } else {
          // API调用成功但无数据
          wx.showModal({
            title: '暂无门店数据',
            content: '当前账户暂无关联的门店信息',
            showCancel: false,
            confirmText: '返回上页',
            success: () => {
              wx.navigateBack();
            }
          });
        }
        
        this.setData({
          storeList: [],
          selectedStore: null
        });
        
        return;
      }
      
      this.setData({ storeList: allStores });
      
      // 保存到本地存储
      wx.setStorageSync('partnerStoreList', allStores);
      
      if (allStores.length > 0) {
        const selectedStore = allStores[0];
        this.setData({ selectedStore });
        wx.setStorageSync('partnerSelectedStore', selectedStore);
        
        // 加载该门店的合伙人数据
        this.loadPartnerData(selectedStore);
      }
    }).catch(err => {
      console.error('获取门店列表错误:', err);
      
      // 根据错误类型显示不同提示
      if (err.statusCode === 401) {
        wx.showModal({
          title: '登录已失效',
          content: '请重新登录后再试',
          showCancel: false,
          success: () => {
            wx.reLaunch({
              url: '/pages/login/login'
            });
          }
        });
      } else if (err.errMsg && err.errMsg.includes('network')) {
        wx.showModal({
          title: '网络连接失败',
          content: '请检查网络连接后重试',
          confirmText: '重新获取',
          cancelText: '返回上页',
          success: (res) => {
            if (res.confirm) {
              this.loadStoreList();
            } else {
              wx.navigateBack();
            }
          }
        });
      } else {
        wx.showModal({
          title: '获取门店数据失败',
          content: '服务器异常，请稍后再试',
          confirmText: '重新获取',
          cancelText: '返回上页',
          success: (res) => {
            if (res.confirm) {
              this.loadStoreList();
            } else {
              wx.navigateBack();
            }
          }
        });
      }
    }).finally(() => {
      // 隐藏加载状态
      wx.hideLoading();
    });
  },

  // 根据门店号加载门店信息
  loadStoreByNo(storeNo) {
    storeApi.getStoreByNo(storeNo).then(res => {
      if (res.success && res.data) {
        const store = res.data;
        console.log('根据门店号获取门店信息:', store);
        
        this.setData({
          storeList: [store],
          selectedStore: store
        });
        
        // 保存到本地存储
        wx.setStorageSync('partnerStoreList', [store]);
        wx.setStorageSync('partnerSelectedStore', store);
        
        // 加载该门店的合伙人数据
        this.loadPartnerData(store);
      } else {
        console.error('根据门店号获取门店信息失败:', res);
        wx.showToast({
          title: '门店信息不存在',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('根据门店号获取门店信息错误:', err);
      wx.showToast({
        title: '网络错误，请稍后再试',
        icon: 'none'
      });
    });
  },

  // 加载合伙人数据
  loadPartnerData(store) {
    if (!store || !store.store_no) {
      console.error('门店信息不完整，无法加载合伙人数据');
      return;
    }
    
    this.setData({ loadingPartners: true });
    
    // 修复：使用store_no而不是store.id
    partnerApi.getStorePartners(store.store_no).then(res => {
      this.setData({ loadingPartners: false });
      
      if (res.success && res.data) {
        const partnerList = res.data || [];
        console.log('获取门店合伙人数据成功:', partnerList);
        
        // 检查数据结构，特别是user_id字段
        if (partnerList.length > 0) {
          console.log('第一个合伙人数据结构:', partnerList[0]);
          console.log('user_id字段值:', partnerList[0].user_id);
        }
        
        // 格式化合伙人数据，添加格式化后的字段
        const formattedPartnerList = partnerList.map(partner => ({
          ...partner,
          formatted_join_date: this.formatDate(partner.created_at),
          formatted_partner_type: this.formatPartnerType(partner.type)
        }));
        
        // 查找当前用户在该门店的状况
        const userStatus = this.findUserStatus(partnerList);
        
        this.setData({
          partnerList: formattedPartnerList,
          partnerCount: formattedPartnerList.length,
          userStatus
        });
      } else {
        console.error('获取门店合伙人数据失败:', res);
        wx.showToast({
          title: res.message || '获取合伙人信息失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      this.setData({ loadingPartners: false });
      console.error('获取门店合伙人数据错误:', err);
      wx.showToast({
        title: '网络错误，请稍后再试',
        icon: 'none'
      });
    });
  },

  // 查找当前用户在该门店的状况
  findUserStatus(partners) {
    const app = getApp();
    // 修复：使用正确的用户ID字段，id是业务ID（user_id），_id是自增ID
    // 合伙人表中使用的是业务ID进行关联
    const currentUserId = app.globalData.userInfo ? app.globalData.userInfo.id : null;
    
    if (!currentUserId) {
      return {
        capital: 0,
        role: '未登录',
        percent: 0
      };
    }
    
    const userPartner = partners.find(partner => partner.user_id === currentUserId);
    
    if (userPartner) {
      // 处理角色显示：M型合伙人显示为"总部代表"
      let role = userPartner.type || '';
      if (role === 'M型合伙人') {
        role = '总部代表';
      }
      
      return {
        capital: userPartner.amount || 0,
        role: role,
        percent: userPartner.percent || 0
      };
    } else {
      return {
        capital: 0,
        role: '非合伙人',
        percent: 0
      };
    }
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  },

  // 格式化合伙人类型显示
  formatPartnerType(type) {
    if (type === 'M型合伙人') {
      return '总部代表';
    }
    return type || '';
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.selectedStore) {
      this.loadPartnerData(this.data.selectedStore);
    }
    wx.stopPullDownRefresh();
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '门店合伙人信息',
      path: '/partner/store-partners/store-partners'
    };
  },

  // 发送私信
  sendPrivateMessage(e) {
    const userId = e.currentTarget.dataset.userId;
    const userName = e.currentTarget.dataset.userName;
    
    console.log('发送私信给用户:', {
      userId,
      userName,
      dataset: e.currentTarget.dataset
    });
    
    // 检查用户ID是否存在
    if (!userId || userId === 'undefined' || userId === '') {
      console.error('用户ID无效:', userId);
      wx.showToast({
        title: '用户信息不完整，无法发送私信',
        icon: 'none'
      });
      return;
    }
    
    // 修复：使用targetId参数名，这是聊天页面期望的参数
    wx.navigateTo({
      url: `/partner/messages/chat?targetId=${userId}&targetUserName=${encodeURIComponent(userName || '用户')}`
    });
  }
});