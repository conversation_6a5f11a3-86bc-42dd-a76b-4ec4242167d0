<!--partner/wallet/wallet.wxml-->
<!--合伙人端我的钱包页面-->
<view class="wallet-container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </view>

  <block wx:if="{{!loading}}">
    <!-- 余额卡片区 -->
    <view class="balance-section">
      <view class="balance-card">
        <view class="balance-header">
          <view class="balance-icon">
            <image src="/images/icons2/余额.svg" class="icon"></image>
          </view>
          <view class="balance-content">
            <view class="balance-label">余额</view>
            <view class="balance-value">¥{{walletStats.balance || '0.00'}}</view>
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="balance-actions">
          <button class="action-btn withdraw-btn" bindtap="onWithdraw">
            <image src="/images/icons2/提现.svg" class="btn-icon"></image>
            <text>提现</text>
          </button>
          <button class="action-btn recharge-btn" bindtap="onRecharge">
            <image src="/images/icons2/充值.svg" class="btn-icon"></image>
            <text>充值</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 统计数据区 -->
    <view class="stats-section">
      <view class="stats-grid">
        <!-- 待结销售分佣 -->
        <view class="stats-item" bindtap="onCardTap" data-type="pending_commission">
          <view class="stats-value">¥{{walletStats.pending_commission || '0.00'}}</view>
          <view class="stats-label">待结销售分佣</view>
        </view>
        
        <!-- 累计销售分佣 -->
        <view class="stats-item" bindtap="onCardTap" data-type="total_commission">
          <view class="stats-value">¥{{walletStats.total_commission || '0.00'}}</view>
          <view class="stats-label">累计销售分佣</view>
        </view>
        
        <!-- 累计分红 -->
        <view class="stats-item" bindtap="onCardTap" data-type="total_dividend">
          <view class="stats-value">¥{{walletStats.total_dividend || '0.00'}}</view>
          <view class="stats-label">累计分红</view>
        </view>
        
        <!-- 累计提现 -->
        <view class="stats-item" bindtap="onCardTap" data-type="total_withdraw">
          <view class="stats-value">¥{{walletStats.total_withdraw || '0.00'}}</view>
          <view class="stats-label">累计提现</view>
        </view>
      </view>
    </view>

    <!-- 明细栏 -->
    <view class="details-section">
      <view class="details-header">
        <text class="details-title">钱包明细</text>
      </view>
      
      <!-- 标签栏 -->
      <view class="tabs-container">
        <view class="tabs-bar">
          <block wx:for="{{tabs}}" wx:key="key">
            <view class="tab-item {{activeTab === item.key ? 'active' : ''}}" 
                  bindtap="switchTab" 
                  data-key="{{item.key}}">
              <view class="tab-name">{{item.name}}</view>
            </view>
          </block>
        </view>
      </view>

      <!-- 明细记录列表 -->
      <view class="records-container">
        <block wx:for="{{records}}" wx:key="id">
          <view class="record-item">
            <view class="record-left">
              <view class="record-desc">{{item.description}}</view>
              <view class="record-time">{{item.created_at_formatted}}</view>
              <view class="record-extra" wx:if="{{item.order_no}}">
                <text class="extra-label">订单号：</text>
                <text class="extra-value">{{item.order_no}}</text>
              </view>
            </view>
            <view class="record-right">
              <view class="record-amount {{item.amount > 0 ? 'positive' : 'negative'}}">
                {{item.amount > 0 ? '+' : ''}}¥{{item.amount}}
              </view>
              <view class="record-balance">余额: ¥{{item.balance_after || '0.00'}}</view>
            </view>
          </view>
        </block>

        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{loadingRecords}}">
          <view class="loading-spinner"></view>
          <text>加载中...</text>
        </view>

        <!-- 没有更多数据 -->
        <view class="no-more" wx:if="{{!hasMoreRecords && records.length > 0}}">
          <text>没有更多记录了</text>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{!loadingRecords && records.length === 0}}">
          <image class="empty-icon" src="/images/icons2/空状态.svg"></image>
          <text class="empty-text">暂无{{currentTabName}}记录</text>
        </view>
      </view>
    </view>
  </block>
</view>