const db = require('../../config/db');
const User = require('../../models/User');
const Cart = require('../../models/Cart');
const { CustomerOrder, CustomerSubOrder, CustomerOrderItem } = require('../../models/CustomerOrder');
const OrderService = require('../../services/order/orderService');
const PaymentService = require('../../services/order/paymentService');
const InventoryService = require('../../services/order/inventoryService');
const FundAllocationService = require('../../services/order/fundAllocationService');

/**
 * 顾客端订单控制器
 * 处理顾客相关的订单操作
 */
class CustomerOrderController {
  /**
   * 创建购物车订单
   * POST /api/customer/orders/cart
   */
  static async createCartOrder(req, res) {
    try {
      const { user_id, cart_items, delivery_method, address_id, store_no, payment_methods, total_amount } = req.body;
      
      console.log('创建购物车订单请求:', req.body);
      console.log('参数验证详情:', {
        user_id_exists: !!user_id,
        user_id_value: user_id,
        cart_items_exists: !!cart_items,
        cart_items_is_array: Array.isArray(cart_items),
        cart_items_length: cart_items ? cart_items.length : 0,
        cart_items_value: cart_items
      });
      
      // 参数验证
      if (!user_id || !cart_items || !Array.isArray(cart_items) || cart_items.length === 0) {
        console.error('参数验证失败:', {
          user_id_missing: !user_id,
          cart_items_missing: !cart_items,
          cart_items_not_array: !Array.isArray(cart_items),
          cart_items_empty: cart_items && Array.isArray(cart_items) && cart_items.length === 0
        });
        return res.status(400).json({ success: false, message: '参数错误：缺少必要参数' });
      }
      
      // 自提订单必须指定门店
      if (delivery_method === 'self' && !store_no) {
        return res.status(400).json({ success: false, message: '自提订单必须选择门店' });
      }
      
      // 数据类型转换
      const userId = String(user_id); // user_id是业务ID，应为字符串类型
      const totalAmountNum = parseFloat(total_amount || 0);
      
      // 验证用户是否存在
      console.log('查找用户，userId:', userId, '类型:', typeof userId);
      const user = await User.findByUserId(userId);
      console.log('用户查找结果:', user);
      if (!user) {
        console.error('用户不存在，userId:', userId);
        return res.status(404).json({ success: false, message: '用户不存在' });
      }
      console.log('用户验证通过，用户信息:', { user_id: user.user_id, username: user.username });
      
      // 查询购物车商品详情并重新计算总金额
      let calculatedTotal = 0;
      const validCartItems = [];
      
      for (const item of cart_items) {
        const productQuery = 'SELECT * FROM products WHERE id = ?';
        const productResult = await db.query(productQuery, [item.product_id]);
        
        if (productResult.length === 0) {
          return res.status(404).json({ success: false, message: `商品ID ${item.product_id} 不存在` });
        }
        
        const product = productResult[0];
        const itemTotal = parseFloat(product.price) * parseInt(item.quantity);
        calculatedTotal += itemTotal;
        
        validCartItems.push({
          product_id: item.product_id,
          quantity: parseInt(item.quantity),
          price: parseFloat(product.price),
          total: itemTotal,
          product: product
        });
      }
      
      // 检查平台库存是否充足
      const stockCheck = await InventoryService.checkPlatformStock(validCartItems);
      if (!stockCheck.sufficient) {
        return res.status(400).json({
          success: false,
          message: '库存不足',
          insufficient_items: stockCheck.insufficientItems
        });
      }
      
      // 生成订单号
      const orderNo = OrderService.generateCustomerOrderNo();
      
      // 获取用户地址信息
      let addressInfo = null;
      if (address_id) {
        const addressQuery = 'SELECT * FROM user_addresses WHERE id = ? AND user_id = ?';
        const addressResult = await db.query(addressQuery, [address_id, userId]);
        if (addressResult.length > 0) {
          addressInfo = addressResult[0];
        }
      }
      
      // 开始事务
      const connection = await db.getConnection();
      try {
        await connection.beginTransaction();
        
        // 计算总数量
        const totalQuantity = validCartItems.reduce((sum, item) => sum + parseInt(item.quantity || 0), 0);

        // 创建主订单
        const mainOrderQuery = `
          INSERT INTO customer_orders
          (user_id, order_no, salesman_id, total_quantity, total_amount, delivery_method, address_id, payment_methods, status, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        const mainOrderResult = await connection.execute(mainOrderQuery, [
          userId,
          orderNo,
          user.salesman_id || null, // 根据业务文档，订单业绩归属销售人
          totalQuantity,
          calculatedTotal,
          delivery_method === 'self' ? 'self' : 'express', // 规范化配送方式：只有express和self两种
          address_id || null,
          JSON.stringify(payment_methods || ['微信支付']),
          '待支付', // 使用中文状态
          Date.now(),
          Date.now()
        ]);
        
        const mainOrderId = mainOrderResult[0].insertId;
        
        // 按库存分配策略拆分订单
        let allocations;
        if (delivery_method === 'self' && store_no) {
          // 自提订单：直接分配到指定门店
          allocations = await InventoryService.allocateStockToSpecificStore(validCartItems, store_no, delivery_method);
        } else {
          // 快递订单：按优先级分配
          allocations = await InventoryService.allocateStockByPriority(validCartItems, userId, delivery_method);
        }
        
        // 扣减库存
        await InventoryService.batchDeductStock(allocations, connection, delivery_method);
        
        // 按门店聚合商品，创建子订单
        const storeGroups = new Map(); // 用于按门店聚合商品

        // 聚合所有商品到对应的门店
        for (const allocation of allocations) {
          for (const storeAllocation of allocation.allocations) {
            const storeNo = storeAllocation.store_no || 'platform';

            if (!storeGroups.has(storeNo)) {
              storeGroups.set(storeNo, {
                store_no: storeNo,
                items: [],
                total_amount: 0,
                total_quantity: 0
              });
            }

            const product = validCartItems.find(item => item.product_id === allocation.product_id);
            const itemSubtotal = product.price * storeAllocation.quantity;

            storeGroups.get(storeNo).items.push({
              product_id: allocation.product_id,
              product_name: product.product ? product.product.name : '商品',
              product_price: product.price,
              quantity: storeAllocation.quantity,
              subtotal: itemSubtotal
            });

            storeGroups.get(storeNo).total_amount += itemSubtotal;
            storeGroups.get(storeNo).total_quantity += storeAllocation.quantity;
          }
        }
        
        // 为每个门店创建一个子订单
        for (const [storeNo, storeGroup] of storeGroups) {
          // 生成子订单号
          const subOrderNo = OrderService.generateSubOrderNo();
          
          // 创建子订单
          const subOrderQuery = `
            INSERT INTO customer_sub_orders
            (main_order_id, sub_order_no, store_no, sub_total_quantity, sub_total_amount, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `;

          const subOrderResult = await connection.execute(subOrderQuery, [
            mainOrderId,
            subOrderNo,
            storeNo,
            storeGroup.total_quantity,
            storeGroup.total_amount,
            '待支付', // 使用中文状态
            Date.now(),
            Date.now()
          ]);
          
          const subOrderId = subOrderResult[0].insertId;
          
          // 为该子订单创建所有商品记录
          for (const item of storeGroup.items) {
            const orderItemQuery = `
              INSERT INTO customer_order_items 
              (main_order_id, sub_order_id, product_id, product_name, product_price, quantity, subtotal, created_at)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `;
            
            await connection.execute(orderItemQuery, [
              mainOrderId,
              subOrderId,
              item.product_id,
              item.product_name,
              item.product_price,
              item.quantity,
              item.subtotal,
              Date.now()
            ]);
          }
        }
        
        // 清空购物车中的已下单商品
        for (const item of validCartItems) {
          await connection.execute('DELETE FROM cart WHERE userId = ? AND productId = ?', [userId, item.product_id]);
        }
        
        // 提交事务
        await connection.commit();
        
        // 事务提交后进行资金分配
        try {
          // 按门店进行资金分配
          for (const [storeNo, storeGroup] of storeGroups) {
            // 根据业务文档：订单创建后立即进行资金分配
            await FundAllocationService.allocateOrderFunds(
              mainOrderId, 
              storeNo, 
              'customer', 
              storeGroup.total_amount
            );
          }
        } catch (fundError) {
          console.error('资金分配失败:', fundError);
          // 资金分配失败不影响订单创建，只记录错误
        }
        
        res.json({
          success: true,
          message: '订单创建成功',
          data: {
            orderId: mainOrderId,
            orderNo: orderNo,
            totalAmount: calculatedTotal,
            status: '待支付', // 使用中文状态
            paid: false // 新创建的订单默认未支付
          }
        });
        
      } catch (error) {
        // 回滚事务和库存
        await connection.rollback();
        console.error('创建订单失败，回滚事务:', error);
        throw error;
      } finally {
        connection.release();
      }
      
    } catch (error) {
      console.error('创建购物车订单失败:', error);
      res.status(500).json({ success: false, message: '创建订单失败', error: error.message });
    }
  }

  /**
   * 处理订单支付
   * POST /api/customer/orders/:orderId/pay
   */
  static async processOrderPayment(req, res) {
    let connection = null;
    try {
      const { orderId } = req.params;
      const { user_id, payment_method } = req.body;
      
      console.log('处理订单支付:', { orderId, user_id, payment_method });
      
      // 参数验证
      if (!orderId || !user_id || !payment_method) {
        return res.status(400).json({ success: false, message: '参数错误' });
      }
      
      // 获取数据库连接并开始事务，使用行锁防止并发支付
      connection = await db.getConnection();
      await connection.beginTransaction();
      
      // 使用 SELECT ... FOR UPDATE 锁定订单记录，防止并发支付
      const [orderRows] = await connection.execute(
        'SELECT * FROM customer_orders WHERE id = ? AND user_id = ? FOR UPDATE',
        [orderId, user_id]
      );
      
      if (orderRows.length === 0) {
        await connection.rollback();
        return res.status(404).json({ success: false, message: '订单不存在或无权限访问' });
      }
      
      const order = orderRows[0];
      
      // 检查订单状态
      if (order.status !== '待支付' && order.status !== '待付款') {
        await connection.rollback();
        return res.status(400).json({ success: false, message: '订单状态不允许支付' });
      }
      
      // 根据支付方式处理
      if (payment_method === 'balance') {
        // 余额支付 - 使用当前事务连接
        const paymentResult = await PaymentService.processBalancePaymentWithConnection(
          user_id, 
          order.total_amount, 
          orderId, 
          'customer',
          connection
        );
        
        if (paymentResult.success) {
          // 查询该主订单下的所有子订单
          const [subOrderRows] = await connection.execute(
            'SELECT id, store_no, sub_total_amount FROM customer_sub_orders WHERE main_order_id = ?',
            [orderId]
          );

          // 根据配送方式确定订单状态
          const statusText = order.delivery_method === 'self' ? '待自提' : '待发货';
          
          // 更新主订单状态
          await connection.execute(
            'UPDATE customer_orders SET status = ?, updated_at = ?, paid_at = ? WHERE id = ?',
            [statusText, Date.now(), Date.now(), orderId]
          );
          
          // 更新子订单状态
          await connection.execute(
            'UPDATE customer_sub_orders SET status = ?, updated_at = ? WHERE main_order_id = ?',
            [statusText, Date.now(), orderId]
          );

          // 对每个子订单进行资金分配
          for (const subOrder of subOrderRows) {
            console.log('处理子订单资金分配:', { orderId, store_no: subOrder.store_no, amount: subOrder.sub_total_amount });
            try {
              await FundAllocationService.allocateOrderFunds(orderId, subOrder.store_no, 'customer', subOrder.sub_total_amount, connection);
              console.log('资金分配成功:', { orderId, store_no: subOrder.store_no });
            } catch (fundError) {
              console.error('资金分配失败详细信息:', {
                orderId,
                store_no: subOrder.store_no,
                amount: subOrder.sub_total_amount,
                error: fundError.message,
                stack: fundError.stack
              });
              throw fundError; // 重新抛出错误
            }
          }

          // 提交事务
          await connection.commit();
        } else {
          // 支付失败，回滚事务
          await connection.rollback();
          res.json(paymentResult);
          return;
        }
        
        res.json(paymentResult);
      } else if (payment_method === 'balance_priority') {
        // 余额优先支付 - 使用当前事务连接
        // 先检查用户余额
        const userBalance = await PaymentService.getUserBalance(user_id);
        const orderAmount = parseFloat(order.total_amount);
        
        if (userBalance >= orderAmount) {
          // 余额足够，使用余额支付
          const paymentResult = await PaymentService.processBalancePaymentWithConnection(
            user_id, 
            order.total_amount, 
            orderId, 
            'customer',
            connection
          );
          
          if (paymentResult.success) {
            // 查询该主订单下的所有子订单
            const [subOrderRows] = await connection.execute(
              'SELECT id, store_no, sub_total_amount FROM customer_sub_orders WHERE main_order_id = ?',
              [orderId]
            );

            // 根据配送方式确定订单状态
            const statusText = order.delivery_method === 'self' ? '待自提' : '待发货';
            
            // 更新主订单状态
            await connection.execute(
              'UPDATE customer_orders SET status = ?, updated_at = ?, paid_at = ? WHERE id = ?',
              [statusText, Date.now(), Date.now(), orderId]
            );
            
            // 更新子订单状态
            await connection.execute(
              'UPDATE customer_sub_orders SET status = ?, updated_at = ? WHERE main_order_id = ?',
              [statusText, Date.now(), orderId]
            );

            // 对每个子订单进行资金分配
            for (const subOrder of subOrderRows) {
              console.log('处理子订单资金分配:', { orderId, store_no: subOrder.store_no, amount: subOrder.sub_total_amount });
              await FundAllocationService.allocateOrderFunds(orderId, subOrder.store_no, 'customer', subOrder.sub_total_amount, connection);
            }

            // 提交事务
            await connection.commit();
            
            // 返回余额支付成功结果
            res.json({
              success: true,
              message: '余额支付成功',
              data: {
                paymentMethod: 'balance',
                remainingBalance: userBalance - orderAmount,
                paidAmount: orderAmount
              }
            });
          } else {
            // 支付失败，回滚事务
            await connection.rollback();
            res.json(paymentResult);
          }
        } else {
          // 余额不足，返回微信支付信息（开发阶段模拟成功）
          console.log('余额不足，需要微信支付:', { userBalance, orderAmount });
          
          // 根据配送方式确定订单状态
          const statusText = order.delivery_method === 'self' ? '待自提' : '待发货';
          
          // 开发阶段直接模拟微信支付成功，更新订单状态
          await connection.execute('UPDATE customer_orders SET status = ?, updated_at = ?, paid_at = ? WHERE id = ?',
            [statusText, Date.now(), Date.now(), orderId]);
          
          // 同时更新子订单状态
          await connection.execute('UPDATE customer_sub_orders SET status = ?, updated_at = ? WHERE main_order_id = ?',
            [statusText, Date.now(), orderId]);
          
          // 查询该主订单下的所有子订单进行资金分配
          const [subOrderRows] = await connection.execute(
            'SELECT id, store_no, sub_total_amount FROM customer_sub_orders WHERE main_order_id = ?',
            [orderId]
          );
          
          // 对每个子订单进行资金分配
          for (const subOrder of subOrderRows) {
            console.log('处理子订单资金分配:', { orderId, store_no: subOrder.store_no, amount: subOrder.sub_total_amount });
            await FundAllocationService.allocateOrderFunds(orderId, subOrder.store_no, 'customer', subOrder.sub_total_amount, connection);
          }
          
          // 提交事务
          await connection.commit();
            
          res.json({
            success: true,
            message: '微信支付成功（开发模拟）',
            data: {
              paymentMethod: 'wxpay',
              paidAmount: orderAmount,
              userBalance: userBalance
            }
          });
        }
      } else if (payment_method === 'wxpay') {
        // 微信支付（开发阶段模拟成功）- 使用当前事务连接
        console.log('微信支付（开发模拟）');
        
        // 根据配送方式确定订单状态
        const statusText = order.delivery_method === 'self' ? '待自提' : '待发货';
        
        // 开发阶段直接模拟微信支付成功，更新订单状态
        await connection.execute('UPDATE customer_orders SET status = ?, updated_at = ?, paid_at = ? WHERE id = ?',
          [statusText, Date.now(), Date.now(), orderId]);
        
        // 同时更新子订单状态
        await connection.execute('UPDATE customer_sub_orders SET status = ?, updated_at = ? WHERE main_order_id = ?',
          [statusText, Date.now(), orderId]);
        
        // 查询该主订单下的所有子订单进行资金分配
        const [subOrderRows] = await connection.execute(
          'SELECT id, store_no, sub_total_amount FROM customer_sub_orders WHERE main_order_id = ?',
          [orderId]
        );
        
        // 对每个子订单进行资金分配
        for (const subOrder of subOrderRows) {
          console.log('处理子订单资金分配:', { orderId, store_no: subOrder.store_no, amount: subOrder.sub_total_amount });
          await FundAllocationService.allocateOrderFunds(orderId, subOrder.store_no, 'customer', subOrder.sub_total_amount, connection);
        }
        
        // 提交事务
        await connection.commit();
        
        res.json({
          success: true,
          message: '微信支付成功（开发模拟）',
          data: {
            paymentMethod: 'wxpay',
            paidAmount: parseFloat(order.total_amount)
          }
        });
      } else {
        // 不支持的支付方式
        await connection.rollback();
        res.json({ success: false, message: '暂不支持该支付方式' });
      }
      
    } catch (error) {
      // 回滚事务
      if (connection) {
        try {
          await connection.rollback();
        } catch (rollbackError) {
          console.error('事务回滚失败:', rollbackError);
        }
      }
      
      console.error('处理订单支付失败:', error);
      res.status(500).json({ success: false, message: '支付失败', error: error.message });
    } finally {
      // 释放数据库连接
      if (connection) {
        try {
          connection.release();
        } catch (releaseError) {
          console.error('释放数据库连接失败:', releaseError);
        }
      }
    }
  }

  /**
   * 获取用户订单列表
   * GET /api/customer/orders
   */
  static async getUserOrders(req, res) {
    try {
      const { user_id, status, page = 1, limit = 10, search } = req.query;

      if (!user_id) {
        return res.status(400).json({ success: false, message: '缺少用户ID' });
      }

      const offset = (parseInt(page) - 1) * parseInt(limit);

      // 构建查询条件
      let whereClause = 'WHERE co.user_id = ?';
      const params = [String(user_id)]; // user_id是业务ID，应为字符串类型

      // 只有当status不为空且不为'all'时才添加状态过滤条件
      if (status && status !== 'all' && status.trim() !== '') {
        whereClause += ' AND co.status = ?';
        params.push(status);
      }

      // 新增：配送方式过滤（仅当传入有效值时生效）
      const deliveryMethodRaw = (req.query.delivery_method || '').trim();
      if (deliveryMethodRaw) {
        // 规范化为self或express
        const dm = deliveryMethodRaw.toLowerCase();
        const normalized = dm === 'self' || dm === '自提' || dm === 'pickup'
          ? 'self'
          : (dm === 'express' || dm === '快递' || dm === 'delivery' ? 'express' : '');
        if (normalized) {
          whereClause += ' AND co.delivery_method = ?';
          params.push(normalized);
        }
      }

      // 添加搜索条件
      if (search && search.trim()) {
        whereClause += ` AND (co.order_no LIKE ? OR EXISTS (
          SELECT 1 FROM customer_order_items coi
          WHERE coi.main_order_id = co.id
          AND coi.product_name LIKE ?
        ))`;
        const searchPattern = `%${search.trim()}%`;
        params.push(searchPattern, searchPattern);
      }
      
      // 查询订单列表
      const ordersQuery = `
        SELECT 
          co.*,
          COUNT(cso.id) as sub_order_count
        FROM customer_orders co
        LEFT JOIN customer_sub_orders cso ON co.id = cso.main_order_id
        ${whereClause}
        GROUP BY co.id
        ORDER BY co.created_at DESC
        LIMIT ? OFFSET ?
      `;
      
      params.push(parseInt(limit), offset);
      
      const orders = await db.query(ordersQuery, params);
      
      // 查询总数（排除分页参数）
      const countParams = params.slice(0, params.length - 2); // 移除limit和offset参数
      const countQuery = `SELECT COUNT(*) as total FROM customer_orders co ${whereClause}`;
      const countResult = await db.query(countQuery, countParams);
      const total = countResult[0].total;
      
      // 为每个订单获取商品项目信息
      const formattedOrders = await Promise.all(orders.map(async (order) => {
        // 获取订单项目
        const itemsQuery = `
          SELECT 
            coi.*,
            p.images as product_image
          FROM customer_order_items coi
          LEFT JOIN products p ON coi.product_id = p.id
          WHERE coi.main_order_id = ?
          ORDER BY coi.created_at
        `;
        const items = await db.query(itemsQuery, [order.id]);
        
        // 格式化商品项目数据
        const formattedItems = items.map(item => ({
          id: item.id,
          name: item.product_name,
          price: item.product_price,
          quantity: item.quantity,
          subtotal: item.subtotal,
          image: item.product_image,
          specs: item.specs || '' // 规格信息，如果有的话
        }));
        
        // 计算订单总数量
        const totalQuantity = formattedItems.reduce((sum, item) => sum + item.quantity, 0);
        
        return {
          ...order,
          items: formattedItems,
          total_quantity: totalQuantity,
          status_text: OrderService.getStatusText(order.status),
          payment_methods_text: OrderService.getPaymentMethodText(order.payment_methods),
          created_at_formatted: OrderService.formatDate(order.created_at),
          updated_at_formatted: OrderService.formatDate(order.updated_at),
          paid_at_formatted: OrderService.formatDate(order.paid_at),
          shipped_at_formatted: OrderService.formatDate(order.shipped_at),
          completed_at_formatted: OrderService.formatDate(order.completed_at),
          cancelled_at_formatted: OrderService.formatDate(order.cancelled_at)
        };
      }));
      
      res.json({
        success: true,
        data: {
          orders: formattedOrders,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / parseInt(limit))
          }
        }
      });
      
    } catch (error) {
      console.error('获取用户订单列表失败:', error);
      res.status(500).json({ success: false, message: '获取订单列表失败', error: error.message });
    }
  }

  /**
   * 获取订单详情
   * GET /api/customer/orders/:orderId
   */
  static async getOrderDetail(req, res) {
    try {
      const { orderId } = req.params;
      const { user_id } = req.query;
      
      if (!user_id) {
        return res.status(400).json({ success: false, message: '缺少用户ID' });
      }
      
      // 验证订单权限
      const order = await OrderService.validateOrderPermission(orderId, user_id, 'customer');
      if (!order) {
        return res.status(404).json({ success: false, message: '订单不存在或无权限访问' });
      }
      
      // 查询子订单和商品详情
      const subOrdersQuery = `
        SELECT 
          cso.*,
          s.name as store_name,
          s.address as store_address
        FROM customer_sub_orders cso
        LEFT JOIN stores s ON cso.store_no = s.store_no
        WHERE cso.main_order_id = ?
        ORDER BY cso.created_at
      `;
      
      const subOrders = await db.query(subOrdersQuery, [orderId]);
      
      // 查询每个子订单的商品详情
      for (const subOrder of subOrders) {
        const itemsQuery = `
          SELECT 
            coi.*,
            p.name as product_name,
            p.images as product_image,
            p.description as product_description
          FROM customer_order_items coi
          LEFT JOIN products p ON coi.product_id = p.id
          WHERE coi.sub_order_id = ?
        `;
        
        subOrder.items = await db.query(itemsQuery, [subOrder.id]);
      }
      
      // 查询地址信息
      let addressInfo = null;
      if (order.address_id) {
        const addressQuery = 'SELECT * FROM user_addresses WHERE id = ?';
        const addressResult = await db.query(addressQuery, [order.address_id]);
        if (addressResult.length > 0) {
          addressInfo = addressResult[0];
        }
      }
      
      // 格式化订单数据
      const orderDetail = {
        ...order,
        payment_methods_text: OrderService.getPaymentMethodText(order.payment_methods),
        created_at_formatted: OrderService.formatDate(order.created_at),
        updated_at_formatted: OrderService.formatDate(order.updated_at),
        paid_at_formatted: OrderService.formatDate(order.paid_at),
        shipped_at_formatted: OrderService.formatDate(order.shipped_at),
        completed_at_formatted: OrderService.formatDate(order.completed_at),
        cancelled_at_formatted: OrderService.formatDate(order.cancelled_at),
        sub_orders: subOrders.map(subOrder => ({
          ...subOrder,
          created_at_formatted: OrderService.formatDate(subOrder.created_at),
          updated_at_formatted: OrderService.formatDate(subOrder.updated_at),
          shipped_at_formatted: OrderService.formatDate(subOrder.shipped_at),
          completed_at_formatted: OrderService.formatDate(subOrder.completed_at)
        })),
        address: addressInfo
      };
      
      res.json({
        success: true,
        data: orderDetail
      });
      
    } catch (error) {
      console.error('获取订单详情失败:', error);
      res.status(500).json({ success: false, message: '获取订单详情失败', error: error.message });
    }
  }

  /**
   * 取消订单
   * POST /api/customer/orders/:orderId/cancel
   */
  static async cancelOrder(req, res) {
    try {
      const { orderId } = req.params;
      const { user_id, reason } = req.body;
      
      if (!user_id) {
        return res.status(400).json({ success: false, message: '缺少用户ID' });
      }
      
      // 验证订单权限
      const order = await OrderService.validateOrderPermission(orderId, user_id, 'customer');
      if (!order) {
        return res.status(404).json({ success: false, message: '订单不存在或无权限访问' });
      }
      
      // 检查订单状态（只有待支付和待发货的订单可以取消）
      if (!['待支付', '待付款', '待发货'].includes(order.status)) {
        return res.status(400).json({ success: false, message: '当前订单状态不允许取消' });
      }
      
      const connection = await db.getConnection();
      try {
        await connection.beginTransaction();
        
        // 如果订单已支付，需要退款
        if (order.status === '待发货') {
          await PaymentService.processRefund(orderId, order.total_amount, 'customer', connection);
        }
        
        // 回滚库存
        const subOrdersQuery = `
          SELECT cso.*, coi.product_id, coi.quantity 
          FROM customer_sub_orders cso
          LEFT JOIN customer_order_items coi ON cso.id = coi.sub_order_id
          WHERE cso.main_order_id = ?
        `;
        const subOrders = await connection.query(subOrdersQuery, [orderId]);
        
        // 构建库存回滚数据
        const allocations = [];
        const productMap = new Map();
        
        for (const subOrder of subOrders) {
          if (!productMap.has(subOrder.product_id)) {
            productMap.set(subOrder.product_id, {
              product_id: subOrder.product_id,
              total_quantity: 0,
              allocations: []
            });
          }
          
          const allocation = productMap.get(subOrder.product_id);
          allocation.total_quantity += subOrder.quantity;
          allocation.allocations.push({
            store_no: subOrder.store_no,
            product_id: subOrder.product_id,
            quantity: subOrder.quantity,
            source: subOrder.store_no ? 'store' : 'platform'
          });
        }
        
        allocations.push(...productMap.values());
        
        // 回滚库存
        await InventoryService.batchRollbackStock(allocations, connection);
        
        // 更新订单状态
        await connection.execute(
          'UPDATE customer_orders SET status = ?, cancel_reason = ?, updated_at = ? WHERE id = ?',
          ['已取消', reason || '用户取消', Date.now(), orderId]
        );
        
        // 更新子订单状态
        await connection.execute(
          'UPDATE customer_sub_orders SET status = ?, updated_at = ? WHERE main_order_id = ?',
          ['已取消', Date.now(), orderId]
        );
        
        await connection.commit();
        
        res.json({
          success: true,
          message: '订单取消成功'
        });
        
      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }
      
    } catch (error) {
      console.error('取消订单失败:', error);
      res.status(500).json({ success: false, message: '取消订单失败', error: error.message });
    }
  }

  /**
   * 确认收货
   * POST /api/customer/orders/:orderId/confirm
   */
  static async confirmReceipt(req, res) {
    try {
      const { orderId } = req.params;
      const { user_id } = req.body;
      
      if (!user_id) {
        return res.status(400).json({ success: false, message: '缺少用户ID' });
      }
      
      // 验证订单权限
      const order = await OrderService.validateOrderPermission(orderId, user_id, 'customer');
      if (!order) {
        return res.status(404).json({ success: false, message: '订单不存在或无权限访问' });
      }
      
      // 检查订单状态
      if (order.status !== '待收货' && order.status !== '已发货') {
        return res.status(400).json({ success: false, message: '当前订单状态不允许确认收货' });
      }
      
      const connection = await db.getConnection();
      try {
        await connection.beginTransaction();
        
        // 更新订单状态
        await connection.execute(
          'UPDATE customer_orders SET status = ?, completed_at = ?, updated_at = ? WHERE id = ?',
          ['已完成', Date.now(), Date.now(), orderId]
        );
        
        // 更新子订单状态
        await connection.execute(
          'UPDATE customer_sub_orders SET status = ?, updated_at = ? WHERE main_order_id = ?',
          ['已完成', Date.now(), orderId]
        );
        
        // 处理订单完成 - 将待结分佣转入账户余额
        await FundAllocationService.processOrderCompletion(orderId);
        
        await connection.commit();
        
        res.json({
          success: true,
          message: '确认收货成功'
        });
        
      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }
      
    } catch (error) {
      console.error('确认收货失败:', error);
      res.status(500).json({ success: false, message: '确认收货失败', error: error.message });
    }
  }

  /**
   * 申请退款
   * POST /api/customer/orders/:orderId/refund
   */
  static async applyRefund(req, res) {
    try {
      const { orderId } = req.params;
      const { user_id, reason, description } = req.body;
      
      if (!user_id || !reason) {
        return res.status(400).json({ success: false, message: '参数错误' });
      }
      
      // 验证订单权限
      const order = await OrderService.validateOrderPermission(orderId, user_id, 'customer');
      if (!order) {
        return res.status(404).json({ success: false, message: '订单不存在或无权限访问' });
      }
      
      // 检查订单状态
      if (!['待发货', '待收货', '已发货', '已完成'].includes(order.status)) {
        return res.status(400).json({ success: false, message: '当前订单状态不支持退款' });
      }
      
      // 检查是否已经申请过退款
      const existingRefundQuery = 'SELECT id FROM order_refunds WHERE order_id = ? AND order_type = "customer"';
      const existingRefund = await db.query(existingRefundQuery, [orderId]);
      if (existingRefund.length > 0) {
        return res.status(400).json({ success: false, message: '该订单已申请过退款' });
      }
      
      // 创建退款申请
      const refundQuery = `
        INSERT INTO order_refunds 
        (order_id, order_type, user_id, amount, reason, description, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      await db.query(refundQuery, [
        orderId,
        'customer',
        user_id,
        order.total_amount,
        reason,
        description || '',
        '待审核',
        Date.now(),
        Date.now()
      ]);
      
      // 更新订单状态
      await db.query('UPDATE customer_orders SET status = ?, updated_at = ? WHERE id = ?', 
        ['退款/售后', Date.now(), orderId]);
      
      res.json({
        success: true,
        message: '退款申请提交成功，请等待审核'
      });
      
    } catch (error) {
      console.error('申请退款失败:', error);
      res.status(500).json({ success: false, message: '申请退款失败', error: error.message });
    }
  }

  /**
   * 查看退款进度
   * GET /api/customer/orders/:orderId/refund
   */
  static async getRefundDetail(req, res) {
    try {
      const { orderId } = req.params;
      const { user_id } = req.query;
      
      if (!user_id) {
        return res.status(400).json({ success: false, message: '缺少用户ID' });
      }
      
      // 验证订单权限
      const order = await OrderService.validateOrderPermission(orderId, user_id, 'customer');
      if (!order) {
        return res.status(404).json({ success: false, message: '订单不存在或无权限访问' });
      }
      
      // 查询退款信息
      const refundQuery = 'SELECT * FROM order_refunds WHERE order_id = ? AND order_type = "customer"';
      const refundResult = await db.query(refundQuery, [orderId]);
      
      if (refundResult.length === 0) {
        return res.status(404).json({ success: false, message: '未找到退款申请' });
      }
      
      const refund = refundResult[0];
      refund.created_at_formatted = OrderService.formatDate(refund.created_at);
      refund.updated_at_formatted = OrderService.formatDate(refund.updated_at);
      
      res.json({
        success: true,
        data: refund
      });
      
    } catch (error) {
      console.error('获取退款详情失败:', error);
      res.status(500).json({ success: false, message: '获取退款详情失败', error: error.message });
    }
  }

  /**
   * 删除订单
   * DELETE /api/customer/orders/:orderId
   */
  static async deleteOrder(req, res) {
    try {
      const { orderId } = req.params;
      const { user_id } = req.body;
      
      if (!user_id) {
        return res.status(400).json({ success: false, message: '缺少用户ID' });
      }
      
      // 验证订单权限
      const order = await OrderService.validateOrderPermission(orderId, user_id, 'customer');
      if (!order) {
        return res.status(404).json({ success: false, message: '订单不存在或无权限访问' });
      }
      
      // 检查订单状态（只有已取消或已完成的订单可以删除）
      if (!['已取消', '已完成'].includes(order.status)) {
        return res.status(400).json({ success: false, message: '当前订单状态不允许删除' });
      }
      
      // 软删除订单
      await db.query('UPDATE customer_orders SET deleted_at = ?, updated_at = ? WHERE id = ?', 
        [Date.now(), Date.now(), orderId]);
      
      res.json({
        success: true,
        message: '订单删除成功'
      });
      
    } catch (error) {
      console.error('删除订单失败:', error);
      res.status(500).json({ success: false, message: '删除订单失败', error: error.message });
    }
  }
}

module.exports = CustomerOrderController;