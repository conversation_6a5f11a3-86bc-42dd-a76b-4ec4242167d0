<!-- 账户设置页面 -->
<view class="account-settings page-content">
  <!-- 设置选项 -->
  <view class="settings-list">
    <!-- 快捷通道模块 -->
    <view class="settings-group">
      <view class="group-title">快捷通道</view>
      <view class="settings-item">
        <view class="item-left">
          <image class="item-icon" src="/images/icons2/快捷通道.svg" />
          <text class="item-text">启动时直接进入合伙人端</text>
        </view>
        <switch class="quick-access-switch" checked="{{quickAccessEnabled}}" bindchange="onQuickAccessChange" color="#007aff" />
      </view>
      <view class="quick-access-desc">
        <text class="desc-text">开启后，当您是合伙人且已登录时，启动小程序将直接进入合伙人端</text>
      </view>
    </view>

    <view class="settings-group">
      <view class="group-title">其他</view>
      <view class="settings-item" bindtap="logout">
        <view class="item-left">
          <image class="item-icon" src="/images/icons2/退出.png" />
          <text class="item-text logout-text">退出登录</text>
        </view>
        <image class="arrow-icon" src="/images/icons2/向右.png" />
      </view>
    </view>
  </view>
</view>