// pages/filter/filter.js
Page({
  data: {
    // 主题数据
    topics: [
      { id: 1, name: '工商财税', selected: false },
      { id: 2, name: '企业转让', selected: false },
      { id: 3, name: '地址托管', selected: false },
      { id: 4, name: '疑难业务', selected: false },
      { id: 5, name: '企业征信', selected: false },
      { id: 6, name: '异常处理', selected: false },
      { id: 7, name: '资质代办', selected: false },
      { id: 8, name: '知识产权', selected: false },
      { id: 9, name: '高新科技', selected: false },
      { id: 10, name: '老板圈子', selected: false },
      { id: 11, name: '其他', selected: false }
    ],
    // 区域选择
    region: ['全部', '全部', '全部'],
    regionText: '请选择地区'
  },

  onLoad: function (options) {
    // 如果有传入的筛选条件，则恢复之前的选择
    try {
      const filterParams = wx.getStorageSync('filterParams');

      if (filterParams) {
        // 恢复区域选择
        if (filterParams.region && Array.isArray(filterParams.region) && filterParams.region.length === 3) {
          this.setData({
            region: filterParams.region,
            regionText: this.formatRegionText(filterParams.region)
          });
        }

        // 恢复主题选择
        if (filterParams.selectedTopicIds && Array.isArray(filterParams.selectedTopicIds)) {
          const topics = this.data.topics.map(topic => {
            return {
              ...topic,
              selected: filterParams.selectedTopicIds.includes(topic.id)
            };
          });

          this.setData({ topics });
        } else if (filterParams.topics && Array.isArray(filterParams.topics)) {
          // 处理主题数据格式
          const topics = this.data.topics.map(topic => {
            return {
              ...topic,
              selected: filterParams.topics.includes(topic.id)
            };
          });

          this.setData({ topics });
        }
      }
    } catch (error) {
      console.error('Error loading filter params:', error);
    }
  },

  // 防止滑动穿透
  preventTouchMove: function() {
    return false;
  },

  // 区域选择器变化
  bindRegionChange: function(e) {
    const region = e.detail.value;
    this.setData({
      region: region,
      regionText: this.formatRegionText(region)
    });
  },

  // 格式化区域文本
  formatRegionText: function(region) {
    if (region[0] === '全部') {
      return '全国';
    } else if (region[1] === '全部') {
      return region[0];
    } else if (region[2] === '全部') {
      return region[0] + ' ' + region[1];
    } else {
      return region[0] + ' ' + region[1] + ' ' + region[2];
    }
  },

  // 切换主题选择
  toggleTopic: function(e) {
    const topicId = parseInt(e.currentTarget.dataset.id);
    console.log('Toggle topic:', topicId);

    const topics = this.data.topics.map(topic => {
      if (topic.id === topicId) {
        return {
          ...topic,
          selected: !topic.selected
        };
      }
      return topic;
    });

    this.setData({ topics });
  },

  // 重置筛选条件
  resetFilter: function() {
    const topics = this.data.topics.map(topic => {
      return {
        ...topic,
        selected: false
      };
    });

    this.setData({
      region: ['全部', '全部', '全部'],
      regionText: '请选择地区',
      topics: topics
    });
  },

  // 取消筛选
  cancelFilter: function() {
    wx.navigateBack();
  },

  // 确认筛选
  confirmFilter: function() {
    try {
      // 获取选中的主题名称
      const selectedTopics = this.data.topics
        .filter(topic => topic.selected)
        .map(topic => topic.name);

      // 获取选中的主题ID
      const selectedTopicIds = this.data.topics
        .filter(topic => topic.selected)
        .map(topic => topic.id);

      // 构建筛选参数
      const filterParams = {
        region: this.data.region,
        regionText: this.data.regionText,
        selectedTopicIds: selectedTopicIds,
        topics: selectedTopics,
        topicNames: selectedTopics
      };
      
      console.log('筛选参数:', JSON.stringify(filterParams, null, 2));

      // 保存筛选参数
      wx.setStorageSync('filterParams', filterParams);

      // 返回上一页并传递参数
      const pages = getCurrentPages();
      if (pages.length > 1) {
        const prevPage = pages[pages.length - 2];

        // 调用上一页的方法，应用筛选条件
        if (typeof prevPage.applyFilter === 'function') {
          prevPage.applyFilter(filterParams);
        }
      }
    } catch (error) {
      console.error('Error saving filter params:', error);
    }

    // 返回上一页
    wx.navigateBack();
  }
});
