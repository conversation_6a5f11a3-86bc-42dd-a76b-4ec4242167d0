const db = require('../config/db');

/**
 * 获取门店资金信息
 * GET /api/store/funds/:storeNo
 */
exports.getStoreFunds = async (req, res) => {
  try {
    const { storeNo } = req.params;
    
    if (!storeNo) {
      return res.status(400).json({ success: false, message: '缺少门店编号' });
    }
    
    // 查询门店信息
    const storeRows = await db.query('SELECT id, store_no, name, capital FROM stores WHERE store_no = ?', [storeNo]);
    
    if (storeRows.length === 0) {
      return res.status(404).json({ success: false, message: '门店不存在' });
    }
    
    const store = storeRows[0];
    
    // 查询门店资金信息
    const fundRows = await db.query('SELECT * FROM store_funds WHERE store_no = ?', [storeNo]);
    
    let fundInfo = null;
    
    if (fundRows.length > 0) {
      fundInfo = fundRows[0];
    } else {
      // 如果门店资金记录不存在，创建默认记录
      const now = Date.now();
      
      try {
        await db.query('INSERT INTO store_funds (store_no, total_shares, capital, profit, reserve_fund, bonus_pool, update_time) VALUES (?, ?, ?, ?, ?, ?, ?)', 
          [storeNo, 0, parseFloat(store.capital) || 0, 0, 0, 0, now]);
      } catch (insertError) {
        console.error('getStoreFunds: 创建默认资金记录失败:', insertError);
        // 即使插入失败，也继续使用默认值
      }
      
      fundInfo = {
        store_no: storeNo,
        total_shares: 0,
        capital: parseFloat(store.capital) || 0,
        profit: 0,
        reserve_fund: 0,
        bonus_pool: 0,
        update_time: now
      };
    }
    
    // 确保所有数值字段都是有效的数字
    const safeFundInfo = {
      store_no: fundInfo.store_no,
      total_shares: parseFloat(fundInfo.total_shares) || 0,
      capital: parseFloat(fundInfo.capital) || 0,
      profit: parseFloat(fundInfo.profit) || 0,
      reserve_fund: parseFloat(fundInfo.reserve_fund) || 0,
      bonus_pool: parseFloat(fundInfo.bonus_pool) || 0,
      update_time: fundInfo.update_time
    };
    
    // 将数据库字段映射为前端期望的字段名
    const mappedFundInfo = {
      ...safeFundInfo,
      dividend: safeFundInfo.profit,      // 利润映射为分红
      fund: safeFundInfo.reserve_fund     // 公积金保持原名
    };
    
    const result = {
      success: true,
      data: {
        store: {
          id: store.id,
          store_no: store.store_no,
          name: store.name
        },
        funds: mappedFundInfo
      }
    };
    
    res.json(result);
    
  } catch (error) {
    console.error('getStoreFunds: 获取门店资金信息失败:', error);
    res.status(500).json({ success: false, message: '获取门店资金信息失败', error: error.message });
  }
};

/**
 * 记录门店资金变动
 * POST /api/store/funds/record
 */
exports.createFundRecord = async (req, res) => {
  try {
    const { store_no, type, amount, description, voucher_images } = req.body;
    
    if (!store_no || !type || !amount) {
      return res.status(400).json({ success: false, message: '缺少必要参数' });
    }
    
    // 查询门店信息
    const storeRows = await db.query('SELECT id FROM stores WHERE store_no = ?', [store_no]);
    if (storeRows.length === 0) {
      return res.status(404).json({ success: false, message: '门店不存在' });
    }
    
    const storeNo = storeRows[0].store_no;
    
    // 生成凭证编号（与管理端保持一致）
    const now = new Date();
    const timeStr = now.getFullYear().toString() +
      String(now.getMonth() + 1).padStart(2, '0') +
      String(now.getDate()).padStart(2, '0') +
      String(now.getHours()).padStart(2, '0') +
      String(now.getMinutes()).padStart(2, '0') +
      String(now.getSeconds()).padStart(2, '0');
    
    // 根据type字段确定account_type
    let account_type = 'fund'; // 默认值
    if (['capital_increase', 'capital_decrease', 'capital_transfer'].includes(type)) {
      account_type = 'capital';
    } else if (['dividend_payment', 'dividend_distribution'].includes(type)) {
      account_type = 'dividend';
    } else if (['fund_deposit', 'fund_withdrawal', 'purchase_payment'].includes(type)) {
      account_type = 'fund';
    }

    // 先插入记录，获取自增ID
    const insertResult = await db.query('INSERT INTO store_fund_records (store_no, type, account_type, amount, description, created_at, voucher_images) VALUES (?, ?, ?, ?, ?, ?, ?)', 
      [store_no, type, account_type, amount, description, now, voucher_images ? JSON.stringify(voucher_images) : null]);
    
    const recordId = insertResult.insertId;
    
    // 生成凭证号：分类缩写+时间戳+自增ID
    const typeShort = type === 'purchase_payment' ? 'HK' : 'QT'; // 采购支付对应"支付货款"
    const voucherNo = `${typeShort}${timeStr}-${String(recordId).padStart(4, '0')}`;
    
    // 更新凭证号
    await db.query('UPDATE store_fund_records SET voucher_no = ? WHERE id = ?', [voucherNo, recordId]);
    
    // 更新门店资金信息
    if (type === 'purchase_payment') {
      // 采购支付：扣减股本金
      await db.query('UPDATE store_funds SET capital = capital - ?, update_time = ? WHERE store_no = ?', 
        [Math.abs(amount), Date.now(), store_no]);
      
      // 同时更新stores表的capital字段
      await db.query('UPDATE stores SET capital = capital - ? WHERE store_no = ?', 
        [Math.abs(amount), store_no]);
    } else if (type === 'profit_income') {
      // 利润收入：增加利润
      await db.query('UPDATE store_funds SET profit = profit + ?, update_time = ? WHERE store_no = ?', 
        [Math.abs(amount), Date.now(), store_no]);
    }
    
    res.json({
      success: true,
      data: {
        voucher_no: voucherNo,
        record_id: recordId
      }
    });
    
  } catch (error) {
    console.error('记录门店资金变动失败:', error);
    res.status(500).json({ success: false, message: '记录门店资金变动失败', error: error.message });
  }
};

/**
 * 获取门店资金变动记录
 * GET /api/store/funds/records/:storeNo
 */
exports.getFundRecords = async (req, res) => {
  try {
    const { storeNo } = req.params;
    const { limit = 20, offset = 0, account_type } = req.query;
    
    if (!storeNo) {
      return res.status(400).json({ success: false, message: '缺少门店编号' });
    }
    
    // 构建查询条件
    let whereClause = 'WHERE store_no = ?';
    let queryParams = [storeNo];
    
    // 如果指定了账户类型，添加过滤条件
    if (account_type && account_type !== 'all') {
      whereClause += ' AND account_type = ?';
      queryParams.push(account_type);
    }
    
    // 查询资金变动记录
    const records = await db.query(
      `SELECT * FROM store_fund_records ${whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?`, 
      [...queryParams, parseInt(limit), parseInt(offset)]
    );
    
    // 查询总记录数
    const countRows = await db.query(`SELECT COUNT(*) as total FROM store_fund_records ${whereClause}`, queryParams);
    const total = countRows[0]?.total || 0;
    
    // 确保记录数据格式正确
      const formattedRecords = records.map(record => ({
        ...record,
        amount: parseFloat(record.amount) || 0
      }));
    
    const result = {
      success: true,
      data: {
        records: formattedRecords,
        total: total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        account_type: account_type || 'all'
      }
    };
    
    res.json(result);
    
  } catch (error) {
    res.status(500).json({ success: false, message: '获取门店资金变动记录失败', error: error.message });
  }
};