const express = require('express');
const router = express.Router();
const Group = require('../models/Group');
const { checkAuth } = require('../middleware/auth');

// 创建群组
router.post('/', checkAuth, async (req, res) => {
  try {
    console.log('创建群组 - 请求体:', JSON.stringify(req.body));
    console.log('创建群组 - 用户信息:', JSON.stringify(req.user));

    const { name, description, visible, isPublic, needApprove } = req.body;

    // 确保用户ID存在
    if (!req.user || !req.user.id) {
      console.error('创建群组失败: 用户ID不存在');
      return res.status(400).json({
        success: false,
        message: '用户信息不完整'
      });
    }

    const creatorId = req.user.id;
    console.log('创建群组 - 创建者ID:', creatorId);

    // 确保群名存在
    if (!name || !name.trim()) {
      return res.status(400).json({
        success: false,
        message: '群名称不能为空'
      });
    }

    // 处理布尔值参数，确保类型正确
    const visibleValue = typeof visible === 'boolean' ? visible :
                       visible === 'true' ? true :
                       visible === 'false' ? false :
                       visible !== undefined ? !!visible : true;

    const isPublicValue = typeof isPublic === 'boolean' ? isPublic :
                        isPublic === 'true' ? true :
                        isPublic === 'false' ? false :
                        isPublic !== undefined ? !!isPublic : visibleValue;

    const needApproveValue = typeof needApprove === 'boolean' ? needApprove :
                           needApprove === 'true' ? true :
                           needApprove === 'false' ? false :
                           needApprove !== undefined ? !!needApprove : false;

    console.log('创建群组 - 处理后的参数:', {
      name,
      description,
      creatorId,
      isPublic: isPublicValue,
      visible: visibleValue,
      needApprove: needApproveValue
    });

    // 使用所有前端传递的参数，补充后端处理的布尔值和 creatorId，避免 avatar 丢失
    const group = await Group.create({
      ...req.body,
      creatorId,
      isPublic: isPublicValue,
      visible: visibleValue,
      needApprove: needApproveValue
    });

    res.json({
      success: true,
      data: group
    });
  } catch (error) {
    console.error('创建群组失败:', error);
    res.status(500).json({
      success: false,
      message: '创建群组失败',
      error: error.message
    });
  }
});

// 获取用户加入的群组列表
router.get('/my-groups', checkAuth, async (req, res) => {
  try {
    const groups = await Group.getUserGroups(req.user.id);
    res.json({
      success: true,
      data: groups
    });
  } catch (error) {
    console.error('获取用户群组列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取群组列表失败'
    });
  }
});

// 获取公开群组列表
router.get('/public-groups', checkAuth, async (req, res) => {
  try {
    const { page = 1, pageSize = 20 } = req.query;
    const groups = await Group.getPublicGroups(req.user.id, parseInt(page), parseInt(pageSize));
    res.json({
      success: true,
      data: groups
    });
  } catch (error) {
    console.error('获取公开群组列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取群组列表失败'
    });
  }
});

// 获取群组详情
router.get('/:groupId', checkAuth, async (req, res) => {
  try {
    const { groupId } = req.params;
    const group = await Group.getById(groupId);

    if (!group) {
      return res.status(404).json({
        success: false,
        message: '群组不存在'
      });
    }

    // 获取创建者信息
    let creatorInfo = null;
    if (group.creatorId) {
      try {
        const User = require('../models/User');
        // 使用findByUserId而不是findById，因为creatorId存储的是user_id（业务ID）
        creatorInfo = await User.findByUserId(group.creatorId);
        if (creatorInfo) {
          creatorInfo = {
            id: creatorInfo.id,
            nickname: creatorInfo.nickname || '未知用户',
            avatar: creatorInfo.avatar || '/images/icons/default-avatar.png'
          };
        }
      } catch (err) {
        console.error('获取创建者信息失败:', err);
      }
    }

    // 格式化群组信息
    const groupDetail = {
      ...group,
      creatorInfo,
      creator: creatorInfo ? creatorInfo.nickname : '未知用户',
      announcement: group.announcement || '',
      cover: group.avatar || group.cover || '/images/icons/group-default.png'
    };

    res.json({
      success: true,
      data: groupDetail
    });
  } catch (error) {
    console.error('获取群组详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取群组详情失败'
    });
  }
});

// 加入群组
router.post('/:groupId/join', checkAuth, async (req, res) => {
  try {
    const { groupId } = req.params;
    const userId = req.user.id;

    await Group.joinGroup(groupId, userId);
    res.json({
      success: true,
      message: '加入群组成功'
    });
  } catch (error) {
    console.error('加入群组失败:', error);
    res.status(500).json({
      success: false,
      message: '加入群组失败'
    });
  }
});

// 退出群组
router.post('/:groupId/leave', checkAuth, async (req, res) => {
  try {
    const { groupId } = req.params;
    const userId = req.user.id;

    await Group.leaveGroup(groupId, userId);
    res.json({
      success: true,
      message: '退出群组成功'
    });
  } catch (error) {
    console.error('退出群组失败:', error);
    res.status(500).json({
      success: false,
      message: '退出群组失败'
    });
  }
});

// 获取群组成员列表
router.get('/:groupId/members', checkAuth, async (req, res) => {
  try {
    const { groupId } = req.params;
    const members = await Group.getMembers(groupId);
    res.json({
      success: true,
      data: members
    });
  } catch (error) {
    console.error('获取群组成员列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取成员列表失败'
    });
  }
});

// 发送群消息
router.post('/:groupId/messages', checkAuth, async (req, res) => {
  try {
    const { groupId } = req.params;
    const { content, type } = req.body;
    const senderId = req.user.id;

    // 检查用户是否为群组成员
    const members = await Group.getMembers(groupId);
    const isMember = members.some(member => member.userId === senderId);

    if (!isMember) {
      return res.status(403).json({
        success: false,
        message: '您不是群组成员，无法发送消息'
      });
    }

    const message = await Group.sendMessage({
      groupId,
      senderId,
      content,
      type
    });

    res.json({
      success: true,
      data: message
    });
  } catch (error) {
    console.error('发送群消息失败:', error);
    res.status(500).json({
      success: false,
      message: '发送消息失败'
    });
  }
});

// 获取群消息历史
router.get('/:groupId/messages', checkAuth, async (req, res) => {
  try {
    const { groupId } = req.params;
    const { page = 1, pageSize = 20 } = req.query;
    const userId = req.user.id;

    // 检查用户是否为群组成员
    const members = await Group.getMembers(groupId);
    const isMember = members.some(member => member.userId === userId);

    if (!isMember) {
      return res.status(403).json({
        success: false,
        message: '您不是群组成员，无法查看消息'
      });
    }

    const messages = await Group.getMessages(groupId, parseInt(page), parseInt(pageSize));

    res.json({
      success: true,
      data: messages
    });
  } catch (error) {
    console.error('获取群消息历史失败:', error);
    res.status(500).json({
      success: false,
      message: '获取消息历史失败'
    });
  }
});

// 更新群组信息
router.put('/:groupId/info', checkAuth, async (req, res) => {
  try {
    const { groupId } = req.params;
    const updateData = req.body;
    const userId = req.user.id;

    // 检查用户是否为群主或管理员
    const members = await Group.getMembers(groupId);
    const currentUser = members.find(member => member.userId === userId);

    if (!currentUser || (currentUser.role !== 'owner' && currentUser.role !== 'admin')) {
      return res.status(403).json({
        success: false,
        message: '只有群主和管理员可以修改群组信息'
      });
    }

    // 验证数据
    if (updateData.name !== undefined) {
      if (!updateData.name.trim()) {
        return res.status(400).json({
          success: false,
          message: '群名称不能为空'
        });
      }
      if (updateData.name.length > 20) {
        return res.status(400).json({
          success: false,
          message: '群名称不能超过20个字符'
        });
      }
    }

    if (updateData.description !== undefined && updateData.description.length > 100) {
      return res.status(400).json({
        success: false,
        message: '群简介不能超过100个字符'
      });
    }

    // 更新群组信息
    await Group.updateGroupInfo(groupId, updateData);

    res.json({
      success: true,
      message: '群组信息更新成功'
    });
  } catch (error) {
    console.error('更新群组信息失败:', error);
    res.status(500).json({
      success: false,
      message: '更新群组信息失败'
    });
  }
});

// 更新群公告
router.put('/:groupId/announcement', checkAuth, async (req, res) => {
  try {
    const { groupId } = req.params;
    const { announcement } = req.body;
    const userId = req.user.id;

    // 检查用户是否为群主或管理员
    const members = await Group.getMembers(groupId);
    const currentUser = members.find(member => member.userId === userId);

    if (!currentUser || (currentUser.role !== 'owner' && currentUser.role !== 'admin')) {
      return res.status(403).json({
        success: false,
        message: '只有群主和管理员可以修改群公告'
      });
    }

    // 更新群公告
    await Group.updateAnnouncement(groupId, announcement);

    res.json({
      success: true,
      message: '群公告更新成功'
    });
  } catch (error) {
    console.error('更新群公告失败:', error);
    res.status(500).json({
      success: false,
      message: '更新群公告失败'
    });
  }
});

module.exports = router;