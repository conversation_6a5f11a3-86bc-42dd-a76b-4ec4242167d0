const express = require('express');
const router = express.Router();
const CustomerOrderController = require('../../controllers/customer/customerOrderController');

/**
 * 顾客端订单路由
 * 基础路径: /api/customer/orders
 */

// 创建购物车订单
router.post('/cart', CustomerOrderController.createCartOrder);

// 处理订单支付
router.post('/:orderId/pay', CustomerOrderController.processOrderPayment);

// 临时测试支付接口（无需认证）- 仅用于调试
router.post('/:orderId/test-pay', CustomerOrderController.processOrderPayment);

// 获取用户订单列表
router.get('/', CustomerOrderController.getUserOrders);

// 获取订单详情
router.get('/:orderId', CustomerOrderController.getOrderDetail);

// 申请退款
router.post('/:orderId/refund', CustomerOrderController.applyRefund);

// 查看退款进度
router.get('/:orderId/refund', CustomerOrderController.getRefundDetail);

// 取消订单
router.post('/:orderId/cancel', CustomerOrderController.cancelOrder);

// 确认收货
router.post('/:orderId/confirm', CustomerOrderController.confirmReceipt);

// 删除订单
router.delete('/:orderId', CustomerOrderController.deleteOrder);

module.exports = router;