const { userApi } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager');
Page({
  data: {
    searchValue: '',
    tabActive: 'all',
    userList: [],
    allSelected: false, // 全选状态
    selectedCount: 0, // 选中数量
    searchTimer: null, // 搜索防抖定时器
    isSearching: false, // 搜索状态
    // 新的排序筛选相关数据
    sortBy: '', // 当前排序字段：id、nickname、regDate
    sortOrder: 'asc', // 排序方向：asc-升序，desc-降序
    showFilter: false, // 是否显示筛选面板
    filterApplied: false, // 是否应用了筛选
    statusFilter: [], // 状态筛选：['normal', 'frozen']
    originalUserList: [], // 保存原始用户列表用于恢复默认排序
    // 用户调整弹窗相关
    showEditDrawer: false,
    currentUser: {},
    balanceReasons: ['后台充值', '损益', '补扣款', '其他'],
    selectedReasonIndex: -1,
    adjustAmount: ''
  },
  getRoleClass(roleType) {
    // 根据role_type返回对应的CSS类名
    if (!roleType) return 'customer';
    
    // 直接基于role_type判断
    if (roleType === 'admin') return 'admin';
    if (roleType === 'partner') return 'partner';
    if (roleType === 'customer') return 'customer';
    
    return 'customer'; // 默认返回顾客样式
  },
  
  // 将role_type转换为中文显示标签
  getRoleTypeLabel(roleType) {
    const roleLabels = {
      'customer': '顾客',
      'partner': '合伙人',
      'admin': '管理员'
    };
    return roleLabels[roleType] || roleType;
  },
  
  
  // 计算选中状态和数量
  calculateTotal() {
    const userList = this.data.userList;
    let selectedCount = 0;
    
    userList.forEach(user => {
      if (user.selected) {
        selectedCount++;
      }
    });
    
    // 检查是否全选
    const allSelected = userList.length > 0 && userList.every(user => user.selected);
    
    this.setData({
      selectedCount: selectedCount,
      allSelected: allSelected
    });
  },
  onLoad() {
    // 页面加载时恢复全局登录状态
    loginStateManager.restoreLoginStateToGlobal();
    // 清空搜索状态
    this.setData({
      searchValue: '',
      isSearching: false,
      searchTimer: null
    });
    this.fetchUserList();
  },

  // 加载用户列表（用于排序和筛选）
  loadUserList() {
    this.fetchUserList(this.data.searchValue);
  },
  fetchUserList(search = '') {
    // 如果是搜索请求，显示搜索状态
    if (search) {
      this.setData({ isSearching: true });
    }
    
    // 构建请求参数，包含搜索、标签筛选、排序和状态筛选
    const params = {
      search: search,
      tab: this.data.tabActive,
      sortBy: this.data.sortBy,
      sortOrder: this.data.sortOrder,
      statusFilter: this.data.statusFilter
    };
    
    userApi.getUserList(params).then(res => {
      console.log('获取用户列表响应:', res); // 调试信息
      if (res.success && Array.isArray(res.data)) {
        console.log('用户数据示例:', res.data[0]); // 查看第一个用户的数据结构
        console.log('第一个用户的highestRole字段:', res.data[0]?.highestRole); // 专门检查highestRole字段
        // 为每个用户添加选中状态
        const userList = res.data.map(user => {
          console.log('处理用户数据:', user); // 调试每个用户的数据
          console.log('用户', user.id, '的highestRole:', user.highestRole); // 调试每个用户的highestRole
          return {
            ...user,
            id: String(user.id), // 确保ID为字符串类型
            selected: false // 初始化为未选中
          };
        });
        this.setData({ 
          userList,
          originalUserList: [...userList], // 保存原始列表
          isSearching: false, // 搜索完成
          sortBy: '', // 重置排序状态
          sortOrder: 'asc'
        }, () => {
          // 用户列表更新后，计算选中状态
          this.calculateTotal();
        });
      } else {
        this.setData({ isSearching: false });
        wx.showToast({ title: '获取用户失败', icon: 'none' });
      }
    }).catch(() => {
      this.setData({ isSearching: false });
      wx.showToast({ title: '网络错误', icon: 'none' });
    });
  },
  onSearchInput(e) {
    const searchValue = e.detail.value;
    this.setData({ searchValue });
    
    // 清除之前的定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer);
    }
    
    // 设置防抖定时器，500ms后执行搜索
    const timer = setTimeout(() => {
      this.performSearch(searchValue);
    }, 500);
    
    this.setData({ searchTimer: timer });
  },
  
  // 执行搜索
  performSearch(searchValue) {
    // 清空选中状态
    this.setData({
      allSelected: false,
      selectedCount: 0
    });
    
    // 如果搜索值为空，则显示所有用户
    const trimmedValue = searchValue.trim();
    this.fetchUserList(trimmedValue);
  },
  
  onSearch() {
    // 立即执行搜索，不等待防抖
    this.performSearch(this.data.searchValue);
  },
  onTabChange(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({ tabActive: type });
    
    // 清空选中状态
    this.setData({
      allSelected: false,
      selectedCount: 0
    });
    
    // 重新获取用户列表，支持标签筛选
    this.fetchUserList(this.data.searchValue.trim());
    
    // 动态获取标签名称，避免硬编码
    const tabNames = {
      'all': '全部用户',
      'customer': '顾客',
      'vip': 'VIP会员',
      'partner': '合伙人'
    };
    wx.showToast({ title: '切换到' + (tabNames[type] || type), icon: 'none' });
  },
  // 选择/取消选择单个用户
  toggleSelectItem(e) {
    const index = e.currentTarget.dataset.index;
    const userList = this.data.userList;
    
    // 切换选中状态
    userList[index].selected = !userList[index].selected;
    
    this.setData({ userList });
    this.calculateTotal();
  },
  
  // 全选/取消全选功能
  onSelectAll() {
    const allSelected = !this.data.allSelected;
    const userList = this.data.userList;
    
    // 设置所有用户的选中状态
    userList.forEach(user => {
      user.selected = allSelected;
    });
    
    this.setData({ userList });
    this.calculateTotal();
    
    wx.showToast({ 
      title: allSelected ? '已选中所有用户' : '已取消选中所有用户', 
      icon: 'none' 
    });
  },
  
  // 批量冻结用户
  onBatchFreeze() {
    if (this.data.selectedCount === 0) {
      wx.showToast({ title: '请先选择要冻结的用户', icon: 'none' });
      return;
    }
    
    wx.showModal({
      title: '确认冻结',
      content: `确定要冻结选中的 ${this.data.selectedCount} 个用户吗？`,
      success: (res) => {
        if (res.confirm) {
          // 显示加载提示
          wx.showLoading({ title: '正在冻结用户...' });
          
          // 获取选中用户的ID列表
          const selectedIds = this.data.userList.filter(user => user.selected).map(user => user.id);
          
          // 调用后端API进行批量冻结操作
           userApi.batchFreezeUsers(selectedIds).then(response => {
             wx.hideLoading();
             if (response.success) {
               wx.showToast({ title: `已冻结 ${this.data.selectedCount} 个用户`, icon: 'success' });
               // 清空选中状态
               this.setData({
                 allSelected: false,
                 selectedCount: 0
               });
               // 重新获取用户列表
               this.fetchUserList();
             } else {
               wx.showToast({ title: response.message || '冻结失败', icon: 'none' });
             }
           }).catch(error => {
             wx.hideLoading();
             console.error('批量冻结用户失败:', error);
             wx.showToast({ title: '网络错误，冻结失败', icon: 'none' });
           });
        }
      }
    });
  },
  
  // 批量解冻用户
  onBatchUnfreeze() {
    if (this.data.selectedCount === 0) {
      wx.showToast({ title: '请先选择要解冻的用户', icon: 'none' });
      return;
    }
    
    wx.showModal({
      title: '确认解冻',
      content: `确定要解冻选中的 ${this.data.selectedCount} 个用户吗？`,
      success: (res) => {
        if (res.confirm) {
          // 显示加载提示
          wx.showLoading({ title: '正在解冻用户...' });
          
          // 获取选中用户的ID列表
          const selectedIds = this.data.userList.filter(user => user.selected).map(user => user.id);
          
          // 调用后端API进行批量解冻操作
           userApi.batchUnfreezeUsers(selectedIds).then(response => {
             wx.hideLoading();
             if (response.success) {
               wx.showToast({ title: `已解冻 ${this.data.selectedCount} 个用户`, icon: 'success' });
               // 清空选中状态
               this.setData({
                 allSelected: false,
                 selectedCount: 0
               });
               // 重新获取用户列表
               this.fetchUserList();
             } else {
               wx.showToast({ title: response.message || '解冻失败', icon: 'none' });
             }
           }).catch(error => {
             wx.hideLoading();
             console.error('批量解冻用户失败:', error);
             wx.showToast({ title: '网络错误，解冻失败', icon: 'none' });
           });
        }
      }
    });
  },
   
  // 加入合伙人
  onAddPartner() {
    wx.navigateTo({
      url: '/admin/partner-join/partner-join'
    });
  },
  onEditUser(e) {
    const id = e.currentTarget.dataset.id;
    // 获取用户详细信息
    this.getUserDetail(id);
  },

  // 获取用户详细信息
  getUserDetail(userId) {
    console.log('获取用户详情，用户ID:', userId);
    wx.showLoading({ title: '加载中...' });
    userApi.getUserInfo(userId).then(res => {
      console.log('获取用户详情响应:', res);
      wx.hideLoading();
      if (res.success && res.data) {
        this.setData({
          currentUser: res.data,
          showEditDrawer: true,
          selectedReasonIndex: -1,
          adjustAmount: ''
        });
      } else {
        wx.showToast({ title: '获取用户信息失败', icon: 'none' });
      }
    }).catch((error) => {
      console.error('获取用户详情失败:', error);
      wx.hideLoading();
      wx.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
    });
  },

  // 关闭弹窗
  onCloseEditDrawer() {
    this.setData({
      showEditDrawer: false,
      currentUser: {},
      selectedReasonIndex: -1,
      adjustAmount: ''
    });
  },

  // 点击遮罩层
  onDrawerMaskTap() {
    this.onCloseEditDrawer();
  },

  // 点击内容区域阻止冒泡
  onDrawerContentTap() {
    // 阻止冒泡，防止关闭弹窗
  },

  // 推荐人输入
  onReferrerInput(e) {
    const value = e.detail.value;
    console.log('推荐人输入:', value);
    this.setData({
      'currentUser.referrerId': value
    });
  },

  // 销售人输入
  onSalesmanInput(e) {
    const value = e.detail.value;
    console.log('销售人输入:', value);
    this.setData({
      'currentUser.salesmanId': value
    });
  },

  // 订阅门店输入
  onStoreInput(e) {
    const value = e.detail.value;
    console.log('订阅门店输入:', value);
    this.setData({
      'currentUser.subscribedStore': value
    });
  },

  // 事由选择
  onReasonChange(e) {
    this.setData({
      selectedReasonIndex: parseInt(e.detail.value)
    });
  },

  // 金额输入
  onAmountInput(e) {
    const value = e.detail.value;
    console.log('金额输入:', value);
    this.setData({
      adjustAmount: value
    });
  },

  // 保存用户信息
  onSaveUserInfo() {
    const { currentUser, selectedReasonIndex, adjustAmount, balanceReasons } = this.data;
    
    console.log('保存用户信息，当前数据:', { currentUser, selectedReasonIndex, adjustAmount });
    
    // 验证金额输入
    if (selectedReasonIndex >= 0 && (!adjustAmount || isNaN(parseFloat(adjustAmount)))) {
      wx.showToast({ title: '请输入有效的调整金额', icon: 'none' });
      return;
    }

    wx.showLoading({ title: '保存中...' });

    // 构建更新数据
    const updateData = {
      referrerId: currentUser.referrerId || null,
      salesmanId: currentUser.salesmanId || null,
      subscribedStore: currentUser.subscribedStore || null
    };

    // 如果有余额调整
    if (selectedReasonIndex >= 0 && adjustAmount) {
      updateData.balanceAdjustment = {
        amount: parseFloat(adjustAmount),
        reason: balanceReasons[selectedReasonIndex]
      };
    }

    console.log('构建的更新数据:', updateData);

    // 调用更新接口
    this.updateUserInfo(currentUser.id, updateData);
  },

  // 更新用户信息
  updateUserInfo(userId, updateData) {
    console.log('开始更新用户信息:', { userId, updateData });
    userApi.adminUpdateUser(userId, updateData).then(res => {
      console.log('更新用户信息响应:', res);
      wx.hideLoading();
      if (res.success) {
        wx.showToast({ title: '保存成功', icon: 'success' });
        this.onCloseEditDrawer();
        // 刷新用户列表
        this.fetchUserList(this.data.searchValue);
      } else {
        wx.showToast({ title: res.message || '保存失败', icon: 'none' });
      }
    }).catch((error) => {
      console.error('更新用户信息失败:', error);
      wx.hideLoading();
      wx.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
    });
  },

  // 排序切换事件
  onSortChange(e) {
    const sortBy = e.currentTarget.dataset.sort;
    const { sortBy: currentSort, sortOrder } = this.data;
    
    let newSortOrder = 'asc';
    if (currentSort === sortBy) {
      // 如果点击的是当前排序字段，则切换排序顺序
      newSortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    }
    
    this.setData({
      sortBy,
      sortOrder: newSortOrder,
    }, () => {
      this.loadUserList(); // 重新加载用户列表
    });
  },

  // 显示/隐藏筛选面板
  onToggleFilter() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 状态筛选选择
  onStatusSelect(e) {
    const status = e.currentTarget.dataset.status;
    let statusFilter = [...this.data.statusFilter];
    
    const index = statusFilter.indexOf(status);
    if (index > -1) {
      statusFilter.splice(index, 1); // 取消选择
    } else {
      statusFilter.push(status); // 添加选择
    }
    
    this.setData({ statusFilter });
  },

  // 清空筛选
  onClearFilter() {
    this.setData({
      statusFilter: [],
      filterApplied: false
    }, () => {
      this.loadUserList(); // 重新加载用户列表
    });
  },

  // 应用筛选
  onApplyFilter() {
    this.setData({
      showFilter: false,
      filterApplied: this.data.statusFilter.length > 0
    }, () => {
      this.loadUserList(); // 重新加载用户列表
    });
  }
});