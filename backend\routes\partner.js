/**
 * 合伙人相关路由
 */
const express = require('express');
const router = express.Router();
const partnerController = require('../controllers/partnerController');
const partnerStatsController = require('../controllers/partnerStatsController');
const partnerApplicationController = require('../controllers/partnerApplicationController');
const PartnerOrderController = require('../controllers/partner/partnerOrderController');
const storeController = require('../controllers/storeController');
const { checkAuth } = require('../middleware/auth');

// 原有合伙人功能
router.post('/join', partnerController.joinPartner);
router.get('/list', partnerController.getPartnersByStoreId);
router.get('/store-partners', checkAuth, partnerController.getStorePartners);

// 合伙人统计相关路由
router.get('/stats', checkAuth, partnerStatsController.getPartnerStats);
router.get('/fund-records', checkAuth, partnerStatsController.getFundRecords);
router.get('/order-stats', checkAuth, partnerStatsController.getOrderStats);

// 合伙人钱包相关路由
router.get('/wallet/stats', checkAuth, partnerStatsController.getPartnerWalletStats);
router.get('/wallet/records', checkAuth, partnerStatsController.getPartnerWalletRecords);

// 新的合伙人端专用门店接口
router.get('/stores', checkAuth, partnerStatsController.getPartnerStores);
router.get('/joined-stores', checkAuth, storeController.getPartnerJoinedStores);

// 合伙人申请相关路由
router.post('/apply', checkAuth, partnerApplicationController.applyPartner);
router.get('/applications/my', checkAuth, partnerApplicationController.getMyApplications);
router.get('/applications', checkAuth, partnerApplicationController.getAllApplications);
router.get('/applications/:id', checkAuth, partnerApplicationController.getApplicationDetail);
router.put('/applications/:id/review', checkAuth, partnerApplicationController.reviewApplication);



// 合伙人端门店订单相关路由
router.get('/store-orders', checkAuth, PartnerOrderController.getStoreOrders);
router.get('/store-orders/:id', checkAuth, PartnerOrderController.getStoreOrderDetail);

// 合伙人端顾客订单相关路由
router.get('/customer-orders', checkAuth, PartnerOrderController.getPartnerCustomerOrders);

// 我的推荐相关路由
router.get('/my-partner-referrals', checkAuth, partnerStatsController.getMyPartnerReferrals);
router.get('/my-customer-referrals', checkAuth, partnerStatsController.getMyCustomerReferrals);

module.exports = router;