/* 我的门店页面样式 */
.my-stores-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx; /* 为底部导航栏留出空间 */
}



/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-content {
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 500rpx;
}

.empty-content {
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: bold;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #999;
}

/* 门店列表 */
.store-list {
  padding: 30rpx;
  padding-top: 20rpx;
}

.store-item {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: flex-start;
  position: relative;
}

/* 门店形象照 */
.store-image-container {
  position: relative;
  margin-right: 25rpx;
  flex-shrink: 0;
}

.store-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 15rpx;
  background-color: #f0f0f0;
}

.store-level-badge {
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(45deg, #ff6b6b, #ffa500);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  white-space: nowrap;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

/* 门店信息 */
.store-info {
  flex: 1;
  min-width: 0;
}

.store-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.store-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.store-status {
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  color: white;
  margin-left: 15rpx;
}

.status-active {
  background-color: #4CAF50;
}

.status-inactive {
  background-color: #FF9800;
}

.status-closed {
  background-color: #F44336;
}

.status-pending {
  background-color: #2196F3;
}

/* 门店详情 */
.store-details {
  margin-bottom: 25rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  font-size: 26rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #666;
  margin-right: 10rpx;
  flex-shrink: 0;
}

.detail-value {
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.detail-value.highlight {
  color: #667eea;
  font-weight: bold;
}

/* 操作按钮区域 */
.store-actions {
  display: flex;
  justify-content: space-between;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
  margin-top: 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 10rpx;
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
  opacity: 0.7;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.action-text {
  font-size: 22rpx;
  color: #666;
}



/* 底部提示 */
.bottom-tip {
  text-align: center;
  padding: 30rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .store-item {
    padding: 25rpx;
  }
  
  .store-image {
    width: 100rpx;
    height: 100rpx;
  }
  
  .store-name {
    font-size: 30rpx;
  }
  
  .detail-item {
    font-size: 24rpx;
  }
}

/* 深色模式适配已移除 - 根据业务需求取消深色主题变换功能 */