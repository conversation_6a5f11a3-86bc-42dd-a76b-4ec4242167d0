<!-- admin/partner-applications/partner-applications.wxml -->
<view class="container">
  <!-- 状态筛选卡片标签 -->
  <view class="status-tabs">
    <view class="tab-item {{statusFilter === '' ? 'active' : ''}}" bindtap="onStatusTabChange" data-status="" data-text="全部">
      全部
    </view>
    <view class="tab-item {{statusFilter === 'pending' ? 'active' : ''}}" bindtap="onStatusTabChange" data-status="pending" data-text="待审核">
      待审核
    </view>
    <view class="tab-item {{statusFilter === 'approved' ? 'active' : ''}}" bindtap="onStatusTabChange" data-status="approved" data-text="已通过">
      已通过
    </view>
    <view class="tab-item {{statusFilter === 'rejected' ? 'active' : ''}}" bindtap="onStatusTabChange" data-status="rejected" data-text="已拒绝">
      已拒绝
    </view>
  </view>

  <view class="applications-list">
    <block wx:if="{{applications.length > 0}}">
      <view class="application-item" wx:for="{{applications}}" wx:key="id" bindtap="viewDetail" data-id="{{item.id}}">
        <view class="application-header">
          <view class="applicant-info">
            <image class="avatar" src="{{item.avatar || '../../images/profile.png'}}"></image>
            <view class="name-phone">
              <text class="name">{{item.nickname || item.name}}</text>
              <text class="phone">{{item.phone}}</text>
              <text class="user-id">ID: {{item.user_id}}</text>
            </view>
          </view>
          <view class="status {{item.status}}">
            {{item.status === 'pending' ? '待审核' : (item.status === 'approved' ? '已通过' : '已拒绝')}}
          </view>
        </view>
        <view class="application-body">
          <view class="region">
            <text class="label">意向区域：</text>
            <text class="value">{{item.province}} {{item.city}} {{item.district}}</text>
          </view>
          <view class="time">
            <text class="label">申请时间：</text>
            <text class="value">{{item.created_at ? wxs.formatDate(item.created_at) : '未知时间'}}</text>
          </view>
        </view>
      </view>
    </block>
    
    <view class="empty" wx:if="{{!loading && applications.length === 0}}">
      <text>暂无申请记录</text>
    </view>
    
    <view class="loading" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>
    
    <view class="no-more" wx:if="{{!loading && applications.length >= total && applications.length > 0}}">
      <text>没有更多数据了</text>
    </view>
  </view>
</view>

<wxs module="wxs">
  function formatDate(timestamp) {
    if (!timestamp) return '未知时间';
    var date = getDate(parseInt(timestamp));
    return date.getFullYear() + '-' + 
           padZero(date.getMonth() + 1) + '-' + 
           padZero(date.getDate()) + ' ' + 
           padZero(date.getHours()) + ':' + 
           padZero(date.getMinutes());
  }
  
  function padZero(num) {
    return num < 10 ? '0' + num : '' + num;
  }
  
  module.exports = {
    formatDate: formatDate
  };
</wxs>