# 控制器架构说明

## 订单控制器拆分

订单控制器已按业务端进行拆分，提高代码的可维护性和职责清晰度。

### 新的控制器结构

```
controllers/
├── customer/                 # 顾客端控制器
│   └── customerOrderController.js
├── partner/                  # 合伙人端控制器
│   └── partnerOrderController.js
└── admin/                    # 管理端控制器
    └── adminOrderController.js
```

### 服务层架构

```
services/order/
├── orderService.js           # 订单通用业务逻辑
├── paymentService.js         # 支付相关服务
├── inventoryService.js       # 库存管理服务
└── fundAllocationService.js  # 资金分配服务
```

### 路由配置

```
routes/
├── customer/
│   └── customerOrderRoutes.js
├── partner/
│   └── partnerOrderRoutes.js
├── admin/
│   └── adminOrderRoutes.js
└── index.js                  # 主路由文件（已更新）
```

## 各端职责划分

### 顾客端 (Customer)
- **路径**: `/api/customer/orders`
- **职责**: 处理顾客的订单相关操作
- **主要功能**:
  - 创建购物车订单
  - 处理订单支付
  - 查看订单列表和详情
  - 申请退款
  - 删除订单

### 合伙人端 (Partner)
- **路径**: `/api/partner/orders`
- **职责**: 处理门店合伙人的订单管理
- **主要功能**:
  - 创建采购订单
  - 创建移库订单
  - 管理门店订单
  - 更新订单状态
  - 查看门店库存

### 管理端 (Admin)
- **路径**: `/api/admin/orders`
- **职责**: 系统管理员的订单管理功能
- **主要功能**:
  - 查看所有订单
  - 处理退款申请
  - 订单统计分析
  - 系统级订单管理

## 公共服务层

### orderService.js
- 订单号生成
- 订单状态管理
- 用户权限验证
- 通用工具函数

### paymentService.js
- 余额支付处理
- 用户资金账户管理
- 资金变动记录

### inventoryService.js
- 库存检查和扣减
- 库存分配逻辑
- 库存回滚机制

### fundAllocationService.js
- 订单资金分配
- 佣金计算
- 利润分配
- 资金统计

## API路径规范

各端使用独立的API路径：

- `/api/customer/orders/*` - 顾客端订单API
- `/api/partner/orders/*` - 合伙人端订单API  
- `/api/admin/orders/*` - 管理端订单API

## 迁移优势

1. **职责清晰**: 每个控制器只处理对应端的业务逻辑
2. **代码维护**: 降低代码复杂度，提高可维护性
3. **权限控制**: 更精确的权限验证和数据访问控制
4. **团队协作**: 不同端的开发可以并行进行
5. **性能优化**: 减少不必要的数据查询和处理
6. **测试友好**: 更容易编写单元测试和集成测试

## 使用建议

1. **功能开发**: 根据业务端使用对应的控制器
2. **权限验证**: 根据用户角色调用对应端的API
3. **错误处理**: 统一的错误处理机制，确保用户体验
4. **代码组织**: 保持各端业务逻辑的独立性

---

*控制器架构已完成拆分，各端使用独立的API路径和业务逻辑。*