/**
 * 积分控制器
 */
const Points = require('../models/Points');

/**
 * 获取用户积分
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getUserPoints = async (req, res) => {
  try {
    const userId = req.userId || req.query.id;
    
    if (!userId) {
      return res.status(400).json({ success: false, message: '缺少用户ID' });
    }

    const pointsData = await Points.getUserPoints(userId);
    res.json({ success: true, data: pointsData });
  } catch (error) {
    console.error('获取用户积分失败:', error);
    res.status(500).json({ success: false, message: '获取用户积分失败' });
  }
};

/**
 * 更新用户积分
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updateUserPoints = async (req, res) => {
  try {
    const userId = req.userId;
    const { changeAmount, event } = req.body;
    
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }
    
    if (typeof changeAmount !== 'number') {
      return res.status(400).json({ success: false, message: '积分变动量必须是数字' });
    }
    
    if (!event) {
      return res.status(400).json({ success: false, message: '缺少积分变动事件描述' });
    }

    const result = await Points.updateUserPoints(userId, changeAmount, event);
    res.json({ success: true, data: result });
  } catch (error) {
    console.error('更新用户积分失败:', error);
    res.status(500).json({ success: false, message: '更新用户积分失败' });
  }
};

/**
 * 获取用户积分记录
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getUserPointsRecords = async (req, res) => {
  try {
    const userId = req.userId || req.query.id;
    const limit = parseInt(req.query.limit) || 10;
    const offset = parseInt(req.query.offset) || 0;
    
    if (!userId) {
      return res.status(400).json({ success: false, message: '缺少用户ID' });
    }

    const records = await Points.getUserPointsRecords(userId, limit, offset);
    res.json({ success: true, data: records });
  } catch (error) {
    console.error('获取用户积分记录失败:', error);
    res.status(500).json({ success: false, message: '获取用户积分记录失败' });
  }
};

/**
 * 获取积分配置
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getPointsConfig = async (req, res) => {
  try {
    const config = await Points.getPointsConfig();
    if (!config) {
      return res.status(404).json({ success: false, message: '积分配置不存在' });
    }
    res.json({ success: true, data: config });
  } catch (error) {
    console.error('获取积分配置失败:', error);
    res.status(500).json({ success: false, message: '获取积分配置失败' });
  }
};

/**
 * 更新积分配置（需要管理员权限）
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updatePointsConfig = async (req, res) => {
  try {
    // 这里应该添加一个管理员权限检查
    const { consumeRule, gainWays } = req.body;
    
    if (!consumeRule && !gainWays) {
      return res.status(400).json({ success: false, message: '缺少更新数据' });
    }

    const configData = { consumeRule, gainWays };
    const result = await Points.updatePointsConfig(configData);
    res.json({ success: true, data: result });
  } catch (error) {
    console.error('更新积分配置失败:', error);
    res.status(500).json({ success: false, message: '更新积分配置失败' });
  }
};
