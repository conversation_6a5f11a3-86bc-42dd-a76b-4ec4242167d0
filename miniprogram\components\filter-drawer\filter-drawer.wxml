<!--components/filter-drawer/filter-drawer.wxml-->
<!--商品筛选抽屉-->
<view class="filter-drawer {{show ? 'show' : ''}}" catchtouchmove="preventTouchMove">
  <!-- 遮罩层 -->
  <view class="filter-mask" bindtap="cancelFilter"></view>
  
  <!-- 筛选内容 -->
  <view class="filter-content" catchtouchmove="preventTouchMove">
    <!-- 头部 -->
    <view class="filter-header">
      <text class="filter-title">商品筛选</text>
      <view class="filter-close" bindtap="cancelFilter">
        <text class="close-icon">×</text>
      </view>
    </view>
    
    <!-- 筛选内容区域 -->
    <scroll-view class="filter-body" scroll-y="true">
      <!-- 价格范围 -->
      <view class="filter-section">
        <view class="section-title">价格范围</view>
        <view class="price-inputs">
          <view class="price-input">
            <text class="price-label">最低价</text>
            <input class="price-value" 
                   type="number" 
                   value="{{minPrice}}"
                   bindinput="onMinPriceInput"
                   placeholder="0" />
          </view>
          <view class="price-separator">-</view>
          <view class="price-input">
            <text class="price-label">最高价</text>
            <input class="price-value" 
                   type="number" 
                   value="{{maxPrice}}"
                   bindinput="onMaxPriceInput"
                   placeholder="10000" />
          </view>
        </view>
        <slider class="price-slider" 
                min="{{minPrice}}" 
                max="10000" 
                value="{{maxPrice}}"
                bindchange="onPriceRangeChange"
                activeColor="#5698c3"
                backgroundColor="#f0f0f0"
                block-size="20"
                block-color="#5698c3" />
      </view>
      
      <!-- 排序方式 -->
      <view class="filter-section">
        <view class="section-title">排序方式</view>
        <view class="sort-options">
          <view class="sort-option {{selectedSort === item.value ? 'active' : ''}}"
                wx:for="{{sortOptions}}"
                wx:key="value"
                bindtap="onSortChange"
                data-value="{{item.value}}">
            <text class="sort-label">{{item.label}}</text>
            <view class="sort-check" wx:if="{{selectedSort === item.value}}">
              <text class="check-icon">✓</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 库存状态 -->
      <view class="filter-section">
        <view class="section-title">库存状态</view>
        <view class="stock-option" bindtap="onStockToggle">
          <text class="stock-label">仅显示有库存商品</text>
          <view class="stock-switch {{onlyInStock ? 'active' : ''}}">
            <view class="switch-thumb"></view>
          </view>
        </view>
      </view>
      
      <!-- 商品分类 -->
      <view class="filter-section" wx:if="{{categories.length > 0}}">
        <view class="section-title">商品分类</view>
        <view class="category-options">
          <view class="category-option {{selectedCategories.includes(item.id) ? 'active' : ''}}"
                wx:for="{{categories}}"
                wx:key="id"
                bindtap="onCategoryToggle"
                data-id="{{item.id}}">
            <text class="category-label">{{item.name}}</text>
            <view class="category-check" wx:if="{{selectedCategories.includes(item.id)}}">
              <text class="check-icon">✓</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部操作按钮 -->
    <view class="filter-footer">
      <view class="reset-btn" bindtap="resetFilter">
        <text>重置</text>
      </view>
      <view class="apply-btn" bindtap="applyFilter">
        <text>应用筛选</text>
      </view>
    </view>
  </view>
</view>
