采购订单的业务逻辑

一、关于门店采购价
，，采购订单的结算是按门店采购价执行
，，每个级别的门店都对应了一个采购价字段（products表的purchase_price_l1~l5字段）
，，如果对应的采购价字段值为空，则取门店基准价（store_price字段）作为当前采购价
，，合伙人端各页面涉及商品采购价格显示、采购订单结算都使用此采购价（选择不同门店时，先判断门店级别是否变化，如果级别变化，则刷新的级别采购价；如果门店级别不变，则无需刷新采购价）


二、关于采购订单创建与状态影响
1、创建条件
》》点击“采购结算”要判断商品的平台总部库存是否足够（products表platform_stock字段）；
》》在【采购结算】页点击“提交订单”要判断门店的股本金是否足够支付订单金额（股本金不足时，如果公积金余额足够，可开启公积金支付，则可由公积金补充支付）。
》》说明：在“采购结算”前无需判断上述条件（包括加入采购车、调整数量等操作，均无需作库存和资金判断）。

2、订单状态与库存变化
》》采购订单被审核通过后，门店库存增加（采购订单上的商品）。
》》注意：采购订单完成，并不需要扣减平台总部的库存，门店库存实际上只相当于平台总部库存的部分镜像而存在，所以采购订单没有发货状态，不需要发货和收货。
》》

3、订单状态与资金变化
》》采购订单创建成功，则直接扣减门店的付款账户（即股本金账户，或者还包括公积金账户）。
》》采购订单被拒绝或者被取消，则门店的付款账户资金回滚。
