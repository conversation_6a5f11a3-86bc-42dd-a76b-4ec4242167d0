const { cartApi, userApi } = require('../../utils/api');

Page({
  data: {
    deliveryMethod: 'express', // 配送方式：express-快递，self-自提
    addressInfo: {}, // 收货地址信息
    storeInfo: {}, // 自提门店信息
    cartItems: [], // 购物车商品列表
    totalAmount: '0.00', // 总金额
    loading: true, // 加载状态
    cartItemIds: [], // 购物车商品ID列表
    wechatPaySelected: true, // 微信支付是否选中
    balancePayEnabled: false, // 余额支付是否启用
    showCityWarning: false // 是否显示跨城市提示条幅
  },

  onLoad: function (options) {
    console.log('确认订单页面加载成功');
    console.log('页面参数:', options);
    
    // 简化登录状态验证：只检查基本的token和用户信息存在性
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');

    console.log('订单页面登录状态检查:',
      '有token:', !!token,
      '有用户信息:', !!userInfo);

    if (!token || !userInfo) {
      console.log('订单页面检测到缺少token或用户信息');

      wx.showModal({
        title: '登录失效',
        content: '请先登录后再进行订单操作',
        showCancel: false,
        success: () => {
          wx.redirectTo({ url: '/pages/auth/auth' });
        }
      });
      return;
    }
    
    // 处理从商品详情页面直接购买的情况
    if (options.from === 'buy_now' && options.id) {
      this.handleBuyNow(options.id);
    } else {
      this.initPage();
    }
  },

  onShow: function () {
    console.log('确认订单页面显示');
    // 页面显示时检查是否需要更新城市提示
    this.checkCityWarning();
  },

  // 检查城市提示
  checkCityWarning: function() {
    if (this.data.deliveryMethod === 'self' && this.data.storeInfo.id) {
      const userInfo = wx.getStorageSync('userInfo') || {};
      const userCity = userInfo.city;
      const storeCity = this.data.storeInfo.city;
      
      const shouldShowWarning = userCity && storeCity && userCity !== storeCity;
      
      if (shouldShowWarning !== this.data.showCityWarning) {
        this.setData({
          showCityWarning: shouldShowWarning
        });
      }
    } else {
      // 非自提模式或无门店信息时隐藏提示
      if (this.data.showCityWarning) {
        this.setData({
          showCityWarning: false
        });
      }
    }
  },

  // 处理立即购买
  handleBuyNow: function(productId) {
    console.log('处理立即购买，商品ID:', productId);
    
    // 从购物车中获取对应的商品
    const cartItems = wx.getStorageSync('cartItems') || [];
    const targetItem = cartItems.find(item => item.productId === productId);
    
    if (targetItem) {
      // 将商品数据存储到本地存储，供页面使用
      wx.setStorageSync('selectedCartItems', [targetItem]);
      console.log('立即购买商品数据已存储:', targetItem);
    } else {
      console.log('未找到对应的购物车商品');
      wx.showToast({
        title: '商品数据获取失败',
        icon: 'none'
      });
    }
    
    this.initPage();
  },

  // 初始化页面
  initPage: function() {
    console.log('开始初始化确认订单页面');
    // 先获取商品数据，再获取地址信息
    this.getCartItems();
    // 延迟获取地址信息，避免同时请求
    setTimeout(() => {
      // 默认是快递方式，只获取默认地址
      this.getDefaultAddress();
      // 注意：不在初始化时获取门店信息，只有切换到自提时才获取
    }, 100);
  },

  // 获取购物车商品信息
  getCartItems: function() {
    console.log('开始获取购物车商品数据');
    
    this.setData({ loading: true });
    
    // 尝试多种方式获取数据
    let selectedItems = null;
    
    // 1. 尝试从本地存储获取
    try {
      selectedItems = wx.getStorageSync('selectedCartItems');
      console.log('从本地存储获取数据:', selectedItems);
    } catch (error) {
      console.error('读取本地存储失败:', error);
    }
    
    // 2. 如果本地存储没有，尝试从全局数据获取
    if (!selectedItems || selectedItems.length === 0) {
      try {
        const app = getApp();
        selectedItems = app.globalData.selectedCartItems;
        console.log('从全局数据获取数据:', selectedItems);
      } catch (error) {
        console.error('读取全局数据失败:', error);
      }
    }
    
    // 3. 验证数据完整性
    if (selectedItems && Array.isArray(selectedItems)) {
      // 检查每个商品是否有必要的字段
      selectedItems = selectedItems.filter(item => {
        if (!item.id || !item.productId || !item.price || !item.quantity) {
          console.warn('发现不完整的商品数据，已过滤:', item);
          return false;
        }
        // 验证数据类型和范围
        const price = parseFloat(item.price);
        const quantity = parseInt(item.quantity);
        if (isNaN(price) || price <= 0 || isNaN(quantity) || quantity <= 0) {
          console.warn('发现无效的价格或数量数据，已过滤:', item);
          return false;
        }
        return true;
      });
    }
    
    if (selectedItems && selectedItems.length > 0) {
      console.log('成功获取购物车数据:', selectedItems);
      
      // 处理商品数据
      const processedItems = selectedItems.map(item => {
        // 计算小计金额
        const price = parseFloat(item.price) || 0;
        const quantity = parseInt(item.quantity) || 1;
        const subtotal = (price * quantity).toFixed(2);
        
        return {
          ...item,
          subtotal: subtotal
        };
      });
      
      console.log('设置到页面的商品数据:', processedItems);
      
      this.setData({
        cartItems: processedItems,
        loading: false
      });
      
      console.log('页面数据设置完成，当前cartItems:', this.data.cartItems);
      
      this.calculateTotal();
      
      // 延迟清除购物车数据，确保数据已正确设置到页面
      setTimeout(() => {
        try {
          wx.removeStorageSync('selectedCartItems');
          console.log('购物车本地存储数据已清除');
        } catch (error) {
          console.error('清除购物车本地存储失败:', error);
        }

        try {
          const app = getApp();
          app.globalData.selectedCartItems = null;
          console.log('购物车全局数据已清除');
        } catch (error) {
          console.error('清除购物车全局数据失败:', error);
        }
      }, 500); // 增加延迟时间
      
      return;
    }
    
    // 如果没有全局数据，显示错误提示
    console.log('没有找到购物车数据');
    wx.showToast({
      title: '请从购物车页面选择商品进入',
      icon: 'none',
      duration: 2000
    });
    this.setData({
      cartItems: [],
      loading: false
    });
    this.calculateTotal();
  },

  // 获取默认收货地址
  getDefaultAddress: function() {
    console.log('开始获取默认收货地址');
    
    userApi.getDefaultAddress()
      .then(res => {
        console.log('默认地址API响应:', res);
        
        if (res && res.success) {
          if (res.data) {
            // 格式化地址信息
            const addressInfo = {
              id: res.data.id,
              name: res.data.name,
              phone: this.maskPhone(res.data.phone),
              address: `${res.data.province}${res.data.city}${res.data.district}${res.data.detail}`
            };
            
            console.log('格式化后的地址信息:', addressInfo);
            
            this.setData({
              addressInfo: addressInfo
            });
          } else {
            console.log('用户暂无默认地址');
            this.setData({
              addressInfo: {}
            });
          }
        } else {
          console.log('获取默认地址失败:', res.message);
          this.setData({
            addressInfo: {}
          });
        }
      })
      .catch(err => {
        console.error('获取默认地址失败:', err);
        this.setData({
          addressInfo: {}
        });
      });
  },

  // 手机号脱敏处理
  maskPhone: function(phone) {
    if (!phone) return '';
    if (phone.length < 7) return phone;
    return phone.substring(0, 3) + '****' + phone.substring(7);
  },

  // 获取默认自提门店
  getDefaultStore: function() {
    console.log('开始获取默认自提门店（订阅门店）');
    
    // 获取用户信息中的订阅门店编号
    const userInfo = wx.getStorageSync('userInfo');
    console.log('调试信息 - 完整userInfo:', userInfo);
    
    // 检查多种可能的订阅门店字段
    let subscribeStoreNo = null;
    if (userInfo) {
      subscribeStoreNo = userInfo.subscribe_store_no || userInfo.subscribedStore;
      console.log('检查订阅门店字段:', {
        subscribe_store_no: userInfo.subscribe_store_no,
        subscribedStore: userInfo.subscribedStore,
        最终使用: subscribeStoreNo
      });
    }
    
    if (!userInfo || !subscribeStoreNo) {
      console.log('❌ 用户信息中没有订阅门店编号');
      this.setData({
        storeInfo: {},
        showCityWarning: false
      });
      return;
    }
    
    console.log('用户订阅门店编号:', subscribeStoreNo);
    
    // 获取销售人门店列表，从中找到订阅门店
    userApi.getSalesmanStores()
      .then(res => {
        console.log('销售人门店API响应:', res);
        
        if (res && res.success && res.data && res.data.length > 0) {
          console.log('✅ 获取到销售人门店列表:', res.data);
          
          // 从门店列表中找到订阅门店（store_no等于用户的subscribe_store_no）
          const subscribedStore = res.data.find(store => store.store_no === subscribeStoreNo);
          
          if (subscribedStore) {
            console.log('✅ 找到订阅门店:', subscribedStore);
            console.log('门店ID:', subscribedStore.id);
            console.log('门店名称:', subscribedStore.name);
            console.log('门店编号:', subscribedStore.store_no);
            console.log('门店地址:', subscribedStore.address);
            
            // 设置订阅门店为默认自提门店（显示时不检查库存）
            this.setData({
              storeInfo: subscribedStore
            });
            
            console.log('✅ 门店信息已设置到页面数据:', this.data.storeInfo);
            console.log('✅ 当前配送方式:', this.data.deliveryMethod);
            console.log('✅ 页面应该显示门店信息');
            console.log('✅ 页面完整数据状态:', {
              deliveryMethod: this.data.deliveryMethod,
              storeInfo: this.data.storeInfo,
              hasStoreId: !!this.data.storeInfo.id,
              shouldShowStore: this.data.deliveryMethod === 'self' && !!this.data.storeInfo.id
            });
            
            // 检查用户与门店城市是否相同，显示城市提示
            this.checkCityWarning();
          } else {
            console.log('❌ 销售人门店列表中未找到订阅门店');
            this.setData({
              storeInfo: {},
              showCityWarning: false
            });
          }
        } else {
          console.log('❌ 获取销售人门店失败或无门店数据');
          this.setData({
            storeInfo: {},
            showCityWarning: false
          });
        }
      })
      .catch(err => {
        console.error('❌ 获取销售人门店异常:', err);
        this.setData({
          storeInfo: {},
          showCityWarning: false
        });
      });
  },

  // 切换配送方式
  switchDelivery: function(e) {
    const type = e.currentTarget.dataset.type;
    console.log('切换配送方式:', type);
    
    if (type === 'self') {
      // 切换到自提，先检查用户是否有销售人
      this.checkSalesmanForSelfPickup();
    } else {
      // 切换到快递，直接设置配送方式并获取默认地址
      this.setData({
        deliveryMethod: type
      });
      this.getDefaultAddress();
    }
  },

  // 检查用户是否有销售人（用于自提订单）
  checkSalesmanForSelfPickup: function() {
    console.log('检查用户销售人信息');
    
    // 调用API检查用户是否有销售人
    userApi.getSalesmanStores()
      .then(res => {
        console.log('销售人门店检查结果:', res);
        
        if (res && res.success) {
          if (res.data && res.data.length > 0) {
            // 用户有销售人，可以切换到自提页面
            console.log('✅ 用户有销售人，切换到自提页面');
            this.setData({
              deliveryMethod: 'self'
            });
            // 获取并显示订阅门店作为默认自提门店
            this.getDefaultStore();
          } else {
            // 用户没有销售人，显示提示弹窗，不切换到自提页面
            console.log('❌ 用户没有销售人，显示提示弹窗');
            this.showNoSalesmanDialog();
          }
        } else {
          // API调用失败，也显示提示弹窗
          console.log('❌ 获取销售人门店失败，显示提示弹窗');
          this.showNoSalesmanDialog();
        }
      })
      .catch(err => {
        console.error('检查销售人信息失败:', err);
        this.showNoSalesmanDialog();
      });
  },

  // 显示无销售人提示弹窗
  showNoSalesmanDialog: function() {
    wx.showModal({
      title: '提示',
      content: '暂无可供自提的门店，请选择快递方式，如有其他请求可联系客服处理',
      cancelText: '取消',
      confirmText: '联系客服',
      success: (res) => {
        if (res.confirm) {
          // 跳转到客服中心页面
          wx.navigateTo({
            url: '/pages/service/service'
          });
        }
        // 无论选择什么，都保持快递方式
        this.setData({
          deliveryMethod: 'express'
        });
      }
    });
  },

  // 切换微信支付选择状态
  toggleWechatPay: function() {
    this.setData({
      wechatPaySelected: !this.data.wechatPaySelected
    });
    console.log('微信支付选择状态:', this.data.wechatPaySelected);
  },

  // 切换余额支付开关状态
  toggleBalancePay: function(e) {
    const enabled = e.detail.value;
    this.setData({
      balancePayEnabled: enabled
    });
    console.log('余额支付开关状态:', enabled);
  },

  // 选择收货地址
  selectAddress: function() {
    wx.navigateTo({
      url: '/pages/address/list'
    });
  },

  // 选择自提门店
  selectStore: function() {
    wx.navigateTo({
      url: '/pages/store/select'
    });
  },

  // 计算总金额
  calculateTotal: function() {
    let total = 0;
    
    this.data.cartItems.forEach(item => {
      total += parseFloat(item.subtotal || 0);
    });
    
    this.setData({
      totalAmount: total.toFixed(2)
    });
    
    console.log('计算总金额:', this.data.totalAmount);
  },

  // 提交订单
  submitOrder: function() {
    console.log('点击提交订单按钮');
    
    // 显示加载提示
    wx.showLoading({
      title: '验证登录状态...',
      mask: true
    });
    
    // 增强登录状态验证，主动验证token有效性
    const loginStateManager = require('../../utils/login-state-manager');
    
    // 先检查本地token是否存在
    let token = wx.getStorageSync('token');
    let userInfo = wx.getStorageSync('userInfo');
    let tokenTime = wx.getStorageSync('tokenTimestamp');
    const now = Date.now();
    
    // 如果本地存储没有token，尝试从全局数据获取
    const app = getApp();
    if (!token && app && app.globalData && app.globalData.token) {
      token = app.globalData.token;
      tokenTime = app.globalData.tokenTimestamp || now;
      userInfo = app.globalData.userInfo;
      console.log('从全局数据恢复token成功');
      
      // 同步回本地存储
      wx.setStorageSync('token', token);
      wx.setStorageSync('tokenTimestamp', tokenTime);
      if (userInfo) {
        wx.setStorageSync('userInfo', userInfo);
      }
    }
    
    // 如果仍然没有token，尝试从登录状态管理器获取
    if (!token) {
      try {
        const loginState = loginStateManager.getLoginState();
        if (loginState && loginState.token) {
          token = loginState.token;
          tokenTime = loginState.timestamp || now;
          userInfo = loginState.userInfo;
          console.log('从登录状态管理器获取token成功');
          
          // 更新本地存储和全局数据
          wx.setStorageSync('token', token);
          wx.setStorageSync('tokenTimestamp', tokenTime);
          if (userInfo) {
            wx.setStorageSync('userInfo', userInfo);
          }
          if (app && app.globalData) {
            app.globalData.token = token;
            app.globalData.tokenTimestamp = tokenTime;
            if (userInfo) {
              app.globalData.userInfo = userInfo;
            }
          }
        }
      } catch (e) {
        console.error('尝试从登录状态管理器获取token失败:', e);
      }
    }
    
    console.log('提交订单前登录状态检查:',
      '有token:', !!token,
      '有用户信息:', !!userInfo,
      'token时间戳:', tokenTime,
      '当前时间:', now);

    // 简化验证逻辑：只检查基本的token和用户信息存在性
    // 让后端的认证中间件来处理token的有效性验证
    if (!token || !userInfo) {
      wx.hideLoading();
      console.log('提交订单时检测到缺少token或用户信息');

      wx.showModal({
        title: '登录已失效',
        content: '您的登录状态已失效，请重新登录后再提交订单',
        showCancel: true,
        cancelText: '返回购物车',
        confirmText: '去登录',
        success: (res) => {
          if (res.cancel) {
            // 返回购物车页面
            wx.navigateBack();
          } else {
            // 跳转到登录页面，并指定返回到购物车页面
            wx.redirectTo({ url: '/pages/auth/auth?redirect=/pages/cart/cart' });
          }
        }
      });
      return;
    }
    
    // 直接提交订单，让后端的认证中间件来验证token有效性
    console.log('基本验证通过，开始提交订单');
    wx.hideLoading();
    this.processOrderSubmission();
  },
  
  // 处理订单提交
  processOrderSubmission: function() {
    // 验证地址或门店
    if (this.data.deliveryMethod === 'express' && !this.data.addressInfo.id) {
      wx.showToast({
        title: '请先设置收货地址',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.deliveryMethod === 'self' && !this.data.storeInfo.id) {
      wx.showToast({
        title: '请先选择自提门店',
        icon: 'none'
      });
      return;
    }

    // 如果是自提且存在跨城市提示，需要用户再次确认
    if (this.data.deliveryMethod === 'self' && this.data.showCityWarning) {
      wx.showModal({
        title: '确认提示',
        content: '您与门店不在同一城市，请确认是否要在此门店自提',
        cancelText: '取消',
        confirmText: '确认',
        success: (res) => {
          if (res.confirm) {
            // 用户确认后继续提交订单
            this.continueOrderSubmission();
          }
        }
      });
      return;
    }

    // 继续正常的订单提交流程
    this.continueOrderSubmission();
  },
  
  // 继续订单提交流程
  continueOrderSubmission: function() {

    // 验证商品
    if (this.data.cartItems.length === 0) {
      wx.showToast({
        title: '购物车为空',
        icon: 'none'
      });
      return;
    }

    // 如果是自提模式，需要先检查门店库存
    if (this.data.deliveryMethod === 'self') {
      this.checkStoreInventoryBeforeSubmit();
      return;
    }

    // 快递模式直接继续提交
    this.finalOrderSubmission();
  },

  // 提交订单前检查门店库存
  checkStoreInventoryBeforeSubmit: function() {
    if (!this.data.storeInfo || !this.data.storeInfo.store_no) {
      wx.showToast({
        title: '请先选择自提门店',
        icon: 'none'
      });
      return;
    }

    // 显示检查库存的加载提示
    wx.showLoading({
      title: '检查库存中...',
      mask: true
    });

    // 构建库存检查数据
    const checkData = {
      store_no: this.data.storeInfo.store_no,
      products: this.data.cartItems.map(item => ({
        product_id: item.productId,
        quantity: item.quantity
      }))
    };

    console.log('提交订单前检查库存:', checkData);

    const { storeApi } = require('../../utils/api');
    storeApi.checkOfflineInventory(checkData)
      .then(res => {
        wx.hideLoading();
        console.log('提交订单前库存检查结果:', res);
        
        if (res && res.success) {
          if (res.data && res.data.sufficient) {
            // 库存充足，继续提交订单
            this.finalOrderSubmission();
          } else {
            // 库存不足，显示提示
            wx.showModal({
              title: '提示',
              content: '线下库存不足，无法自提，请选择快递方式',
              showCancel: false,
              confirmText: '确定',
              success: () => {
                // 切换到快递方式
                this.setData({
                  deliveryMethod: 'express'
                });
              }
            });
          }
        } else {
          // API调用失败
          wx.showToast({
            title: '库存检查失败，请重试',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('提交订单前检查库存失败:', err);
        wx.showToast({
          title: '库存检查失败，请重试',
          icon: 'none'
        });
      });
  },

  // 最终提交订单
  finalOrderSubmission: function() {

    // 验证购物车商品数据
    const validCartItems = this.data.cartItems.filter(item => {
      if (!item.id || !item.productId || !item.price || !item.quantity) {
        console.warn('过滤无效商品数据:', item);
        return false;
      }
      const price = parseFloat(item.price);
      const quantity = parseInt(item.quantity);
      if (isNaN(price) || price <= 0 || isNaN(quantity) || quantity <= 0) {
        console.warn('过滤价格或数量无效的商品:', item);
        return false;
      }
      return true;
    });
    
    const cartItemIds = validCartItems.map(item => item.id);
    if (cartItemIds.length === 0) {
      wx.showToast({
        title: '没有有效的商品数据',
        icon: 'none'
      });
      return;
    }
    
    // 重新计算总金额，确保准确性
    const recalculatedTotal = validCartItems.reduce((sum, item) => {
      return sum + (parseFloat(item.price) * parseInt(item.quantity));
    }, 0);
    
    if (recalculatedTotal <= 0) {
      wx.showToast({
        title: '订单金额计算错误',
        icon: 'none'
      });
      return;
    }
    
    console.log('验证通过的商品数量:', validCartItems.length, '总金额:', recalculatedTotal);

    // 验证支付方式
    if (!this.data.wechatPaySelected && !this.data.balancePayEnabled) {
      wx.showToast({
        title: '请选择支付方式',
        icon: 'none'
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '提交订单中...'
    });
    
    // 获取当前用户ID
    const app = getApp();
    const userInfo = (app.globalData && app.globalData.userInfo) || {};
    const userId = userInfo.id;
    
    if (!userId) {
      wx.hideLoading();
      wx.showToast({
        title: '用户信息异常，请重新登录',
        icon: 'none'
      });
      return;
    }
    
    // 构建购物车商品数据，转换为后端期望的格式
    const cart_items = validCartItems.map(item => ({
      product_id: item.productId,
      quantity: item.quantity
    }));
    
    // 构建订单数据，使用后端期望的参数格式
    const orderData = {
      user_id: userId, // 后端需要的用户ID
      cart_items: cart_items, // 后端期望的购物车商品格式
      delivery_method: this.data.deliveryMethod, // 使用下划线格式
      address_id: this.data.deliveryMethod === 'express' ? this.data.addressInfo.id : null, // 使用下划线格式
      store_no: this.data.deliveryMethod === 'self' ? this.data.storeInfo.store_no : null,
      payment_methods: this.getSelectedPaymentMethods(), // 使用下划线格式
      total_amount: recalculatedTotal // 使用下划线格式和重新计算的总金额
    };

    console.log('提交订单数据:', orderData);

    // 调用订单创建API
    const { orderApi } = require('../../utils/api');
    
    // 真实提交订单，不再使用模拟提交
    console.log('开始提交订单');
    orderApi.createCustomerOrder(orderData)
      .then(res => {
        wx.hideLoading();
        
        console.log('订单创建结果:', res);
        
        // 检查API返回是否成功
        if (res.success) {
          // 检查是否有订单数据
          if (res.data && res.data.orderId) {
            const orderId = res.data.orderId;
            const isPaid = res.data.paid;
            console.log('订单创建成功:', orderId, '是否已支付:', isPaid);

            if (isPaid) {
              // 订单已支付，显示成功信息并返回购物车
              wx.showToast({
                title: '订单提交并支付成功',
                icon: 'success'
              });

              setTimeout(() => {
                // 返回购物车页面
                wx.switchTab({
                  url: '/pages/cart/cart'
                });
              }, 1500);
            } else {
              // 订单未支付，跳转到支付页面
              wx.showToast({
                title: '订单提交成功',
                icon: 'success'
              });

              setTimeout(() => {
                wx.navigateTo({
                  url: `/pages/order/pay?id=${orderId}`
                });
              }, 1000);
            }
          } else {
            // API返回成功但数据异常
            console.error('订单创建API返回异常:', res);
            wx.showModal({
              title: '订单提交失败',
              content: res.message || '服务器返回数据异常，请稍后重试',
              showCancel: false
            });
          }
        } else {
          // API返回失败
          console.error('订单创建失败:', res);
          
          // 检查是否为库存不足错误，显示友好提示
          let errorMessage = res.message || '订单提交失败，请稍后重试';
          if (errorMessage.includes('库存不足') || errorMessage.includes('商品库存不足')) {
            errorMessage = '商品库存不足，请联系客服确认后再下单';
          }
          
          wx.showModal({
            title: '订单提交失败',
            content: errorMessage,
            showCancel: false
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('订单创建失败:', err);
        
        // 显示错误信息
        let errorMessage = '网络异常，请稍后重试';
        let isLoginExpired = false;
        
        // 处理401错误（登录失效）
        if (err.statusCode === 401 || err.code === 401 || (err.errMsg && err.errMsg.includes('401'))) {
          isLoginExpired = true;
          errorMessage = '登录已失效，请返回购物车重新结算';
          
          // 不清除登录状态，让用户返回购物车页面
          console.log('订单提交时遇到401错误，不清除登录状态');
        } else if (err.message) {
          // 检查是否为库存不足错误
        if (err.message.includes('库存不足') || err.message.includes('商品库存不足')) {
          errorMessage = '商品库存不足，请联系客服确认后再下单';
        } else {
            errorMessage = err.message;
          }
        }
        
        if (isLoginExpired) {
          // 登录失效时，提供更明确的选项
          wx.showModal({
            title: '登录已失效',
            content: '您的登录状态已失效，请返回购物车重新结算',
            showCancel: true,
            cancelText: '返回购物车',
            confirmText: '去登录',
            success: (res) => {
              if (res.cancel) {
                // 返回购物车页面
                wx.navigateBack();
              } else {
                // 跳转到登录页面，并指定返回到购物车页面
                wx.redirectTo({ url: '/pages/auth/auth?redirect=/pages/cart/cart' });
              }
            }
          });
        } else {
          // 其他错误
          wx.showModal({
            title: '订单提交失败',
            content: errorMessage,
            showCancel: false
          });
        }
      });
  },

  // 获取选中的支付方式
  getSelectedPaymentMethods: function() {
    const methods = [];
    
    if (this.data.wechatPaySelected) {
      methods.push('wechat');
    }
    
    if (this.data.balancePayEnabled) {
      methods.push('balance');
    }
    
    return methods;
  },
  
  // 图片加载失败处理
  onImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    const cartItems = this.data.cartItems;
    
    if (cartItems[index]) {
      // 直接使用默认图片
      cartItems[index].image = '/images/mo/mogoods.jpg';
      this.setData({
        cartItems: cartItems
      });
      console.log('订单商品图片加载失败，使用默认图片:', index);
    }
  }
});