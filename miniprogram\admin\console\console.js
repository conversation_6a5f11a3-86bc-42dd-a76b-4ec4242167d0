const loginStateManager = require('../../utils/login-state-manager');
const { dashboardApi } = require('../../utils/api');

Page({
  data: {
    userInfo: null,
    identityLabel: '',
    showRoleSwitch: false,
    adminLevel: '',
    // 新增统计数据
    storeCount: 0,
    partnerCount: 0,
    userCount: 0,
    totalSales: 0,
    // 徽标数据
    kuaidiPending: 0,
    zitiPending: 0,
    caigouPending: 0,
    yikuPending: 0,
    partnerApplicationPending: 0,
    userWithdrawPending: 0
  },
  onLoad() {
    console.log('=== 管理端控制中心页面 onLoad 开始 ===');
    
    // 检查登录状态和管理员权限
    if (!this.checkLoginAndAdminPermission()) {
      return; // 如果检查失败，直接返回，不继续加载页面
    }
    
    // 检查当前用户的身份信息
    this.debugUserRoles();
    
    // 热重载或页面加载时恢复全局登录状态
    loginStateManager.restoreLoginStateToGlobal();
    this.loadUserInfo();
    this.loadDashboardStats();
    console.log('=== 管理端控制中心页面 onLoad 结束 ===');
  },
  onShow() {
    console.log('管理端控制中心页面 onShow');
    
    // 检查是否从其他端切换过来
    const lastPage = wx.getStorageSync('lastPage');
    const isFromOtherSide = lastPage && (lastPage.includes('partner/') || (lastPage.includes('pages/') && !lastPage.includes('admin/')));
    console.log('检测到来源页面:', lastPage, '是否从其他端切换:', isFromOtherSide);
    
    // 页面显示时也恢复全局登录状态，防止切换tab后丢失
    loginStateManager.restoreLoginStateToGlobal();
    
    // 如果从其他端切换过来，重新调试用户角色信息
    if (isFromOtherSide) {
      console.log('从其他端切换过来，重新检查用户角色信息');
      this.debugUserRoles();
    }
    
    this.loadUserInfo();
    this.loadDashboardStats();
    this.loadDashboardBadges();
  },

  // 检查登录状态和管理员权限
  checkLoginAndAdminPermission() {
    console.log('=== 检查管理端登录状态和权限 ===');
    
    // 恢复登录状态到全局
    loginStateManager.restoreLoginStateToGlobal();
    
    const app = getApp();
    const globalData = app.globalData || {};
    
    // 检查是否已登录
    if (!globalData.isLogin || !globalData.userInfo) {
      console.warn('管理端：用户未登录，跳转到登录页面');
      wx.redirectTo({
        url: '/pages/auth/auth?returnTo=admin'
      });
      return false;
    }
    
    // 检查是否有管理员权限
    let hasAdminRole = false;
    
    // 优先检查全局角色列表
    if (globalData.userRoles && Array.isArray(globalData.userRoles)) {
      hasAdminRole = globalData.userRoles.some(role => role.role_type === 'admin' || role.role_type === 'super_admin');
      console.log('从全局角色列表检查管理员权限:', hasAdminRole);
    } else if (globalData.currentRole) {
      hasAdminRole = globalData.currentRole.role_type === 'admin' || globalData.currentRole.role_type === 'super_admin';
      console.log('从全局当前角色检查管理员权限:', hasAdminRole);
    } else if (globalData.userInfo.roles && Array.isArray(globalData.userInfo.roles)) {
      hasAdminRole = globalData.userInfo.roles.some(role => role.role_type === 'admin' || role.role_type === 'super_admin');
      console.log('从用户信息角色列表检查管理员权限:', hasAdminRole);
    } else if (globalData.userInfo.role_type) {
      hasAdminRole = globalData.userInfo.role_type === 'admin' || globalData.userInfo.role_type === 'super_admin';
      console.log('从用户信息角色类型检查管理员权限:', hasAdminRole);
    }
    
    if (!hasAdminRole) {
      console.warn('管理端：用户没有管理员权限，退回到顾客端');
      wx.showModal({
        title: '权限不足',
        content: '您没有访问管理端的权限，将返回到顾客端',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/home/<USER>'
          });
        }
      });
      return false;
    }
    
    console.log('管理端权限验证通过');
    return true;
  },

  loadUserInfo() {
    console.log('=== 开始加载用户信息 ===');
    const app = getApp();
    const globalData = app.globalData || {};
    let adminLevel = '';
    let identityLabel = '';
    let showRoleSwitch = false;
    
    console.log('全局数据状态:', {
      isLogin: globalData.isLogin,
      hasUserInfo: !!globalData.userInfo,
      hasUserRoles: !!globalData.userRoles,
      hasCurrentRole: !!globalData.currentRole
    });
    
    // 优先用全局数据
    if (globalData.isLogin && globalData.userInfo) {
      console.log('使用全局数据加载用户信息');
      
      // 处理多重身份 - 优先检查全局角色列表
      if (globalData.userRoles && Array.isArray(globalData.userRoles)) {
        console.log('从全局角色列表中查找管理员角色:', globalData.userRoles);
        const adminRole = globalData.userRoles.find(r => r.role_type === 'admin');
        if (adminRole && adminRole.role_name) {
          adminLevel = adminRole.role_name;
          identityLabel = adminRole.role_name;
          console.log('找到管理员角色:', adminRole);
        } else {
          console.warn('全局角色列表中未找到管理员角色或role_name为空');
          adminLevel = '';
          identityLabel = '';
        }
        showRoleSwitch = globalData.userRoles.length > 1;
      } else if (globalData.currentRole && globalData.currentRole.role_type === 'admin') {
        console.log('使用全局当前角色:', globalData.currentRole);
        adminLevel = globalData.currentRole.role_name || '';
        identityLabel = globalData.currentRole.role_name || '';
      } else if (globalData.userInfo.roles && Array.isArray(globalData.userInfo.roles)) {
        console.log('从用户信息中的角色列表查找管理员角色:', globalData.userInfo.roles);
        const adminRole = globalData.userInfo.roles.find(r => r.role_type === 'admin');
        if (adminRole && adminRole.role_name) {
          adminLevel = adminRole.role_name;
          identityLabel = adminRole.role_name;
          // 同步到全局数据
          globalData.userRoles = globalData.userInfo.roles;
          globalData.currentRole = adminRole;
          console.log('找到管理员角色并同步到全局:', adminRole);
        } else {
          adminLevel = '';
          identityLabel = '';
        }
        showRoleSwitch = globalData.userInfo.roles.length > 1;
      } else {
        console.log('使用用户信息中的默认角色名称');
        adminLevel = globalData.userInfo.role_name || '';
        identityLabel = globalData.userInfo.role_name || '';
      }
      
      console.log('设置用户信息:', {
        identityLabel,
        showRoleSwitch,
        adminLevel
      });
      
      this.setData({
        userInfo: globalData.userInfo,
        identityLabel,
        showRoleSwitch,
        adminLevel
      });
      return;
    }
    // 从本地存储获取
    console.log('全局数据不可用，尝试从本地存储获取');
    const loginState = loginStateManager.getLoginState();
    const userInfo = wx.getStorageSync('userInfo');
    
    console.log('本地存储状态:', {
      hasLoginState: !!loginState,
      isLogin: loginState?.isLogin,
      hasUserInfo: !!userInfo,
      userInfoRoles: userInfo?.roles?.length || 0
    });
    
    if (loginState && loginState.isLogin && userInfo) {
      console.log('使用本地存储数据加载用户信息');
      let adminLevel = '';
      let identityLabel = '';
      let showRoleSwitch = false;
      
      if (userInfo.roles && Array.isArray(userInfo.roles)) {
        console.log('从本地用户信息的角色列表查找管理员角色:', userInfo.roles);
        const adminRole = userInfo.roles.find(r => r.role_type === 'admin');
        if (adminRole && adminRole.role_name) {
          adminLevel = adminRole.role_name;
          identityLabel = adminRole.role_name;
          console.log('找到管理员角色:', adminRole);
          
          // 同步到全局数据
          if (globalData) {
            globalData.userRoles = userInfo.roles;
            globalData.currentRole = adminRole;
            globalData.userInfo = userInfo;
            globalData.isLogin = true;
            console.log('将本地数据同步到全局');
          }
        } else {
          console.warn('本地用户信息中未找到管理员角色或role_name为空');
          adminLevel = '';
          identityLabel = '';
        }
        showRoleSwitch = userInfo.roles.length > 1;
      } else {
        console.log('使用本地用户信息中的默认角色名称');
        adminLevel = userInfo.role_name || '';
        identityLabel = userInfo.role_name || '';
      }
      
      console.log('设置本地用户信息:', {
        identityLabel,
        showRoleSwitch,
        adminLevel
      });
      
      this.setData({
        userInfo,
        identityLabel,
        showRoleSwitch,
        adminLevel
      });
      return;
    }
    // 未登录
    this.setData({ userInfo: null, identityLabel: '', showRoleSwitch: false, adminLevel: '' });
  },

  // 调试用户角色信息
  debugUserRoles() {
    console.log('=== 调试用户角色信息 ===');
    const app = getApp();
    const globalData = app.globalData || {};
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    const loginState = loginStateManager.getLoginState();
    
    console.log('全局登录状态:', globalData.isLogin);
    console.log('全局用户信息:', globalData.userInfo);
    console.log('全局当前角色:', globalData.currentRole);
    console.log('全局用户角色列表:', globalData.userRoles);
    console.log('本地用户信息:', userInfo);
    console.log('本地token:', !!token);
    console.log('登录状态管理器状态:', loginState);
    
    // 主动调用getUserRoles API获取最新角色信息
    const { userApi } = require('../../utils/api');
    userApi.getUserRoles({ clientType: 'admin' })
      .then(res => {
        console.log('获取用户角色信息成功:', res);
        if (res.success && res.data && res.data.roles && res.data.roles.length > 0) {
          // 更新全局数据
          globalData.userRoles = res.data.roles;
          globalData.currentRole = res.data.currentRole;
          
          // 更新本地存储
          const updatedUserInfo = { ...userInfo, roles: res.data.roles };
          wx.setStorageSync('userInfo', updatedUserInfo);
          
          console.log('用户角色列表:', res.data.roles);
          const adminRole = res.data.roles.find(r => r.role_type === 'admin');
          console.log('管理员角色:', adminRole);
          
          if (!adminRole) {
            console.warn('⚠️ 用户没有管理员角色！');
            wx.showModal({
              title: '权限提示',
              content: '您当前没有管理员权限，请联系系统管理员开通管理员权限。',
              showCancel: false,
              complete: () => {
                wx.reLaunch({ url: '/pages/profile/profile' });
              }
            });
            return;
          }
          
          // 重新加载用户信息以更新界面
          this.loadUserInfo();
        } else {
          console.warn('⚠️ 获取角色信息失败或角色列表为空！');
        }
      })
      .catch(err => {
        console.error('获取用户角色信息失败:', err);
        // 如果API调用失败，回退到检查本地数据
        if (userInfo && userInfo.roles) {
          console.log('回退到本地角色数据:', userInfo.roles);
          const adminRole = userInfo.roles.find(r => r.role_type === 'admin');
          if (!adminRole) {
            console.warn('⚠️ 用户没有管理员角色！');
            wx.showModal({
              title: '权限提示',
              content: '您当前没有管理员权限，请联系系统管理员开通管理员权限。',
              showCancel: false,
              complete: () => {
                wx.reLaunch({ url: '/pages/profile/profile' });
              }
            });
          }
        }
      });
    
    console.log('=== 调试用户角色信息结束 ===');
    return true;
  },
  // 新增：加载统计数据
  loadDashboardStats() {
    dashboardApi.getDashboardSummary().then(res => {
      if (res.success && res.data) {
        this.setData({
          storeCount: res.data.storeCount || 0,
          partnerCount: res.data.partnerCount || 0,
          userCount: res.data.userCount || 0,
          totalSales: res.data.totalSales || 0
        });
      } else {
        wx.showToast({ title: '统计数据获取失败', icon: 'none' });
      }
    }).catch(() => {
      wx.showToast({ title: '统计数据获取失败', icon: 'none' });
    });
  },
  
  // 新增：加载徽标数据
  loadDashboardBadges() {
    dashboardApi.getDashboardBadges().then(res => {
      if (res.success && res.data) {
        this.setData({
          kuaidiPending: res.data.kuaidiPending || 0,
          zitiPending: res.data.zitiPending || 0,
          caigouPending: res.data.caigouPending || 0,
          yikuPending: res.data.yikuPending || 0,
          partnerApplicationPending: res.data.partnerApplicationPending || 0,
          userWithdrawPending: res.data.userWithdrawPending || 0
        });
      } else {
        console.log('徽标数据获取失败');
      }
    }).catch(() => {
      console.log('徽标数据获取失败');
    });
  },
  onGoSwitchLoginPage() {
    // 在跳转前确保获取用户的角色信息
    this.ensureUserRolesAndNavigate();
  },

  // 确保用户角色信息完整并跳转到切换登录页面
  ensureUserRolesAndNavigate() {
    const app = getApp();
    const { userApi } = require('../../utils/api');
    
    // 检查全局数据中是否已有完整的角色信息
    if (app.globalData.userRoles && app.globalData.userRoles.length > 0 && app.globalData.currentRole) {
      console.log('角色信息已存在，直接跳转');
      wx.navigateTo({ url: '/pages/switch-login/switch-login' });
      return;
    }
    
    // 显示加载提示
    wx.showLoading({ title: '获取身份信息...' });
    
    // 获取用户角色信息（指定客户端类型为管理端）
    userApi.getUserRoles({ clientType: 'admin' })
      .then(res => {
        console.log('获取用户角色信息成功:', res);
        // 修正：检查res.data.roles而不是res.data
        if (res.success && res.data && res.data.roles && res.data.roles.length > 0) {
          // 设置用户角色信息到全局数据
          app.globalData.userRoles = res.data.roles;
          
          // 设置当前角色，优先使用后端返回的currentRole
          if (res.data.currentRole) {
            app.globalData.currentRole = res.data.currentRole;
          } else {
            // 如果后端没有返回currentRole，则查找管理员角色
            const adminRole = res.data.roles.find(role => role.role_type === 'admin');
            if (adminRole) {
              app.globalData.currentRole = adminRole;
            } else {
              // 如果没有管理员角色，使用第一个角色
              app.globalData.currentRole = res.data.roles[0];
            }
          }
          
          console.log('已设置全局角色信息:', {
            userRoles: app.globalData.userRoles,
            currentRole: app.globalData.currentRole
          });
          
          wx.hideLoading();
          wx.navigateTo({ url: '/pages/switch-login/switch-login' });
        } else {
          console.error('角色数据格式错误:', res);
          wx.hideLoading();
          wx.showToast({ title: '获取身份信息失败', icon: 'none' });
        }
      })
      .catch(err => {
        console.error('获取用户角色信息失败:', err);
        wx.hideLoading();
        
        // 检查是否是需要重新登录的错误
        if (err.code === 'PREFERRED_ROLE_REQUIRED' || (err.message && err.message.includes('请退出重新登录'))) {
          wx.showModal({
            title: '提示',
            content: '请退出重新登录',
            showCancel: false,
            success: () => {
              // 清除登录状态
              const app = getApp();
              app.globalData.isLogin = false;
              app.globalData.userInfo = null;
              app.globalData.userRoles = [];
              app.globalData.currentRole = null;
              wx.removeStorageSync('userInfo');
              wx.removeStorageSync('token');
              
              // 跳转到登录页
              wx.reLaunch({ url: '/pages/login/login' });
            }
          });
          return;
        }
        
        wx.showToast({ title: '获取身份信息失败', icon: 'none' });
      });
  },

  goToProducts() {
    wx.redirectTo({ url: '/admin/products/products' });
  },
  goToUsers() {
    wx.redirectTo({ url: '/admin/users/users' });
  },
  // 订单管理模块跳转
  goToOrders(e) {
    const key = e.currentTarget.dataset.key;
    wx.navigateTo({ url: `/admin/orders/orders?tab=${key}` });
  },
  
  // 跳转到合伙人申请管理页面
  goToPartnerApplications() {
    wx.navigateTo({ url: '/admin/partner-applications/partner-applications' });
  },
  
  // 跳转到加入合伙人页面
  goToPartnerJoin() {
    wx.navigateTo({ url: '/admin/partner-join/partner-join' });
  },
  
  // 跳转到门店资金管理页面
  goToStoreFund() {
    wx.navigateTo({ url: '/admin/store/store-fund' });
  }
});