<view class="materials-admin-page">
  <!-- 顶部标签切换和搜索栏 -->
  <view class="tabs-container">
    <!-- 标签栏 -->
    <view class="tabs-row">
      <view class="tab-item {{activeTab === 'company' ? 'active' : ''}}" data-tab="company" bindtap="switchTab">
        公司简介
      </view>
      <view class="tab-item {{activeTab === 'banners' ? 'active' : ''}}" data-tab="banners" bindtap="switchTab">
        轮播图管理
      </view>
      <view class="tab-item {{activeTab === 'messages' ? 'active' : ''}}" data-tab="messages" bindtap="switchTab">
        消息管理
      </view>
      <view class="tab-item {{activeTab === 'faq' ? 'active' : ''}}" data-tab="faq" bindtap="switchTab">
        常见问题
      </view>
    </view>
    
    <!-- 消息搜索栏 - 只在消息管理标签时显示 -->
    <view wx:if="{{activeTab === 'messages'}}" class="search-row">
      <view class="user-mgr-search-bar">
        <view class="user-mgr-search-input-container">
          <input class="user-mgr-search-input" placeholder="搜索消息内容、发送者或类型" value="{{messageSearchKeyword}}" bindinput="onMessageSearchInput" confirm-type="search" bindconfirm="onMessageSearch" />
          <view class="user-mgr-search-btn" bindtap="onMessageSearch">
            <image src="/images/icons2/搜索.png"></image>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 公司简介管理 -->
  <view wx:if="{{activeTab === 'company'}}" class="company-section">
    <!-- 公司信息直接编辑 -->
    <view class="company-edit-card">
      <view class="card-content">
        <!-- 公司Logo上传 -->
        <view class="form-group">
          <text class="form-label">公司Logo</text>
          <view class="logo-edit-area">
            <view class="logo-upload-area" bindtap="changeCompanyLogo">
              <image class="company-logo-preview" src="{{companyLogoTemp || companyInfo.company_logo || '/images/icons2/公司.png'}}" mode="aspectFit"></image>
              <view class="logo-upload-hint">点击更换</view>
            </view>
          </view>
        </view>
        
        <!-- 公司名称 -->
        <view class="form-group">
          <text class="form-label">公司名称</text>
          <input class="form-input" placeholder="请输入公司名称" value="{{companyInfo.company_name}}" bindinput="onCompanyInputChange" data-field="company_name" />
        </view>
        
        <!-- 企业类型 -->
        <view class="form-group">
          <text class="form-label">企业类型</text>
          <input class="form-input" placeholder="请输入企业类型" value="{{companyInfo.company_type}}" bindinput="onCompanyInputChange" data-field="company_type" />
        </view>
        
        <!-- 公司简介 -->
        <view class="form-group">
          <text class="form-label">公司简介</text>
          <textarea class="form-textarea" placeholder="请输入公司简介内容..." value="{{companyInfo.company_description}}" bindinput="onCompanyInputChange" data-field="company_description" maxlength="500" auto-height></textarea>
        </view>
        
        <!-- 联系电话 -->
        <view class="form-group">
          <text class="form-label">联系电话</text>
          <input class="form-input" placeholder="请输入联系电话" value="{{companyInfo.company_phone}}" bindinput="onCompanyInputChange" data-field="company_phone" />
        </view>
        
        <!-- 公司地址 -->
        <view class="form-group">
          <text class="form-label">公司地址</text>
          <input class="form-input" placeholder="请输入公司地址" value="{{companyInfo.company_address}}" bindinput="onCompanyInputChange" data-field="company_address" />
        </view>
        
        <!-- 邮箱地址 -->
        <view class="form-group">
          <text class="form-label">邮箱地址</text>
          <input class="form-input" placeholder="请输入邮箱地址" value="{{companyInfo.company_email}}" bindinput="onCompanyInputChange" data-field="company_email" />
        </view>
      </view>
      
      <!-- 保存按钮区域 -->
      <view class="save-button-area">
        <button class="save-btn" bindtap="saveCompanyChanges">保存</button>
      </view>
    </view>
  </view>

  <!-- 轮播图管理 -->
  <view wx:if="{{activeTab === 'banners'}}" class="banners-section">
    <!-- 页面类型选择 -->
    <view class="page-type-section">
      <scroll-view class="page-type-tabs" scroll-x="true" show-scrollbar="false">
        <view class="page-type-list">
          <block wx:for="{{pageTypes}}" wx:key="value">
            <view class="page-type-tab {{selectedPageType === item.value ? 'active' : ''}}" 
                  data-type="{{item.value}}" bindtap="onPageTypeChange">
              {{item.label}}
            </view>
          </block>
        </view>
      </scroll-view>
      
      <!-- 操作按钮 -->
      <view class="actions-row">
        <button class="add-btn" bindtap="addBanner">
          添加轮播图
        </button>
      </view>
    </view>

    <!-- 轮播图内容区 -->
    <view class="banners-content">
      <view wx:if="{{banners.length === 0}}" class="empty-state">
        <image src="/images/icons2/空状态.png" class="empty-icon"></image>
        <text class="empty-text">暂无{{pageTypeLabels[selectedPageType] || selectedPageType}}轮播图</text>
        <text class="empty-hint">点击上方"添加轮播图"按钮创建新的轮播图</text>
      </view>
      
      <!-- 轮播图编辑卡片 -->
      <view wx:for="{{banners}}" wx:key="id" class="banner-edit-card">
        <view class="card-header">
          <view class="card-title">轮播图 {{index + 1}}</view>
        </view>
        
        <view class="card-content">
          <!-- 浮动操作按钮 -->
          <view class="floating-actions">
            <button class="floating-btn save" bindtap="saveBannerChanges" data-index="{{index}}" data-id="{{item.id}}">保存</button>
            <button class="floating-btn delete" bindtap="deleteBanner" data-index="{{index}}" data-id="{{item.id}}">删除</button>
          </view>
          
          <!-- 图片预览和上传 -->
          <view class="form-group">
            <text class="form-label">轮播图片</text>
            <view class="image-edit-area">
              <view class="image-upload-area" bindtap="changeBannerImage" data-index="{{index}}">
                <image class="banner-preview-img" 
                       src="{{bannerTempImage && editingBannerIndex === index ? bannerTempImage : (item.image_url || '/images/icons2/图片.png')}}" 
                       mode="aspectFill"
                       lazy-load="{{false}}"
                       show-menu-by-longpress="{{false}}"
                       binderror="onImageError"
                       bindload="onImageLoad"
                       data-index="{{index}}"></image>

                <view class="image-upload-hint">点击更换</view>
                <!-- 图片状态指示器 -->
                <view wx:if="{{item._loadFailed}}" class="image-status-indicator error">
                  <text>加载失败</text>
                </view>
                <view wx:elif="{{item._retryCount > 0}}" class="image-status-indicator retry">
                  <text>重试中({{item._retryCount}}/3)</text>
                </view>
                <view wx:elif="{{item.customImageMode}}" class="image-status-indicator success">
                  <text>已更新</text>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 标题编辑 -->
          <view class="form-group">
            <text class="form-label">轮播图标题</text>
            <input class="form-input" placeholder="请输入标题（可选）" value="{{item.title}}" bindinput="onBannerInputChange" data-index="{{index}}" data-field="title" />
          </view>
          
          <!-- 链接编辑 -->
          <view class="form-group">
            <text class="form-label">跳转链接</text>
            <input class="form-input" placeholder="请输入链接地址（可选）" value="{{item.link_url}}" bindinput="onBannerInputChange" data-index="{{index}}" data-field="link_url" />
          </view>
          
          <!-- 排序权重 -->
          <view class="form-group">
            <text class="form-label">排序权重</text>
            <input class="form-input" type="number" placeholder="数字越小越靠前" value="{{item.sort_order}}" bindinput="onBannerInputChange" data-index="{{index}}" data-field="sort_order" />
          </view>
          
          <!-- 开启状态 -->
          <view class="form-group switch-group">
            <text class="form-label">开启状态</text>
            <switch checked="{{item.is_active}}" bindchange="onBannerSwitchChange" data-index="{{index}}" data-field="is_active" />
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 消息管理 -->
  <view wx:if="{{activeTab === 'messages'}}" class="messages-section">
    <!-- 消息类型筛选和筛选按钮 -->
    <view class="message-filter-section">
      <view class="message-filter-list">
        <view class="message-filter-tab {{messageFilter === 'all' ? 'active' : ''}}" data-filter="all" bindtap="switchMessageFilter">
          全部消息
        </view>
        <view class="message-filter-tab {{messageFilter === 'private' ? 'active' : ''}}" data-filter="private" bindtap="switchMessageFilter">
          个人聊天
        </view>
        <view class="message-filter-tab {{messageFilter === 'group' ? 'active' : ''}}" data-filter="group" bindtap="switchMessageFilter">
          群聊消息
        </view>
      </view>
      <!-- 筛选按钮 -->
      <view class="filter-btn {{filterApplied ? 'active' : ''}}" bindtap="toggleMessageFilterPanel">
        <text>筛选</text>
        <text class="filter-icon">{{showMessageFilterPanel ? '▲' : '▼'}}</text>
      </view>
    </view>

    <!-- 消息列表 -->
    <scroll-view class="messages-content" 
                 refresher-enabled="true" 
                 refresher-triggered="{{messageRefreshing}}" 
                 bindrefresherrefresh="onMessageRefresh"
                 scroll-y="true">
      <!-- 消息列表 -->
    <view class="messages-content">
      <view wx:for="{{messages}}" wx:key="id" class="message-item-container">
        <movable-area class="movable-area">
          <!-- 背景操作按钮 -->
          <view class="swipe-actions">
            <!-- 移除重复的屏蔽按钮，只保留删除功能 -->
            <view class="action-item delete-action"
                  data-id="{{item.id}}"
                  bindtap="deleteMessage">
              <text class="action-text">删除</text>
            </view>
          </view>
          
          <!-- 可滑动的消息内容 -->
          <movable-view class="movable-view" direction="horizontal" x="{{item.moveX || 0}}"
                        bindchange="onMessageMove" data-id="{{item.id}}" data-index="{{index}}">
            <view class="message-item-enhanced">
              <!-- 消息头部信息 -->
              <view class="message-header">
                <view class="sender-info">
                  <image class="sender-avatar" src="{{item.senderAvatar || '/images/profile.png'}}" mode="aspectFill"></image>
                  <view class="sender-details">
                    <view class="sender-name">
                      <text>{{item.senderName || '未知发送者'}}</text>
                      <text class="receiver-info">发送给：{{item.receiverName || '未知接收者'}}</text>
                    </view>
                    <text class="message-time">{{item.formattedTime}}</text>
                  </view>
                </view>
                <view class="message-actions">
                  <view class="block-switch-container">
                    <switch class="block-switch" checked="{{item.isBlocked}}" bindchange="toggleBlockMessage" data-id="{{item.id}}" data-index="{{index}}" data-blocked="{{item.isBlocked}}" />
                    <text class="block-label">屏蔽</text>
                  </view>
                </view>
              </view>
              
              <!-- 消息内容 -->
              <view class="message-body">
                <!-- 文本消息 -->
              <view wx:if="{{item.message_type === 'text' || !item.message_type}}" class="text-content">
                <text class="message-content {{item.isBlocked ? 'blocked-content' : ''}} {{!item.expanded && item.content && item.content.length > 50 ? 'text-truncated' : ''}}">{{!item.expanded && item.content && item.content.length > 50 ? item.content.substring(0, 50) + '...' : (item.content || '[消息内容为空]')}}</text>
                <text wx:if="{{!item.expanded && item.content && item.content.length > 50}}" class="expand-text" bindtap="expandMessage" data-id="{{item.id}}">展开</text>
              </view>
                
                <!-- 图片消息 -->
                <view wx:elif="{{item.message_type === 'image'}}" class="image-content">
                  <image class="message-image" src="{{item.image_url}}" mode="aspectFill" bindtap="previewMessageImage" data-url="{{item.image_url}}"></image>
                  <text wx:if="{{item.content}}" class="image-caption">{{item.content}}</text>
                </view>
                
                <!-- 其他类型消息 -->
                <view wx:else class="other-content">
                  <text class="message-content {{item.isBlocked ? 'blocked-content' : ''}}">{{item.content || '[' + item.message_type + '消息]'}}</text>
                </view>
              </view>
            </view>
          </movable-view>
        </movable-area>
      </view>
      
      <!-- 空状态提示 -->
      <view wx:if="{{messages.length === 0}}" class="empty-state">
        <image src="/images/icons2/空状态.png" class="empty-icon"></image>
        <text class="empty-text">暂无消息数据</text>
        <text class="empty-hint">用户发送的消息将在这里显示</text>
      </view>
    </view>
    </scroll-view>
  </view>

  <!-- 常见问题管理 -->
  <view wx:if="{{activeTab === 'faq'}}" class="faq-section">
    <!-- 操作按钮 -->
    <view class="actions-row">
      <button class="add-btn" bindtap="addFaq">
        添加问题
      </button>
    </view>

    <!-- 常见问题列表 -->
    <view class="faq-content">
      <view wx:if="{{faqList.length === 0}}" class="empty-state">
        <image src="/images/icons2/空状态.png" class="empty-icon"></image>
        <text class="empty-text">暂无常见问题</text>
        <text class="empty-hint">点击上方"添加问题"按钮创建新的常见问题</text>
      </view>
      
      <!-- 常见问题编辑卡片 -->
      <view wx:for="{{faqList}}" wx:key="id" class="faq-edit-card">
        <view class="card-header">
          <view class="card-title">问题 {{index + 1}}</view>
        </view>
        
        <view class="card-content">
          <!-- 浮动操作按钮 -->
          <view class="floating-actions">
            <button class="floating-btn save" bindtap="saveFaqChanges" data-index="{{index}}" data-id="{{item.id}}">保存</button>
            <button class="floating-btn delete" bindtap="deleteFaq" data-index="{{index}}" data-id="{{item.id}}">删除</button>
          </view>
          
          <!-- 问题标题 -->
          <view class="form-group">
            <text class="form-label">问题标题</text>
            <input class="form-input" placeholder="请输入问题标题" value="{{item.question}}" bindinput="onFaqInputChange" data-index="{{index}}" data-field="question" />
          </view>
          
          <!-- 问题答案 -->
          <view class="form-group">
            <text class="form-label">问题答案</text>
            <textarea class="form-textarea" placeholder="请输入问题答案..." value="{{item.answer}}" bindinput="onFaqInputChange" data-index="{{index}}" data-field="answer" maxlength="1000" auto-height></textarea>
          </view>
          
          <!-- 排序权重 -->
          <view class="form-group">
            <text class="form-label">排序权重</text>
            <input class="form-input" type="number" placeholder="数字越小越靠前" value="{{item.sort_order}}" bindinput="onFaqInputChange" data-index="{{index}}" data-field="sort_order" />
          </view>
          
          <!-- 开启状态 -->
          <view class="form-group switch-group">
            <text class="form-label">开启状态</text>
            <switch checked="{{item.is_active}}" bindchange="onFaqSwitchChange" data-index="{{index}}" data-field="is_active" />
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 公司信息编辑弹窗 -->
  <view wx:if="{{showCompanyEditModal}}" class="modal-overlay" bindtap="closeCompanyEditModal">
    <view class="modal-content" catchtap="preventBubble">
      <view class="modal-header">
        <text class="modal-title">编辑公司信息</text>
        <image class="modal-close" src="/images/icons2/关闭.png" bindtap="closeCompanyEditModal"></image>
      </view>
      <scroll-view class="modal-body" scroll-y>
        <view class="form-group">
          <text class="form-label">公司名称 *</text>
          <input class="form-input" placeholder="请输入公司名称" value="{{editCompanyData.company_name}}" bindinput="onCompanyFieldInput" data-field="company_name" />
        </view>
        <view class="form-group">
          <text class="form-label">企业类型</text>
          <input class="form-input" placeholder="请输入企业类型" value="{{editCompanyData.company_type}}" bindinput="onCompanyFieldInput" data-field="company_type" />
        </view>
        <view class="form-group">
          <text class="form-label">公司简介</text>
          <textarea class="form-textarea" placeholder="请输入公司简介" value="{{editCompanyData.company_description}}" bindinput="onCompanyFieldInput" data-field="company_description"></textarea>
        </view>
        <view class="form-group">
          <text class="form-label">联系电话</text>
          <input class="form-input" placeholder="请输入联系电话" value="{{editCompanyData.company_phone}}" bindinput="onCompanyFieldInput" data-field="company_phone" />
        </view>
        <view class="form-group">
          <text class="form-label">公司地址</text>
          <input class="form-input" placeholder="请输入公司地址" value="{{editCompanyData.company_address}}" bindinput="onCompanyFieldInput" data-field="company_address" />
        </view>
        <view class="form-group">
          <text class="form-label">邮箱地址</text>
          <input class="form-input" placeholder="请输入邮箱地址" value="{{editCompanyData.company_email}}" bindinput="onCompanyFieldInput" data-field="company_email" />
        </view>
        <view class="form-group">
          <text class="form-label">官方网站</text>
          <input class="form-input" placeholder="请输入官方网站" value="{{editCompanyData.company_website}}" bindinput="onCompanyFieldInput" data-field="company_website" />
        </view>
        <view class="form-group">
          <text class="form-label">营业时间</text>
          <input class="form-input" placeholder="请输入营业时间" value="{{editCompanyData.business_hours}}" bindinput="onCompanyFieldInput" data-field="business_hours" />
        </view>
        <view class="form-group">
          <text class="form-label">联系人</text>
          <input class="form-input" placeholder="请输入联系人" value="{{editCompanyData.contact_person}}" bindinput="onCompanyFieldInput" data-field="contact_person" />
        </view>
      </scroll-view>
      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="closeCompanyEditModal">取消</button>
        <button class="modal-btn confirm" bindtap="saveCompanyInfo">保存</button>
      </view>
    </view>
  </view>

  <!-- 轮播图编辑弹窗 -->
  <view wx:if="{{showBannerEditModal}}" class="modal-overlay" bindtap="closeBannerEditModal">
    <view class="modal-content" catchtap="preventBubble">
      <view class="modal-header">
        <text class="modal-title">{{editBannerData.id ? '编辑轮播图' : '添加轮播图'}}</text>
        <image class="modal-close" src="/images/icons2/关闭.png" bindtap="closeBannerEditModal"></image>
      </view>
      <scroll-view class="modal-body" scroll-y catchtap="preventBubble">
        <view class="form-group" catchtap="preventBubble">
          <text class="form-label">轮播图标题</text>
          <input class="form-input" placeholder="请输入轮播图标题" value="{{editBannerData.title}}" bindinput="onBannerFieldInput" data-field="title" />
        </view>
        <view class="form-group" catchtap="preventBubble">
          <text class="form-label">页面类型 *</text>
          <!-- 页面类型输入模式切换 -->
          <view class="page-type-mode-switch">
            <view class="mode-tab {{pageTypeInputMode === 'select' ? 'active' : ''}}" catchtap="switchPageTypeMode" data-mode="select">
              选择已有
            </view>
            <view class="mode-tab {{pageTypeInputMode === 'input' ? 'active' : ''}}" catchtap="switchPageTypeMode" data-mode="input">
              手动输入
            </view>
          </view>
          
          <!-- 选择模式 -->
          <view wx:if="{{pageTypeInputMode === 'select'}}" class="page-type-select" catchtap="preventBubble">
            <picker mode="selector" range="{{pageTypes}}" range-key="label" value="{{editBannerPageTypeIndex}}" bindchange="onBannerPageTypeChange">
              <view class="form-picker">
                <text wx:if="{{editBannerPageTypeIndex >= 0}}">{{pageTypes[editBannerPageTypeIndex].label}}</text>
                <text wx:else style="color:#bbb">请选择页面类型</text>
              </view>
            </picker>
          </view>
          
          <!-- 输入模式 -->
          <view wx:if="{{pageTypeInputMode === 'input'}}" class="page-type-input" catchtap="preventBubble">
            <input class="form-input" placeholder="请输入新的页面类型（如：product_detail、news_list等）" value="{{customPageType}}" bindinput="onCustomPageTypeInput" />
            <view class="input-tip">提示：建议使用英文和下划线，如 product_detail</view>
          </view>
        </view>
        <view class="form-group" catchtap="preventBubble">
          <text class="form-label">轮播图片 *</text>
          <view class="image-upload">
            <view wx:if="{{editBannerData.image_url}}" class="uploaded-image">
              <image class="preview-img" src="{{editBannerData.image_url}}" mode="aspectFill"></image>
              <view class="image-actions">
                <button class="img-btn" bindtap="previewBannerImage">预览</button>
                <button class="img-btn" bindtap="uploadBannerImage">更换</button>
                <button class="img-btn delete" bindtap="deleteBannerImage">删除</button>
              </view>
            </view>
            <view wx:else class="upload-placeholder" bindtap="uploadBannerImage">
              <image src="/images/icons2/添加图片.png" class="upload-icon"></image>
              <text class="upload-text">点击上传轮播图</text>
            </view>
          </view>
        </view>
        <view class="form-group" catchtap="preventBubble">
          <text class="form-label">排序权重</text>
          <input class="form-input" type="number" placeholder="数字越小越靠前" value="{{editBannerData.sort_order}}" bindinput="onBannerFieldInput" data-field="sort_order" />
        </view>
        <view class="form-group" catchtap="preventBubble">
          <text class="form-label">链接地址</text>
          <input class="form-input" placeholder="点击轮播图时跳转的链接（可选）" value="{{editBannerData.link_url}}" bindinput="onBannerFieldInput" data-field="link_url" />
        </view>
        <view class="form-group switch-group" catchtap="preventBubble">
          <text class="form-label">是否启用</text>
          <switch checked="{{editBannerData.is_active}}" bindchange="onBannerActiveChange" />
        </view>
      </scroll-view>
      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="closeBannerEditModal">取消</button>
        <button class="modal-btn confirm" bindtap="saveBannerInfo">保存</button>
      </view>
    </view>
  </view>

  <!-- 常见问题编辑弹窗 -->
  <view wx:if="{{showFaqEditModal}}" class="modal-overlay" bindtap="closeFaqEditModal">
    <view class="modal-content" catchtap="preventBubble">
      <view class="modal-header">
        <text class="modal-title">{{editFaqData.id ? '编辑常见问题' : '添加常见问题'}}</text>
        <image class="modal-close" src="/images/icons2/关闭.png" bindtap="closeFaqEditModal"></image>
      </view>
      <scroll-view class="modal-body" scroll-y catchtap="preventBubble">
        <view class="form-group" catchtap="preventBubble">
          <text class="form-label">问题标题 *</text>
          <input class="form-input" placeholder="请输入问题标题" value="{{editFaqData.question}}" bindinput="onFaqFieldInput" data-field="question" />
        </view>
        <view class="form-group" catchtap="preventBubble">
          <text class="form-label">问题答案 *</text>
          <textarea class="form-textarea" placeholder="请输入问题答案..." value="{{editFaqData.answer}}" bindinput="onFaqFieldInput" data-field="answer" maxlength="1000" auto-height></textarea>
        </view>
        <view class="form-group" catchtap="preventBubble">
          <text class="form-label">排序权重</text>
          <input class="form-input" type="number" placeholder="数字越小越靠前" value="{{editFaqData.sort_order}}" bindinput="onFaqFieldInput" data-field="sort_order" />
        </view>
        <view class="form-group switch-group" catchtap="preventBubble">
          <text class="form-label">是否启用</text>
          <switch checked="{{editFaqData.is_active}}" bindchange="onFaqActiveChange" />
        </view>
      </scroll-view>
      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="closeFaqEditModal">取消</button>
        <button class="modal-btn confirm" bindtap="saveFaqInfo">保存</button>
      </view>
    </view>
  </view>

  <!-- 占位空间 -->
  <view class="safe-area"></view>

  <!-- 消息筛选抽屉面板 -->
  <view class="message-filter-drawer {{showMessageFilterPanel ? 'show' : ''}}">
    <!-- 遮罩层 -->
    <view class="drawer-mask" bindtap="toggleMessageFilterPanel"></view>
    <!-- 抽屉内容 -->
    <view class="drawer-content">
      <view class="drawer-header">
        <text class="drawer-title">消息筛选</text>
        <button class="drawer-close" bindtap="toggleMessageFilterPanel">
          <image src="/images/icons2/关闭.png" class="close-icon"></image>
        </button>
      </view>
      
      <scroll-view class="drawer-body" scroll-y="true">
        <!-- 时间筛选 -->
        <view class="filter-section">
          <view class="filter-title">时间范围</view>
          <view class="time-filter-options">
            <view class="time-option {{tempTimeFilter === 'today' ? 'active' : ''}}" data-filter="today" bindtap="selectTimeFilter">今天</view>
            <view class="time-option {{tempTimeFilter === 'week' ? 'active' : ''}}" data-filter="week" bindtap="selectTimeFilter">本周</view>
            <view class="time-option {{tempTimeFilter === 'month' ? 'active' : ''}}" data-filter="month" bindtap="selectTimeFilter">本月</view>
            <view class="time-option {{tempTimeFilter === 'all' ? 'active' : ''}}" data-filter="all" bindtap="selectTimeFilter">全部</view>
          </view>
        </view>
        
        <!-- 状态筛选 -->
        <view class="filter-section">
          <view class="filter-title">消息状态</view>
          <view class="status-filter-options">
            <view class="status-option {{tempStatusFilter === 'all' ? 'active' : ''}}" data-filter="all" bindtap="selectStatusFilter">全部</view>
            <view class="status-option {{tempStatusFilter === 'normal' ? 'active' : ''}}" data-filter="normal" bindtap="selectStatusFilter">正常</view>
            <view class="status-option {{tempStatusFilter === 'blocked' ? 'active' : ''}}" data-filter="blocked" bindtap="selectStatusFilter">已屏蔽</view>
          </view>
        </view>
      </scroll-view>
      
      <!-- 操作按钮 -->
      <view class="drawer-footer">
        <button class="filter-clear-btn" bindtap="clearMessageFilter">清空</button>
        <button class="filter-apply-btn" bindtap="applyMessageFilter">应用</button>
      </view>
    </view>
  </view>

  <admin-tabbar current="materials" />
</view>