<view class="orders-container">
  <!-- 一级分类卡片 -->
  <view class="first-level-tabs">
    <block wx:for="{{firstLevelTabs}}" wx:key="key">
      <view class="first-tab {{currentFirstTab === item.key ? 'active' : ''}}" data-key="{{item.key}}" bindtap="onFirstTabChange">
        {{item.label}}
      </view>
    </block>
  </view>

  <!-- 二级分类卡片 -->
  <view class="second-level-tabs">
    <block wx:for="{{secondLevelTabsMap[currentFirstTab]}}" wx:key="key">
      <view class="second-tab {{currentSecondTab === item.key ? 'active' : ''}}" data-key="{{item.key}}" bindtap="onSecondTabChange">
        {{item.label}}
      </view>
    </block>
  </view>

  <!-- 订单列表区域 -->
  <view class="order-list-container">
    <!-- 加载状态 -->
    <view wx:if="{{loading && orderList.length === 0}}" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 订单列表 -->
    <view wx:elif="{{orderList.length > 0}}" class="order-list">
      <!-- 订单卡片整体可点击进入详情页 -->
      <view wx:for="{{orderList}}" wx:key="id" class="order-item" data-id="{{item.id}}" bindtap="onViewOrderDetail">
        <view class="order-header">
          <view class="order-info">
            <text class="order-no">订单号：{{item.order_no}}</text>
            <text class="order-time">{{item.formatted_time}}</text>
          </view>
          <view class="order-status status-{{item.status}}">{{item.status_text || item.status}}</view>
        </view>
        
        <!-- 顾客信息（仅顾客订单显示） -->
        <view wx:if="{{item.order_type === 'customer'}}" class="customer-info">
          <image class="customer-avatar" src="{{item.user_avatar || '/images/icons2/男头像.png'}}" mode="aspectFill"></image>
          <view class="customer-details">
            <text class="customer-nickname">{{item.user_nickname && item.user_nickname !== '??' ? item.user_nickname : ''}}</text>
            <text class="customer-phone" wx:if="{{item.user_phone}}">{{item.user_phone}}</text>
            <text class="customer-id">ID: {{item.user_id}}</text>
          </view>
        </view>
        
        <!-- 门店信息（仅顾客订单显示） -->
        <view wx:if="{{item.order_type === 'customer'}}" class="store-info">
          <text class="store-label">归属门店:</text>
          <text class="store-name">{{item.store_name || '未知门店'}}</text>
          <text class="store-no">({{item.store_no}})</text>
        </view>
        
        <!-- 销售人信息（仅顾客订单显示） -->
        <view wx:if="{{item.order_type === 'customer'}}" class="salesman-info">
          <text class="salesman-label">销售人:</text>
          <text class="salesman-name">{{item.salesman_nickname && item.salesman_nickname !== '??' ? item.salesman_nickname : '无'}}</text>
          <text class="salesman-phone" wx:if="{{item.salesman_phone}}">（{{item.salesman_phone}}）</text>
        </view>
        
        <!-- 用户信息（仅门店采购订单显示） -->
        <view wx:if="{{item.order_type === 'purchase'}}" class="customer-info">
          <image class="customer-avatar" src="{{item.user_avatar || '/images/icons2/男头像.png'}}" mode="aspectFill"></image>
          <view class="customer-details">
            <text class="customer-nickname">{{item.user_nickname && item.user_nickname !== '??' ? item.user_nickname : '用户'}}</text>
            <text class="customer-phone" wx:if="{{item.user_phone}}">{{item.user_phone}}</text>
            <text class="customer-id">ID: {{item.user_id}}</text>
          </view>
        </view>
        
        <!-- 门店信息（仅门店采购订单显示） -->
        <view wx:if="{{item.order_type === 'purchase'}}" class="store-info">
          <text class="store-label">采购门店:</text>
          <text class="store-name">{{item.store_name || '未知门店'}}</text>
          <text class="store-no">({{item.store_no}})</text>
        </view>
        
        <!-- 订单金额 -->
        <view class="order-amount">
          <text class="amount-label">订单总额:</text>
          <text class="amount-value">￥{{item.total_amount}}</text>
        </view>
        
        <!-- 门店采购订单商品信息 -->
        <view wx:if="{{item.order_type === 'purchase'}}" class="purchase-order-items">
          <view class="product-item">
            <image class="product-image" src="{{item.product_image}}" mode="aspectFill"></image>
            <view class="product-info">
              <text class="product-name">{{item.product_name}}</text>
              <text class="product-price">采购价: ￥{{item.purchase_price || item.price}}</text>
            </view>
            <view class="product-right">
              <text class="product-quantity">x{{item.quantity}}</text>
              <text class="product-subtotal">￥{{item.total_amount || item.subtotal}}</text>
            </view>
          </view>
        </view>
        
        <!-- 顾客订单商品信息 -->
        <view wx:if="{{item.items && item.items.length > 0}}" class="customer-order-items">
          <view class="items-header">
            <text class="items-title">商品清单</text>
            <text class="items-count">共{{item.items.length}}件商品</text>
          </view>
          <view wx:for="{{item.items}}" wx:for-item="product" wx:key="id" class="product-item">
            <image class="product-image" src="{{product.product_image || product.image}}" mode="aspectFill"></image>
            <view class="product-info">
              <text class="product-name">{{product.product_name || product.name}}</text>
              <text class="product-price">单价: ￥{{product.price}}</text>
            </view>
            <view class="product-right">
              <text class="product-quantity">x{{product.quantity}}</text>
              <text class="product-subtotal">￥{{product.subtotal}}</text>
            </view>
          </view>
        </view>
        
        <!-- 订单操作按钮区域，阻止事件冒泡 -->
        <view class="order-actions" catchtap="stopPropagation">
          <!-- 顾客订单操作按钮 -->
          <button wx:if="{{item.order_type === 'customer' && item.status === '待付款'}}" class="action-btn remind" data-id="{{item.id}}" bindtap="onRemindPayment">提醒顾客付款</button>
        <button wx:if="{{item.order_type === 'customer' && item.status === '待发货'}}" class="action-btn primary" data-id="{{item.id}}" bindtap="onShipOrder">发货</button>
        <button wx:if="{{item.order_type === 'customer' && item.status === '已发货'}}" class="action-btn secondary" data-id="{{item.id}}" bindtap="onConfirmReceipt">确认收货</button>
          
          <!-- 门店采购订单操作按钮 -->
          <button wx:if="{{item.order_type === 'purchase' && item.status === '待审核'}}" class="action-btn approve" data-id="{{item.id}}" bindtap="onApproveOrder">审核</button>
        <button wx:if="{{item.order_type === 'purchase' && item.status === '待审核'}}" class="action-btn reject" data-id="{{item.id}}" bindtap="onRejectOrder">拒绝</button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty-container">
      <text class="empty-text">暂无{{currentFirstTabLabel}}{{currentSecondTabLabel}}订单</text>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{loading && orderList.length > 0}}" class="load-more">
      <text class="load-more-text">加载中...</text>
    </view>
    
    <!-- 没有更多数据 -->
    <view wx:elif="{{!hasMore && orderList.length > 0}}" class="no-more">
      <text class="no-more-text">没有更多数据了</text>
    </view>
  </view>
</view>

<!-- 发货信息输入弹窗 -->
<view class="modal-overlay" wx:if="{{showShipModal}}" catchtap="onCancelShip">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">发货信息</text>
      <text class="modal-close" bindtap="onCancelShip">×</text>
    </view>
    
    <view class="modal-body">
      <view class="form-item">
        <text class="form-label">物流单号 *</text>
        <input 
          class="form-input" 
          placeholder="请输入物流单号" 
          value="{{shipForm.tracking_number}}"
          data-field="tracking_number"
          bindinput="onShipFormInput"
        />
      </view>
      
      <view class="form-item">
        <text class="form-label">快递公司 *</text>
        <input 
          class="form-input" 
          placeholder="请输入快递公司名称" 
          value="{{shipForm.shipping_company}}"
          data-field="shipping_company"
          bindinput="onShipFormInput"
        />
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="onCancelShip">取消</button>
      <button class="modal-btn confirm" bindtap="onConfirmShip">确认发货</button>
    </view>
  </view>
</view>