Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    }
  },
  data: {
    sku: '',
    name: '',
    image: '',
    imageTemp: '',
    customImageMode: false, // 标记是否使用自定义图片
    spec: '',
    platform_price: '',
    store_price: '',
    retail_price: ''
  },
  observers: {
    // 监听visible属性变化，重置表单
    'visible': function(visible) {
      if (visible) {
        this.resetForm();
        // 当组件变为可见时，检查是否有待处理的裁剪图片
        setTimeout(() => {
          this.checkForCroppedImage();
        }, 100); // 延迟执行，确保页面数据已更新
      }
    }
  },
  lifetimes: {
    attached() {
      // 组件实例被放入页面节点树后执行
    },
    detached() {
      // 组件实例被从页面节点树移除后执行
    }
  },
  pageLifetimes: {
    // 组件所在页面的生命周期函数
    show() {
      console.log('商品创建抽屉组件页面显示事件触发');
      // 只有在组件可见时才检查裁剪图片
      if (this.data.visible) {
        setTimeout(() => {
          this.checkForCroppedImage();
        }, 50); // 短暂延迟确保数据同步
      }
    }
  },
  
  methods: {
    // 检查是否有待处理的裁剪图片
    checkForCroppedImage() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      
      console.log('创建商品组件检查裁剪图片，当前页面数据:', {
        croppedImage: currentPage.data.croppedImage,
        imageType: currentPage.data.imageType,
        visible: this.data.visible
      });
      
      // 只处理标记为创建商品的裁剪图片
      if (currentPage.data.croppedImage && currentPage.data.imageType === 'product-create') {
        console.log('创建商品组件发现待处理的裁剪图片:', currentPage.data.croppedImage);
        
        // 设置临时图片显示
        this.setData({ 
          imageTemp: currentPage.data.croppedImage,
          customImageMode: true
        }, () => {
          console.log('创建商品裁剪图片设置完成:', {
            imageTemp: this.data.imageTemp,
            customImageMode: this.data.customImageMode
          });
        });
        
        // 清除页面数据
        currentPage.setData({
          croppedImage: null,
          imageType: null,
          customImageMode: false
        });
      }
    },
    
    // 添加图片加载事件
    onImageLoad: function(e) {
      console.log('商品图片加载成功:', e.detail);
    },
    onImageError: function(e) {
      console.warn('商品图片加载失败:', e.detail);
    },
    preventTouchMove() { return false; },
    catchTouchMove() {},
    // 重置表单数据
    resetForm() {
      this.setData({
        sku: '',
        name: '',
        image: '',
        imageTemp: '',
        customImageMode: false,
        spec: '',
        platform_price: '',
        store_price: '',
        retail_price: ''
      });
    },
    
    // 阻止触摸穿透
    preventTouchMove() {
      return false;
    },
    
    // 捕获触摸事件
    catchTouchMove() {
      return true;
    },
    
    // SKU输入
    onSkuInput(e) {
      this.setData({ sku: e.detail.value });
    },
    
    // 品名输入
    onNameInput(e) {
      this.setData({ name: e.detail.value });
    },
    
    // 规格输入
    onSpecInput(e) {
      this.setData({ spec: e.detail.value });
    },
    
    // 平台成本价输入
    onPlatformPriceInput(e) {
      this.setData({ platform_price: e.detail.value });
    },
    
    // 门店基准价输入
    onStorePriceInput(e) {
      this.setData({ store_price: e.detail.value });
    },
    
    // 零售基准价输入
    onRetailPriceInput(e) {
      this.setData({ retail_price: e.detail.value });
    },
    
    // 选择商品图片
    chooseImage() {
      console.log('开始选择商品图片');
      // 先尝试使用chooseMedia API
      try {
        wx.chooseMedia({
          count: 1,
          mediaType: ['image'],
          sourceType: ['album', 'camera'],
          success: res => {
            console.log('选择商品图片成功:', res);
            const file = res.tempFiles[0];
            if (file.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
              wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
              return;
            }
            // 跳转到裁剪页面
            wx.navigateTo({
              url: `/admin/cropper/cropper?src=${encodeURIComponent(file.tempFilePath)}&type=product-create`
            });
          },
          fail: err => {
            console.error('chooseMedia 失败:', err);
            // 检查是否是用户取消操作
            if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
              console.log('用户取消选择商品图片');
              return; // 用户取消，直接返回
            }
            // 其他错误情况才使用备选方案
            this.chooseImageFallback();
          }
        });
      } catch (error) {
        console.error('chooseMedia 异常:', error);
        // 如果chooseMedia出现异常，尝试使用chooseImage作为备选方案
        this.chooseImageFallback();
      }
    },

    // 选择商品图片备选方案
    chooseImageFallback() {
      console.log('使用chooseImage作为备选方案选择商品图片');
      wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: res => {
          console.log('chooseImage选择商品图片成功:', res);
          const tempFilePath = res.tempFilePaths[0];
          const tempFile = res.tempFiles[0];
          if (tempFile && tempFile.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
            wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
            return;
          }
          // 跳转到裁剪页面
          wx.navigateTo({
            url: `/admin/cropper/cropper?src=${encodeURIComponent(tempFilePath)}&type=product-create`
          });
        },
        fail: err => {
          console.error('chooseImage 失败:', err);
          // 检查是否是用户取消操作
          if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
            console.log('用户取消选择商品图片');
            return; // 用户取消，直接返回
          }
          // 其他错误情况才显示提示
          wx.showToast({
            title: '选择图片失败，请检查相关权限',
            icon: 'none'
          });
        }
      });
    },
    
    // 删除图片
    deleteImage() {
      this.setData({
        image: '',
        imageTemp: '',
        customImageMode: false
      });
    },
    
    // 预览图片
    previewImage() {
      const imageUrl = this.data.imageTemp || this.data.image;
      if (imageUrl) {
        wx.previewImage({
          urls: [imageUrl],
          current: imageUrl
        });
      }
    },
    
    // 取消
    onCancel() {
      this.triggerEvent('cancel');
    },
    
    // 确认创建
    onConfirm() {
      const { sku, name, imageTemp, image, spec, platform_price, store_price, retail_price } = this.data;
      
      // 基本验证
      if (!sku.trim()) {
        wx.showToast({
          title: 'SKU号不能为空',
          icon: 'none'
        });
        return;
      }
      
      if (!name.trim()) {
        wx.showToast({
          title: '品名不能为空',
          icon: 'none'
        });
        return;
      }
      
      // 构建商品数据
      const productData = {
        sku: sku.trim(),
        name: name.trim(),
        image: imageTemp || image || '',
        spec: spec.trim(),
        platform_price: platform_price ? parseFloat(platform_price) : 0,
        store_price: store_price ? parseFloat(store_price) : 0,
        retail_price: retail_price ? parseFloat(retail_price) : 0
      };
      
      console.log('创建商品数据:', productData);
      
      // 触发确认事件，传递商品数据
      this.triggerEvent('confirm', productData);
    }
  }
});