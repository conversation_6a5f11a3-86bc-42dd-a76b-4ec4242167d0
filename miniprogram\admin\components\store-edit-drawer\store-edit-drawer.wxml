<view class="drawer-mask {{visible ? 'drawer-mask-show' : ''}}" bindtap="onCancel">
  <view class="drawer-content" catchtap="preventBubble">
    <!-- 头部 -->
    <view class="drawer-header">
      <text class="drawer-title">编辑门店</text>
      <view class="drawer-close" bindtap="onCancel">
        <text class="close-icon">×</text>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <scroll-view class="drawer-body" scroll-y="true">
      <!-- 门店图片 -->
      <view class="form-group">
        <view class="form-label">门店形象</view>
        <view class="image-upload-area">
          <image 
            class="store-image" 
            src="{{(storeImageTemp || editData.image || '/images/icons2/店铺.png') + (imageUpdateTime ? '?t=' + imageUpdateTime : '')}}" 
            mode="aspectFill"
            bindtap="onPreviewImage"
          />
          <view class="upload-hint" bindtap="onChooseImage">点击更换图片</view>
          <view wx:if="{{storeImageTemp || editData.image}}" class="delete-image-btn" bindtap="deleteStoreImage">
            <image src="/images/icons2/关闭.png" class="delete-icon"></image>
          </view>
        </view>
      </view>

      <!-- 门店名称 -->
      <view class="form-group">
        <view class="form-label">门店名称 <text class="required">*</text></view>
        <input 
          class="form-input" 
          placeholder="请输入门店名称（必填）" 
          value="{{editData.name}}"
          data-field="name"
          bindinput="onInputChange"
        />
      </view>

      <!-- 门店级别 -->
      <view class="form-group">
        <view class="form-label">门店级别</view>
        <picker 
          class="form-picker" 
          mode="selector" 
          range="{{levelOptions}}" 
          range-key="label"
          value="{{selectedLevelIndex}}"
          bindchange="onLevelChange"
        >
          <view class="picker-text">{{levelOptions[selectedLevelIndex].label}}</view>
        </picker>
      </view>

      <!-- 联系人 -->
      <view class="form-group">
        <view class="form-label">联系人 <text class="optional">（可选）</text></view>
        <input 
          class="form-input" 
          placeholder="请输入联系人姓名（可选）" 
          value="{{editData.contact_person}}"
          data-field="contact_person"
          bindinput="onInputChange"
        />
      </view>

      <!-- 联系电话 -->
      <view class="form-group">
        <view class="form-label">联系电话 <text class="optional">（可选）</text></view>
        <input 
          class="form-input" 
          placeholder="请输入联系电话（可选）" 
          value="{{editData.phone}}"
          data-field="phone"
          bindinput="onInputChange"
          type="number"
        />
      </view>

      <!-- 详细地址 -->
      <view class="form-group">
        <view class="form-label">详细地址 <text class="optional">（可选）</text></view>
        <textarea 
          class="form-textarea" 
          placeholder="请输入详细地址（可选）" 
          value="{{editData.address}}"
          data-field="address"
          bindinput="onInputChange"
          maxlength="200"
        />
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="drawer-footer">
      <button class="btn btn-cancel" bindtap="onCancel">取消</button>
      <button class="btn btn-confirm" bindtap="onConfirm">保存</button>
    </view>
  </view>
</view>