/* 导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  display: flex;
  align-items: center;
  background: #fff;
  z-index: 100;
  padding: 0 32rpx;
  border-bottom: 1px solid #f0f0f0;
}

.nav-left {
  font-size: 28rpx;
  color: #333;
  padding: 20rpx 0;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.back-btn, .more-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
}

.back-icon, .more-icon {
  width: 40rpx;
  height: 40rpx;
}

.container {
  background: #fff;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 群组信息 */
.group-info {
  background: #fff;
  padding: 32rpx;
  border-bottom: 1px solid #eee;
}

.group-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

/* 群组封面 - 1:1比例，正确完整显示 */
.cover-container {
  width: 160rpx;
  height: 160rpx;
  margin-right: 32rpx;
  border-radius: 24rpx;
  overflow: hidden;
  background: #f5f5f5;
  flex-shrink: 0;
  position: relative;
  border: 2rpx solid #eee;
}

.group-cover {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
  border-radius: 22rpx;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.cover-edit-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  border-radius: 22rpx;
}

.cover-container:hover .cover-edit-overlay,
.cover-container:active .cover-edit-overlay {
  opacity: 1;
}

.cover-edit-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.cover-edit-text {
  font-size: 20rpx;
  color: #fff;
  text-align: center;
}

.group-title {
  flex: 1;
}

.group-name-section {
  margin-bottom: 16rpx;
}

.group-name {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 0;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.group-name[bindtap] {
  padding: 8rpx 12rpx;
  background: rgba(0, 122, 255, 0.05);
  border: 2rpx dashed transparent;
}

.group-name[bindtap]:active {
  background: rgba(34, 162, 195, 0.1);
  border-color: #5698c3;
}

.group-desc-section {
  margin-bottom: 0;
}

.group-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 0;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.group-desc[bindtap] {
  padding: 8rpx 12rpx;
  background: rgba(0, 122, 255, 0.05);
  border: 2rpx dashed transparent;
}

.group-desc[bindtap]:active {
  background: rgba(86, 152, 195, 0.1);
  border-color: #5698c3;
}

.edit-hint {
  font-size: 20rpx;
  color: #5698c3;
  margin-left: 12rpx;
  opacity: 0.7;
}

.placeholder {
  color: #999;
  font-style: italic;
}

/* 群组元信息 - 创建者与创建时间左右并排 */
.group-meta {
  margin: 24rpx 0;
  padding: 24rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

.meta-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 32rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  flex: 1;
}

.meta-label {
  color: #666;
  margin-right: 8rpx;
  white-space: nowrap;
}

.meta-value {
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.group-notice {
  background: #f8f8f8;
  padding: 24rpx;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.notice-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.notice-edit-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: #5698c3;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
  transition: all 0.3s ease;
}

.notice-edit-btn:active {
  background: #1a8ba8;
  transform: scale(0.95);
}

.edit-icon {
  margin-right: 6rpx;
  font-size: 20rpx;
}

.edit-text {
  font-size: 24rpx;
}

.notice-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  min-height: 60rpx;
  padding: 12rpx 0;
}

.notice-content.clickable {
  cursor: pointer;
  border: 2rpx dashed transparent;
  border-radius: 8rpx;
  padding: 12rpx;
  transition: all 0.3s ease;
}

.notice-content.clickable:hover {
  border-color: #5698c3;
  background: rgba(86, 152, 195, 0.05);
}

.notice-content .placeholder {
  color: #999;
  font-style: italic;
}



/* 群组详情 */
.group-details {
  padding: 0 32rpx 32rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  max-width: 70%;
}

.announcement {
  max-width: 400rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 16rpx;
}

.qrcode-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 8rpx;
}







/* 群主信息 */
.group-owner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #f8f8f8;
  border-radius: 16rpx;
}

.owner-label {
  font-size: 28rpx;
  color: #666;
}

.owner-info {
  display: flex;
  align-items: center;
  color: rgba(255,255,255,0.8);
  margin-right: 12rpx;
}

.owner-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  margin-right: 12rpx;
  border: 2rpx solid rgba(255,255,255,0.6);
  background-color: #f5f5f5;
}

.owner-name {
  font-size: 26rpx;
  color: #fff;
  max-width: 300rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.member-count, .create-time {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: rgba(255,255,255,0.9);
}

.divider {
  margin: 0 12rpx;
  color: rgba(255,255,255,0.5);
}

/* 群成员区域 */
.member-count-section {
  padding: 32rpx;
  background: #fff;
  border-top: 16rpx solid #f5f5f5;
  border-bottom: 1px solid #eee;
}

.member-count {
  font-size: 28rpx;
  color: #333;
}



/* 成员列表区域 */
.member-section {
  background: #fff;
  border-top: 16rpx solid #f5f5f5;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.member-count {
  font-size: 28rpx;
  color: #999;
}

.member-list {
  padding: 32rpx;
}

.member-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx 20rpx;
}

.member-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* 统一的头像尺寸 - 120rpx */
.member-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: 12rpx;
  background: #f5f5f5;
  border: 1px solid #eee;
  object-fit: cover;
}

.member-name {
  font-size: 24rpx;
  color: #333;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120rpx;
}

/* 操作按钮 - 与成员头像完全一致的尺寸 */
.action-item {
  opacity: 0.9;
}

.action-item .member-name {
  font-size: 24rpx;
  color: #333;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120rpx;
}

.action-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 3rpx dashed #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #999;
  background: #fff;
  margin-bottom: 12rpx;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.add-member .action-avatar {
  border-color: #07c160;
  color: #07c160;
}

.add-member:active .action-avatar {
  background: #f0f9f0;
  transform: scale(0.95);
}

.remove-member .action-avatar {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.remove-member:active .action-avatar {
  background: #fff0f0;
  transform: scale(0.95);
}

/* 展开/收起按钮 */
.toggle-members {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
  color: #5698c3;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.toggle-icon {
  margin-left: 8rpx;
  font-size: 24rpx;
}



.member-role {
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  margin-top: 4rpx;
}

.member-role.owner {
  background: #ff9500;
  color: #fff;
}

.member-role.admin {
  background: #5698c3;
  color: #fff;
}

/* 成员角色标签 */
.member-role {
  display: inline-block;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  line-height: 1.4;
}

.member-role.owner {
  background: #ff9500;
  color: #fff;
}

.member-role.admin {
  background: #5698c3;
  color: #fff;
}

.join-time {
  font-size: 24rpx;
  color: #999;
}

/* 加入群聊按钮 */
.join-group-section {
  padding: 32rpx;
  background: #fff;
  border-top: 16rpx solid #f5f5f5;
}

.join-btn {
  width: 60%; /* 减小按钮宽度 */
  height: 72rpx; /* 减小按钮高度 */
  background: #5698c3; /* 改为晴蓝色 */
  color: #fff;
  font-size: 30rpx; /* 适当减小字体 */
  border-radius: 36rpx; /* 调整圆角比例 */
  border: none;
  margin: 32rpx auto 0; /* 使用auto实现按钮水平居中 */
  display: flex; /* 使用flex布局 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  padding: 0; /* 重置默认内边距 */
  line-height: 1; /* 重置行高 */
  box-sizing: border-box; /* 确保尺寸计算正确 */
}

/* 重置button组件的默认样式 */
.join-btn::after {
  border: none; /* 移除默认边框 */
}

.join-btn:active {
  opacity: 0.8; /* 点击时的透明度效果 */
  transform: scale(0.98); /* 点击时的缩放效果 */
}

.action-btn {
  width: 60%; /* 减小按钮宽度 */
  height: 72rpx; /* 减小按钮高度 */
  border-radius: 36rpx; /* 调整圆角比例 */
  font-size: 30rpx; /* 适当减小字体 */
  font-weight: 500;
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  margin: 0 auto 20rpx; /* 使用auto实现按钮水平居中 */
  padding: 0;
  line-height: 1;
  position: relative;
  border: none;
  transition: all 0.3s;
  background: #5698c3; /* 改为晴蓝色 */
  color: #fff;
}

.action-btn.leave {
  background: #ff4d4f;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.action-btn.join {
  background: #5698c3;
  color: #fff;
}

.action-btn.add-member {
  background: #5698c3;
  color: #fff;
  margin-bottom: 20rpx;
}

.action-btn.leave {
  background: #fff;
  color: #ff4d4f;
  border: 2rpx solid #ff4d4f;
  margin-bottom: 0;
}

.btn-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
}

/* 空状态 */
.empty-state {
  padding: 80rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

/* 适配iPhone X等全面屏 */
@supports (bottom: env(safe-area-inset-bottom)) {
  .action-buttons {
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  }
  .container {
    padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  }
}