/**
 * 合伙人端订单控制器
 * 处理合伙人端的订单相关操作
 */

/**
 * 创建采购订单
 */
exports.createPurchaseOrder = async (req, res, next) => {
  const db = require('../../config/db');
  
  try {
    const {
      store_no,
      product_id,
      quantity,
      payment_method = 'capital' // 默认股本金支付
    } = req.body;

    // 参数验证
    if (!store_no || !product_id || !quantity || quantity <= 0) {
      return res.status(400).json({
        success: false,
        message: '参数错误：门店编号、商品ID和数量不能为空，且数量必须大于0'
      });
    }

    // 获取用户ID（从token中解析或从请求中获取）
    const user_id = req.user?.user_id || req.body.user_id;
    if (!user_id) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    // 1. 检查商品信息和平台库存
    const productQuery = `
      SELECT id, name, platform_stock, store_price, 
             purchase_price_l1, purchase_price_l2, purchase_price_l3, 
             purchase_price_l4, purchase_price_l5
      FROM products 
      WHERE id = ? AND status = 1
    `;
    const productResult = await db.query(productQuery, [product_id]);
    
    if (!productResult || productResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: '商品不存在或已下架'
      });
    }

    const product = productResult[0];
    
    // 检查平台库存是否足够
    if (product.platform_stock < quantity) {
      return res.status(400).json({
        success: false,
        message: `库存不足，当前库存：${product.platform_stock}，需要：${quantity}`
      });
    }

    // 2. 获取门店信息
    const storeQuery = `SELECT store_no, name, level, capital FROM stores WHERE store_no = ?`;
    const storeResult = await db.query(storeQuery, [store_no]);
    
    if (!storeResult || storeResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: '门店不存在'
      });
    }

    const store = storeResult[0];
    
    // 3. 根据门店级别计算采购价
    let purchase_price = product.store_price; // 默认使用门店基准价
    
    if (store.level) {
      const levelField = `purchase_price_${store.level.toLowerCase()}`;
      if (product[levelField] && product[levelField] > 0) {
        purchase_price = product[levelField];
      }
    }

    // 计算订单总金额
    const total_amount = (purchase_price * quantity).toFixed(2);

    // 4. 查询门店资金信息（包括公积金）
    const fundQuery = 'SELECT capital, reserve_fund FROM store_funds WHERE store_no = ?';
    const fundRows = await db.query(fundQuery, [store_no]);
    
    let storeFunds = {
      capital: parseFloat(store.capital) || 0,
      reserve_fund: 0
    };
    
    if (fundRows.length > 0) {
      storeFunds.capital = parseFloat(fundRows[0].capital) || 0;
      storeFunds.reserve_fund = parseFloat(fundRows[0].reserve_fund) || 0;
    }
    
    // 检查门店资金是否足够
    if (payment_method === 'capital') {
      // 只使用股本金支付
      if (storeFunds.capital < total_amount) {
        return res.status(400).json({
          success: false,
          message: `股本金余额不足，当前余额：${storeFunds.capital}，需要：${total_amount}`
        });
      }
    } else if (payment_method === 'reserve') {
      // 只使用公积金支付
      if (storeFunds.reserve_fund < total_amount) {
        return res.status(400).json({
          success: false,
          message: `公积金余额不足，当前余额：${storeFunds.reserve_fund}，需要：${total_amount}`
        });
      }
    } else if (payment_method === 'mixed') {
      // 混合支付（股本金+公积金）
      const totalFunds = storeFunds.capital + storeFunds.reserve_fund;
      if (totalFunds < total_amount) {
        return res.status(400).json({
          success: false,
          message: `资金不足，股本金：${storeFunds.capital}，公积金：${storeFunds.reserve_fund}，总计：${totalFunds}，需要：${total_amount}`
        });
      }
    }

    // 5. 生成订单号
    const generateOrderNo = () => {
      const now = new Date();
      const year = now.getFullYear().toString();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = now.getDate().toString().padStart(2, '0');
      const hour = now.getHours().toString().padStart(2, '0');
      const minute = now.getMinutes().toString().padStart(2, '0');
      const sequence = (now.getMilliseconds() + Math.floor(Math.random() * 1000)).toString().slice(-4).padStart(4, '0');
      return `CG${year}${month}${day}${hour}${minute}${sequence}`;
    };

    const order_no = generateOrderNo();
    const now = Date.now();

    // 6. 开始数据库事务
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // 创建采购订单（包含采购价字段）
      const insertOrderQuery = `
        INSERT INTO store_orders (
          order_no, user_id, store_no, product_id, quantity, 
          total_amount, purchase_price, status, type, order_source, 
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const orderResult = await connection.execute(insertOrderQuery, [
        order_no, user_id, store_no, product_id, quantity,
        total_amount, purchase_price, '待审核', 'purchase', 'store',
        now, now
      ]);

      const order_id = orderResult[0].insertId;

      // 根据支付方式扣减门店资金
      if (payment_method === 'capital') {
        // 只扣减股本金
        await connection.execute(
          'UPDATE store_funds SET capital = capital - ?, update_time = ? WHERE store_no = ?',
          [total_amount, now, store_no]
        );
        
        // 同时更新stores表中的capital字段保持同步
        await connection.execute(
          'UPDATE stores SET capital = capital - ?, update_time = ? WHERE store_no = ?',
          [total_amount, now, store_no]
        );
        
        // 记录股本金支付
         await exports.recordFundPayment(connection, store_no, order_no, product.name, total_amount, 'capital');
        
      } else if (payment_method === 'reserve') {
        // 只扣减公积金
        await connection.execute(
          'UPDATE store_funds SET reserve_fund = reserve_fund - ?, update_time = ? WHERE store_no = ?',
          [total_amount, now, store_no]
        );
        
        // 记录公积金支付
         await exports.recordFundPayment(connection, store_no, order_no, product.name, total_amount, 'fund');
        
      } else if (payment_method === 'mixed') {
        // 混合支付：优先使用股本金，不足部分使用公积金
        const capitalAmount = Math.min(storeFunds.capital, parseFloat(total_amount));
        const reserveAmount = parseFloat(total_amount) - capitalAmount;
        
        if (capitalAmount > 0) {
          await connection.execute(
            'UPDATE store_funds SET capital = capital - ?, update_time = ? WHERE store_no = ?',
            [capitalAmount, now, store_no]
          );
          
          // 同时更新stores表中的capital字段保持同步
          await connection.execute(
            'UPDATE stores SET capital = capital - ?, update_time = ? WHERE store_no = ?',
            [capitalAmount, now, store_no]
          );
          
          // 记录股本金支付
           await exports.recordFundPayment(connection, store_no, order_no, product.name, capitalAmount, 'capital');
        }
        
        if (reserveAmount > 0) {
          await connection.execute(
            'UPDATE store_funds SET reserve_fund = reserve_fund - ?, update_time = ? WHERE store_no = ?',
            [reserveAmount, now, store_no]
          );
          
          // 记录公积金支付
           await exports.recordFundPayment(connection, store_no, order_no, product.name, reserveAmount, 'fund');
        }
      }

      // 提交事务
      await connection.commit();
      connection.release();

      // 返回成功结果
      res.json({
        success: true,
        message: '采购订单创建成功',
        data: {
          order_id,
          order_no,
          product_name: product.name,
          quantity,
          purchase_price,
          total_amount,
          status: '待审核'
        }
      });

    } catch (dbError) {
      // 回滚事务
      await connection.rollback();
      connection.release();
      throw dbError;
    }

  } catch (error) {
    console.error('创建采购订单失败:', error);
    res.status(500).json({
      success: false,
      message: '创建采购订单失败',
      error: error.message
    });
  }
};

// 记录资金支付的辅助方法
exports.recordFundPayment = async (connection, store_no, order_no, product_name, amount, account_type) => {
  // 生成凭证号
  const generateVoucherNo = () => {
    const now = new Date();
    const timestamp = now.getTime().toString();
    return `PAY${timestamp.slice(-10)}`;
  };

  const voucher_no = generateVoucherNo();
  const insertFundRecordQuery = `
    INSERT INTO store_fund_records (
      voucher_no, store_no, type, account_type, amount, 
      description, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?)
  `;
  
  await connection.execute(insertFundRecordQuery, [
    voucher_no, store_no, 'expense', account_type, amount,
    `采购订单支付 - 订单号：${order_no}，商品：${product_name}`,
    new Date()
  ]);
};

/**
 * 创建移库订单
 */
exports.createTransferOrder = async (req, res, next) => {
  try {
    // TODO: 实现创建移库订单的逻辑
    res.json({
      success: true,
      message: '移库订单创建成功',
      data: null
    });
  } catch (error) {
    next(error);
  }
};

/**
 * 获取门店订单列表
 */
exports.getStoreOrders = async (req, res, next) => {
  try {
    const db = require('../../config/db');
    const {
      page = 1,
      limit = 10,
      keyword = '',
      orderType = '',
      subType = '',
      sortBy = 'created_at_desc',
      storeNo = '',
      startDate = '',
      endDate = '',
      productKeyword = ''
    } = req.query;

    // 构建基础查询条件
    let whereConditions = [];
    let queryParams = [];

    // store_orders 表本身就只存储门店订单（采购订单和移库订单）

    // 门店筛选
    if (storeNo) {
      whereConditions.push('so.store_no = ?');
      queryParams.push(storeNo);
      console.log('添加门店筛选条件:', storeNo);
    } else {
      console.log('未指定门店筛选，将返回所有门店的采购订单和移库订单');
    }

    // 订单类型筛选
    if (orderType && orderType !== 'all') {
      whereConditions.push('so.type = ?');
      queryParams.push(orderType);
    }

    // 子类型筛选（状态筛选）- 前端直接传递中文状态
    if (subType && subType !== 'all') {
      whereConditions.push('so.status = ?');
      queryParams.push(subType);
    }

    // 关键词搜索（订单号）
    if (keyword) {
      whereConditions.push('so.order_no LIKE ?');
      queryParams.push(`%${keyword}%`);
    }

    // 日期范围筛选
    if (startDate && endDate) {
      whereConditions.push('so.created_at >= ? AND so.created_at <= ?');
      queryParams.push(new Date(startDate).getTime());
      queryParams.push(new Date(endDate).getTime());
    }

    // 构建WHERE子句
    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

    // 构建排序
    let orderByClause = 'ORDER BY so.created_at DESC';
    if (sortBy) {
      const [sortField, sortDirection] = sortBy.split('_');
      if (sortField === 'date') {
        orderByClause = `ORDER BY so.created_at ${sortDirection === 'desc' ? 'DESC' : 'ASC'}`;
      } else if (sortField === 'amount') {
        orderByClause = `ORDER BY so.total_amount ${sortDirection === 'desc' ? 'DESC' : 'ASC'}`;
      }
    }

    // 分页计算
    const pageNum = parseInt(page);
    const pageSize = parseInt(limit);
    const offset = (pageNum - 1) * pageSize;

    // 查询订单列表（包含采购价字段）
    const orderQuery = `
      SELECT 
        so.id,
        so.order_no,
        so.user_id,
        so.store_no,
        so.product_id,
        so.quantity,
        so.total_amount,
        so.purchase_price,
        so.status,
        so.type as order_type,
        so.created_at,
        so.updated_at,
        s.name as store_name,
        u.nickname as operator_name,
        p.name as product_name,
        p.images as product_image,
        p.price as product_price,
        p.spec as product_specs
      FROM store_orders so
      LEFT JOIN stores s ON so.store_no = s.store_no
      LEFT JOIN users u ON so.user_id = u.id
      LEFT JOIN products p ON so.product_id = p.id
      ${whereClause}
      ${orderByClause}
      LIMIT ? OFFSET ?
    `;

    // 查询总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM store_orders so
      LEFT JOIN stores s ON so.store_no = s.store_no
      LEFT JOIN users u ON so.user_id = u.id
      LEFT JOIN products p ON so.product_id = p.id
      ${whereClause}
    `;

    console.log('执行查询，SQL:', orderQuery);
    console.log('查询参数:', [...queryParams, pageSize, offset]);
    
    // 执行查询
    const [orders, countResult] = await Promise.all([
      db.query(orderQuery, [...queryParams, pageSize, offset]),
      db.query(countQuery, queryParams)
    ]);
    
    console.log('查询结果 - 订单数量:', orders.length, '总数:', countResult[0]?.total || 0);

    const total = countResult[0]?.total || 0;

    // 处理订单数据，将每个订单的商品信息组织成items数组
    const processedOrders = orders.map(order => {
      // 优先使用采购价，如果没有采购价则使用商品价格
      const unitPrice = parseFloat(order.purchase_price || order.product_price || 0);
      const quantity = parseInt(order.quantity || 0);
      const calculatedAmount = (unitPrice * quantity).toFixed(2);
      
      return {
        id: order.id,
        order_no: order.order_no,
        user_id: order.user_id,
        store_no: order.store_no,
        store_name: order.store_name || '未知门店',
        operator_name: order.operator_name || '未知用户',
        total_amount: calculatedAmount, // 基于采购价重新计算总金额
        purchase_price: parseFloat(order.purchase_price || 0).toFixed(2), // 添加采购价字段
        status: order.status,
        order_type: order.order_type,
        // 将时间戳转换为ISO字符串格式，便于前端处理
        created_at: order.created_at ? new Date(parseInt(order.created_at)).toISOString() : null,
        updated_at: order.updated_at ? new Date(parseInt(order.updated_at)).toISOString() : null,
        items: [{
          id: order.product_id,
          product_id: order.product_id,
          product_name: order.product_name || '未知商品',
          product_image: order.product_image,
          price: unitPrice.toFixed(2), // 显示采购价而不是销售价
          quantity: quantity,
          specs: order.product_specs,
          amount: calculatedAmount // 基于采购价计算的金额
        }],
        items_count: 1
      };
    });

    console.log('门店订单查询结果:', {
      total,
      ordersCount: processedOrders.length,
      page: pageNum,
      pageSize,
      queryParams,
      whereClause,
      rawOrders: orders.length
    });
    
    // 如果没有数据，输出调试信息
    if (orders.length === 0) {
      console.log('未找到订单数据，调试信息:');
      console.log('- 查询参数:', req.query);
      console.log('- WHERE条件:', whereClause);
      console.log('- 查询参数值:', queryParams);
      
      // 执行一个简单的查询来检查数据是否存在
      const debugQuery = 'SELECT COUNT(*) as total FROM store_orders';
      db.query(debugQuery).then(debugResult => {
        console.log('数据库中总订单数:', debugResult[0]?.total || 0);
      }).catch(debugErr => {
        console.error('调试查询失败:', debugErr);
      });
    }

    res.json({
      success: true,
      message: '获取门店订单列表成功',
      data: {
        orders: processedOrders,
        total: total,
        page: pageNum,
        pageSize: pageSize
      }
    });
  } catch (error) {
    console.error('获取门店订单列表失败:', error);
    next(error);
  }
};

/**
 * 获取门店订单详情
 */
exports.getStoreOrderDetail = async (req, res, next) => {
  try {
    const { orderId } = req.params;
    // TODO: 实现获取门店订单详情的逻辑
    res.json({
      success: true,
      message: '获取门店订单详情成功',
      data: {
        orderId: orderId,
        // 其他订单详情字段
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * 更新订单状态
 */
exports.updateOrderStatus = async (req, res, next) => {
  try {
    const { orderId } = req.params;
    const { status } = req.body;
    // TODO: 实现更新订单状态的逻辑
    res.json({
      success: true,
      message: '订单状态更新成功',
      data: {
        orderId: orderId,
        status: status
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * 处理门店订单支付
 */
exports.processStoreOrderPayment = async (req, res, next) => {
  try {
    const { orderId } = req.params;
    // TODO: 实现处理门店订单支付的逻辑
    res.json({
      success: true,
      message: '订单支付处理成功',
      data: {
        orderId: orderId,
        paymentStatus: 'success'
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * 获取门店库存
 */
exports.getStoreInventory = async (req, res, next) => {
  try {
    // TODO: 实现获取门店库存的逻辑
    res.json({
      success: true,
      message: '获取门店库存成功',
      data: {
        inventory: [],
        total: 0
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * 获取合伙人端顾客订单列表
 * 根据业务逻辑，需要获取两类订单：
 * 1. 销售人是当前用户的所有订单（不论归属哪个门店）
 * 2. 当前用户名下所有门店的订单（不论销售人是谁）
 */
exports.getPartnerCustomerOrders = async (req, res, next) => {
  try {
    const userId = req.user.id || req.user.userId;
    const {
      page = 1,
      limit = 10,
      status = '',
      store_no = '',
      delivery_method = '',
      date_range = '',
      keyword = ''
    } = req.query;

    console.log('获取合伙人端顾客订单，用户ID:', userId, '查询参数:', req.query);

    const db = require('../../config/db');
    const offset = (page - 1) * limit;

    // 构建基础查询条件
    let whereConditions = [];
    let queryParams = [];
    let havingConditions = [];
    let havingParams = [];

    // 核心业务逻辑：获取当前用户相关的订单
    // 1. 销售人是当前用户的订单
    // 2. 归属于当前用户名下门店的订单
    whereConditions.push(`(
      co.salesman_id = ? OR 
      cso.store_no IN (
        SELECT DISTINCT p.store_no 
        FROM partners p 
        WHERE p.user_id = ?
      )
    )`);
    queryParams.push(userId, userId);

    // 状态筛选 - 将前端英文状态映射为数据库中文状态
    if (status) {
      const statusMapping = {
        'pending_payment': '待支付',
        'pending_shipment': '待发货',
        'pending_pickup': '待自提', // 直接映射为数据库中的"待自提"状态
        'shipped': '已发货',
        'completed': '已完成', // 新增已完成状态映射
        'signed': '已签收',
        'returns': '已取消',
        'cancelled': '已取消'
      };
      
      const dbStatus = statusMapping[status] || status;
      whereConditions.push('cso.status = ?');
      queryParams.push(dbStatus);
    }

    // 门店筛选
    if (store_no) {
      if (store_no === 'PLATFORM_HQ') {
        // 筛选平台总部订单：门店编号为空或null的订单
        whereConditions.push('(cso.store_no IS NULL OR cso.store_no = "" OR cso.store_no = "0")');
      } else {
        // 筛选指定门店的订单
        whereConditions.push('cso.store_no = ?');
        queryParams.push(store_no);
      }
    }

    // 配送方式筛选
    if (delivery_method) {
      if (delivery_method === 'self') {
      // 筛选自提配送方式
      whereConditions.push('co.delivery_method = ?');
      queryParams.push('self');
      } else {
        whereConditions.push('co.delivery_method = ?');
        queryParams.push(delivery_method);
      }
    }

    // 日期范围筛选
    if (date_range) {
      const now = Date.now();
      let startTime, endTime;
      
      switch (date_range) {
        case 'today':
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          startTime = today.getTime();
          endTime = now;
          break;
        case 'week':
          startTime = now - 7 * 24 * 60 * 60 * 1000;
          endTime = now;
          break;
        case 'month':
          startTime = now - 30 * 24 * 60 * 60 * 1000;
          endTime = now;
          break;
      }
      
      if (startTime && endTime) {
        whereConditions.push('co.created_at BETWEEN ? AND ?');
        queryParams.push(startTime, endTime);
      }
    }

    // 关键词搜索（订单号、用户名）
    if (keyword) {
      whereConditions.push('(co.order_no LIKE ? OR u.nickname LIKE ?)');
      queryParams.push(`%${keyword}%`, `%${keyword}%`);
    }

    // 构建主查询SQL
    const mainSql = `
      SELECT 
        co.id as main_order_id,
        co.order_no,
        co.user_id,
        co.salesman_id,
        co.delivery_method,
        co.total_amount,
        co.total_quantity,
        co.created_at,
        co.updated_at,
        cso.id as sub_order_id,
        cso.sub_order_no,
        cso.store_no,
        cso.store_type,
        cso.sub_total_amount,
        cso.sub_total_quantity,
        cso.status,
        cso.shipped_at,
        cso.completed_at,
        u.nickname as customer_name,
        u.avatar as customer_avatar,
        salesman.nickname as salesman_name,
        s.name as store_name,
        GROUP_CONCAT(
          CONCAT(
            coi.product_name, '|',
            IFNULL(coi.product_image, ''), '|',
            coi.product_price, '|',
            coi.quantity, '|',
            coi.subtotal
          ) SEPARATOR ';;'
        ) as items_info
      FROM customer_orders co
      INNER JOIN customer_sub_orders cso ON co.id = cso.main_order_id
      LEFT JOIN customer_order_items coi ON cso.id = coi.sub_order_id
      LEFT JOIN users u ON co.user_id = u.user_id
      LEFT JOIN users salesman ON co.salesman_id = salesman.user_id
      LEFT JOIN stores s ON cso.store_no = s.store_no
      WHERE ${whereConditions.join(' AND ')}
      GROUP BY cso.id
      ORDER BY co.created_at DESC, cso.id DESC
      LIMIT ? OFFSET ?
    `;

    // 构建计数查询SQL
    const countSql = `
      SELECT COUNT(DISTINCT cso.id) as total
      FROM customer_orders co
      INNER JOIN customer_sub_orders cso ON co.id = cso.main_order_id
      LEFT JOIN users u ON co.user_id = u.user_id
      WHERE ${whereConditions.join(' AND ')}
    `;

    console.log('执行查询SQL:', mainSql);
    console.log('查询参数:', [...queryParams, parseInt(limit), parseInt(offset)]);

    // 执行查询
    const [orders, countResult] = await Promise.all([
      db.query(mainSql, [...queryParams, parseInt(limit), parseInt(offset)]),
      db.query(countSql, queryParams)
    ]);

    const total = countResult[0]?.total || 0;

    // 处理订单数据
    const processedOrders = orders.map(order => {
      // 处理商品信息
      let items = [];
      if (order.items_info) {
        items = order.items_info.split(';;').map(itemStr => {
          const [name, image, price, quantity, subtotal] = itemStr.split('|');
          return {
            product_name: name,
            product_image: image || '',
            product_price: parseFloat(price) || 0,
            quantity: parseInt(quantity) || 0,
            subtotal: parseFloat(subtotal) || 0
          };
        });
      }

      // 处理订单状态显示文本
      let statusText = '未知状态';
      switch(order.status) {
        case '待支付':
          statusText = '待支付';
          break;
        case '待发货':
          statusText = '待发货';
          break;
        case '已发货':
          statusText = '已发货';
          break;
        case '已签收':
          statusText = '已签收';
          break;
        case '退换货':
          statusText = '退换货';
          break;
        case '已取消':
          statusText = '已取消';
          break;
        default:
          statusText = order.status || '未知状态';
      }

      // 处理配送方式显示文本
      let deliveryText = '未知配送';
      switch(order.delivery_method) {
        case 'express':
          deliveryText = '快递配送';
          break;
        case 'self':
          deliveryText = '门店自提';
          break;
        default:
          deliveryText = order.delivery_method || '未知配送';
      }

      return {
        id: order.sub_order_id,
        main_order_id: order.main_order_id,
        order_no: order.order_no,
        sub_order_no: order.sub_order_no,
        customer_id: order.user_id,
        customer_name: order.customer_name || '未知用户',
        customer_avatar: order.customer_avatar || '',
        salesman_id: order.salesman_id,
        salesman_name: order.salesman_name || '未知销售',
        store_no: order.store_no,
        store_name: order.store_name || '未知门店',
        delivery_method: order.delivery_method,
        delivery_text: deliveryText,
        total_amount: parseFloat(order.sub_total_amount) || 0,
        total_quantity: parseInt(order.sub_total_quantity) || 0,
        status: order.status,
        status_text: statusText,
        created_at: order.created_at,
        updated_at: order.updated_at,
        shipped_at: order.shipped_at,
        completed_at: order.completed_at,
        items: items
      };
    });

    console.log(`查询到 ${processedOrders.length} 条订单，总计 ${total} 条`);

    res.json({
      success: true,
      message: '获取顾客订单列表成功',
      data: {
        orders: processedOrders,
        total: parseInt(total),
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('获取合伙人端顾客订单失败:', error);
    next(error);
  }
};