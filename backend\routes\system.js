/**
 * 系统路由
 */
const express = require('express');
const router = express.Router();
const db = require('../config/db');
const qrcodeController = require('../controllers/qrcodeController');

// 获取系统状态
router.get('/status', async (req, res) => {
  try {
    console.log('收到系统状态请求');

    // 检查数据库连接
    let dbStatus = {
      connected: false,
      database: process.env.USE_MYSQL === 'true' ? 'MySQL' : 'SQLite',
      name: process.env.DB_NAME || 'morebuy'
    };

    try {
      // 确保数据库已初始化
      if (!db.isInitialized()) {
        await db.init();
      }

      // 执行简单查询测试连接
      const result = await db.query('SELECT 1 as test');
      dbStatus.connected = result && result.length > 0;

      // 获取用户数量
      const userCount = await db.query('SELECT COUNT(*) as count FROM users');
      dbStatus.userCount = userCount[0].count;



      // 获取商品数量
      const productCount = await db.query('SELECT COUNT(*) as count FROM products');
      dbStatus.productCount = productCount[0].count;
    } catch (dbError) {
      console.error('检查数据库状态时出错:', dbError);
      dbStatus.error = dbError.message;
    }

    // 返回系统状态
    res.json({
      success: true,
      status: 'running',
      timestamp: new Date().toISOString(),
      env: process.env.NODE_ENV || 'development',
      database: dbStatus
    });
  } catch (error) {
    console.error('获取系统状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取系统状态失败',
      error: error.message
    });
  }
});

// 生成带scene参数的小程序二维码
router.get('/qrcode', qrcodeController.getQrcode);

// 提交客服留言
router.post('/feedback', async (req, res) => {
  try {
    const { message, name, contact, userId } = req.body;

    // 验证必填字段
    if (!message || !name || !contact) {
      return res.status(400).json({
        success: false,
        message: '留言内容、姓名和联系方式不能为空'
      });
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(contact)) {
      return res.status(400).json({
        success: false,
        message: '请输入正确的手机号'
      });
    }

    const currentTime = Date.now();

    // 插入留言到数据库
    const sql = `
      INSERT INTO user_feedback (
        user_id, name, contact, message, status, create_time, update_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    await db.query(sql, [
      userId || null,
      name,
      contact,
      message,
      '待处理', // 状态：待处理，处理中，已解决
      currentTime,
      currentTime
    ]);

    res.json({
      success: true,
      message: '留言提交成功，我们会尽快回复您'
    });

  } catch (error) {
    console.error('提交客服留言失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
});

module.exports = router;
