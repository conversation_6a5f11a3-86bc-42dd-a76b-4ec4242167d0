// pages/new-products/new-products.js
const { productApi, cartApi, favoriteApi } = require('../../utils/api');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    products: [], // 商品列表
    searchKeyword: '', // 搜索关键词
    sortType: 'default', // 排序类型：default, price_asc, price_desc
    page: 1, // 当前页码
    pageSize: 50, // 每页数量（增加以确保筛选后有足够的新品）
    loading: false, // 加载状态
    hasMore: true, // 是否还有更多数据
    imgErrorMap: {}, // 图片加载错误映射
    showFilter: false, // 是否显示筛选面板
    filterOptions: {
      minPrice: '',
      maxPrice: '',
      onlyDiscount: false
    },
    favoriteStatus: {}, // 存储每个商品的收藏状态 {productId: boolean}
    favoriteLoading: {} // 存储每个商品的收藏操作状态 {productId: boolean}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('[新品上市] 页面加载');
    this.loadProducts();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 页面显示时可以刷新数据
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    console.log('[新品上市] 下拉刷新');
    this.refreshProducts();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    console.log('[新品上市] 上拉加载更多');
    this.loadMoreProducts();
  },

  /**
   * 加载新品商品列表
   */
  loadProducts: function() {
    if (this.data.loading) {
      console.log('[新品上市] 正在加载中，跳过重复请求');
      return;
    }

    console.log('[新品上市] 开始加载商品列表，页码:', this.data.page);
    this.setData({ loading: true });

    const params = {
      page: this.data.page,
      pageSize: this.data.pageSize,
      isNew: 1, // 只获取新品
      keyword: this.data.searchKeyword || undefined,
      sortType: this.getSortParam(),
      minPrice: this.data.filterOptions.minPrice || undefined,
      maxPrice: this.data.filterOptions.maxPrice || undefined,
      onlyDiscount: this.data.filterOptions.onlyDiscount || undefined
    };

    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === undefined || params[key] === '') {
        delete params[key];
      }
    });

    console.log('[新品上市] 请求参数:', params);

    productApi.getProducts(params)
      .then(res => {
        console.log('[新品上市] 商品列表获取成功:', res);
        const newProducts = res.data?.list || [];
        
        console.log('[新品上市] 获取到的新品数量:', newProducts.length);
        console.log('[新品上市] 新品商品详情:', newProducts.map(p => ({ id: p.id, name: p.name, isNew: p.isNew })));
        
        if (this.data.page === 1) {
          // 首次加载或刷新
          this.setData({
            products: newProducts,
            hasMore: newProducts.length >= this.data.pageSize
          });
        } else {
          // 加载更多
          this.setData({
            products: [...this.data.products, ...newProducts],
            hasMore: newProducts.length >= this.data.pageSize
          });
        }
      })
      .catch(err => {
        console.error('[新品上市] 商品列表获取失败:', err);
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ loading: false });
        wx.stopPullDownRefresh();
      });
  },

  /**
   * 刷新商品列表
   */
  refreshProducts: function() {
    console.log('[新品上市] 刷新商品列表');
    this.setData({
      page: 1,
      hasMore: true
    });
    this.loadProducts();
  },

  /**
   * 加载更多商品
   */
  loadMoreProducts: function() {
    if (this.data.loading || !this.data.hasMore) {
      console.log('[新品上市] 跳过加载更多 - loading:', this.data.loading, 'hasMore:', this.data.hasMore);
      return;
    }

    console.log('[新品上市] 加载更多商品，当前页:', this.data.page, '-> 下一页:', this.data.page + 1);
    this.setData({
      page: this.data.page + 1
    });
    this.loadProducts();
  },

  /**
   * 搜索输入
   */
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 搜索确认
   */
  onSearchConfirm: function() {
    console.log('[新品上市] 搜索确认，关键词:', this.data.searchKeyword);
    this.refreshProducts();
  },

  /**
   * 排序点击
   */
  onSortTap: function(e) {
    const sortType = e.currentTarget.dataset.type;
    console.log('[新品上市] 切换排序:', sortType);
    
    // 如果点击的是价格排序，实现三种状态循环切换
    if (sortType === 'price') {
      let newSortType;
      switch (this.data.sortType) {
        case 'default':
          newSortType = 'price_asc'; // 默认 → 升序
          break;
        case 'price_asc':
          newSortType = 'price_desc'; // 升序 → 降序
          break;
        case 'price_desc':
          newSortType = 'default'; // 降序 → 默认
          break;
        default:
          newSortType = 'price_asc';
          break;
      }
      this.setData({
        sortType: newSortType
      });
    } else {
      // 其他排序类型直接设置
      this.setData({
        sortType: sortType
      });
    }
    
    this.refreshProducts();
  },

  /**
   * 获取排序参数
   */
  getSortParam: function() {
    switch (this.data.sortType) {
      case 'price_asc':
        return 'price_asc';
      case 'price_desc':
        return 'price_desc';
      default:
        return 'default';
    }
  },

  /**
   * 显示筛选面板
   */
  showFilter: function() {
    this.setData({
      showFilter: true
    });
  },

  /**
   * 隐藏筛选面板
   */
  hideFilter: function() {
    this.setData({
      showFilter: false
    });
  },

  /**
   * 输入最低价格
   */
  inputMinPrice: function(e) {
    const filterOptions = this.data.filterOptions;
    filterOptions.minPrice = e.detail.value;
    this.setData({
      filterOptions: filterOptions
    });
  },

  /**
   * 输入最高价格
   */
  inputMaxPrice: function(e) {
    const filterOptions = this.data.filterOptions;
    filterOptions.maxPrice = e.detail.value;
    this.setData({
      filterOptions: filterOptions
    });
  },

  /**
   * 切换仅显示优惠商品
   */
  toggleOnlyDiscount: function() {
    const filterOptions = this.data.filterOptions;
    filterOptions.onlyDiscount = !filterOptions.onlyDiscount;
    this.setData({
      filterOptions: filterOptions
    });
  },

  /**
   * 重置筛选条件
   */
  resetFilter: function() {
    this.setData({
      filterOptions: {
        minPrice: '',
        maxPrice: '',
        onlyDiscount: false
      }
    });
  },

  /**
   * 应用筛选条件
   */
  applyFilter: function() {
    this.setData({
      showFilter: false
    });
    this.refreshProducts();
  },

  /**
   * 商品点击
   */
  onProductTap: function(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('[新品上市] 点击商品:', productId);
    
    wx.navigateTo({
      url: `/pages/product/detail?id=${productId}`
    });
  },

  /**
   * 商品图片加载错误
   */
  onProductImgError: function(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('[新品上市] 商品图片加载失败:', productId);
    
    this.setData({
      [`imgErrorMap.${productId}`]: true
    });
  },

  /**
   * 添加/取消收藏
   */
  onFavoriteTap: function(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('收藏操作:', productId);
    
    // 检查是否正在操作
    if (this.data.favoriteLoading[productId]) {
      return;
    }
    
    // 检查登录状态
    const app = getApp();
    app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        this.performFavoriteToggle(productId);
      }
    });
  },

  /**
   * 执行收藏/取消收藏操作
   */
  performFavoriteToggle: function(productId) {
    // 设置加载状态
    this.setData({
      [`favoriteLoading.${productId}`]: true
    });
    
    const isCurrentlyFavorite = this.data.favoriteStatus[productId];
    
    if (isCurrentlyFavorite) {
      // 取消收藏
      favoriteApi.removeFromFavorites(productId)
        .then(res => {
          this.setData({
            [`favoriteLoading.${productId}`]: false
          });
          
          if (res.success) {
            this.setData({
              [`favoriteStatus.${productId}`]: false
            });
            wx.showToast({
              title: '已取消收藏',
              icon: 'success',
              duration: 1500
            });
          } else {
            wx.showToast({
              title: res.message || '操作失败',
              icon: 'none'
            });
          }
        })
        .catch(err => {
          console.error('取消收藏失败', err);
          this.setData({
            [`favoriteLoading.${productId}`]: false
          });
          wx.showToast({
            title: '网络错误，请稍后再试',
            icon: 'none'
          });
        });
    } else {
      // 添加收藏
      favoriteApi.addToFavorites(productId)
        .then(res => {
          this.setData({
            [`favoriteLoading.${productId}`]: false
          });
          
          if (res.success) {
            this.setData({
              [`favoriteStatus.${productId}`]: true
            });
            wx.showToast({
              title: '已加入收藏夹',
              icon: 'success',
              duration: 1500
            });
          } else {
            // 特殊处理：如果商品已在收藏夹中
            if (res.message && res.message.includes('已在收藏夹中')) {
              this.setData({
                [`favoriteStatus.${productId}`]: true
              });
              wx.showToast({
                title: '您已经收藏过该商品',
                icon: 'none',
                duration: 2000
              });
            } else {
              wx.showToast({
                title: res.message || '添加失败',
                icon: 'none'
              });
            }
          }
        })
        .catch(err => {
          console.error('添加到收藏夹失败', err);
          this.setData({
            [`favoriteLoading.${productId}`]: false
          });
          
          // 特殊处理：如果错误信息包含"已在收藏夹中"
          if (err.message && err.message.includes('已在收藏夹中')) {
            this.setData({
              [`favoriteStatus.${productId}`]: true
            });
            wx.showToast({
              title: '您已经收藏过该商品',
              icon: 'none',
              duration: 2000
            });
          } else {
            wx.showToast({
              title: '网络错误，请稍后再试',
              icon: 'none'
            });
          }
        });
    }
  },

  /**
   * 加入购物车
   */
  onAddToCartTap: function(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('加入购物车:', productId);
    
    // 检查登录状态
    const app = getApp();
    app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        this.performAddToCart(productId);
      }
    });
  },

  /**
   * 执行添加到购物车操作
   */
  performAddToCart: function(productId) {
    // 显示加载中
    wx.showLoading({
      title: '正在添加到购物车',
      mask: true
    });
    
    // 使用API添加商品到购物车
    cartApi.addToCart(productId, 1)
      .then(res => {
        wx.hideLoading();
        
        if (res.success) {
          // 显示成功提示
          wx.showToast({
            title: '已加入购物车',
            icon: 'success',
            duration: 1500
          });
        } else {
          wx.showToast({
            title: res.message || '添加失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('添加到购物车失败', err);
        wx.hideLoading();
        
        // 显示错误提示
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  }
});