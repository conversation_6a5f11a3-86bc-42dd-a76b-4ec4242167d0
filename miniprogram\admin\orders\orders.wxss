.orders-container {
  background: #f7f7f7;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.first-level-tabs {
  display: flex;
  gap: 16rpx;
  padding: 40rpx 24rpx 0 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
  overflow-x: auto;
  white-space: nowrap;
  min-height: 72rpx;
  align-items: center;
  box-sizing: border-box;
  padding-bottom: 24rpx;
}
.first-tab {
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #888;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  line-height: 56rpx;
  background: none;
  border-radius: 0;
  display: inline-block;
  white-space: nowrap;
  border-bottom: 4rpx solid transparent;
}
.first-tab.active {
  color: #222;
  font-weight: bold;
  border-bottom: 4rpx solid #FF4D4F;
  background: none;
}
.second-level-tabs {
  display: flex;
  gap: 12rpx;
  padding: 28rpx 24rpx 0 24rpx;
  background: #fff;
  overflow-x: auto;
  white-space: nowrap;
  min-height: 80rpx;
  align-items: center;
  box-sizing: border-box;
  justify-content: flex-start; /* 改为左对齐，避免标签超出屏幕 */
  padding-bottom: 24rpx;
}
.second-tab {
  padding: 12rpx 36rpx;
  border-radius: 20rpx;
  background: #f2f2f2;
  color: #222;
  font-size: 26rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-block;
  white-space: nowrap;
}
.second-tab.active {
  background: #222;
  color: #fff;
  font-weight: bold;
}
/* 订单列表容器 */
.order-list-container {
  padding: 24rpx;
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 80rpx 0;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 订单列表 */
.order-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 订单项 */
.order-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.order-item:active {
  background: #fafafa;
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
  flex: 1;
}

.order-no {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-time {
  font-size: 22rpx;
  color: #999;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-pending_payment {
  background: #fff7e6;
  color: #fa8c16;
}

.status-pending_shipment {
  background: #e6f7ff;
  color: #1890ff;
}

.status-shipped {
  background: #f6ffed;
  color: #52c41a;
}

.status-completed {
  background: #f0f0f0;
  color: #666;
}

.status-cancelled {
  background: #fff2f0;
  color: #ff4d4f;
}

/* 顾客信息 */
.customer-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
  padding: 12rpx 16rpx;
  background: linear-gradient(135deg, #f8fbff 0%, #f0f8ff 100%);
  border-radius: 8rpx;
  border: 1rpx solid #e6f4ff;
}

.customer-avatar {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: #f0f0f0;
  border: 2rpx solid #fff;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.customer-details {
  display: flex;
  flex-direction: column;
  gap: 2rpx;
  flex: 1;
}

.customer-nickname {
  font-size: 26rpx;
  font-weight: 600;
  color: #1890ff;
}

.customer-phone {
  font-size: 22rpx;
  color: #666;
  margin-top: 2rpx;
}

.customer-id {
  font-size: 20rpx;
  color: #999;
  margin-top: 2rpx;
}

.salesman-id {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
  display: block;
}

/* 门店信息 */
.store-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
  padding: 10rpx 16rpx;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f4ff 100%);
  border-radius: 6rpx;
  border: 1rpx solid #d9f7be;
}

.store-label {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

.store-name {
  font-size: 24rpx;
  color: #52c41a;
  font-weight: 600;
}

.store-no {
  font-size: 20rpx;
  color: #999;
}

/* 销售人信息 */
.salesman-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 12rpx;
  padding: 10rpx 16rpx;
  background: linear-gradient(135deg, #fff7e6 0%, #fff2e8 100%);
  border-radius: 6rpx;
  border: 1rpx solid #ffe7ba;
}

.salesman-label {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

.salesman-name {
  font-size: 24rpx;
  color: #fa8c16;
  font-weight: 600;
}

.salesman-phone {
  font-size: 20rpx;
  color: #999;
}

/* 订单内容 */
.order-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.order-type {
  font-size: 26rpx;
  color: #666;
  background: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

/* 订单金额 */
.order-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  padding: 12rpx 16rpx;
  background: linear-gradient(135deg, #fff9f0 0%, #fff2e8 100%);
  border-radius: 8rpx;
  border: 1rpx solid #ffe7ba;
}

.amount-label {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.amount-value {
  font-size: 28rpx;
  color: #fa8c16;
  font-weight: 700;
}

/* 顾客订单商品列表 */
.customer-order-items {
  margin-bottom: 12rpx;
  border: 1rpx solid #f0f8ff;
  border-radius: 8rpx;
  background: linear-gradient(135deg, #fafcff 0%, #f5f9ff 100%);
  padding: 12rpx;
}

/* 商品清单头部 */
.items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
  padding-bottom: 8rpx;
  border-bottom: 1rpx solid rgba(24, 144, 255, 0.1);
}

.items-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #1890ff;
}

.items-count {
  font-size: 20rpx;
  color: #999;
  background: #f0f8ff;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}

/* 门店采购订单商品列表 */
.purchase-order-items {
  margin-bottom: 16rpx;
  border: 1rpx solid #e8f4fd;
  border-radius: 8rpx;
  background: #f8fcff;
  padding: 12rpx;
}

.product-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 8rpx 0;
  border-bottom: 1rpx solid rgba(240, 240, 240, 0.6);
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 72rpx;
  height: 72rpx;
  border-radius: 6rpx;
  background: #f5f5f5;
  border: 1rpx solid #eee;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.product-name {
  font-size: 24rpx;
  color: #333;
  line-height: 1.3;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
  margin-top: 4rpx;
}

/* 商品右侧信息（数量和小计） */
.product-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 6rpx;
  min-width: 120rpx;
}

.product-quantity {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
}

.product-subtotal {
  font-size: 22rpx;
  color: #ff4d4f;
  font-weight: 600;
}

/* 保留旧的product-details样式以防其他地方使用 */
.product-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  margin-top: 4rpx;
}

/* 订单操作 */
.order-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
  margin-top: 16rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: 1rpx solid #d9d9d9;
  background: #fff;
  color: #666;
}

.action-btn.primary {
  background: #1890ff;
  color: #fff;
  border-color: #1890ff;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #333;
  border-color: #d9d9d9;
}

.action-btn.disabled {
  background: #f0f0f0;
  color: #999;
  border-color: #e0e0e0;
  cursor: not-allowed;
}

.action-btn.remind {
  background: #fff7e6;
  color: #fa8c16;
  border-color: #ffd591;
}

.action-btn.approve {
  background: #f6ffed;
  color: #52c41a;
  border-color: #b7eb8f;
}

.action-btn.reject {
  background: #fff2f0;
  color: #ff4d4f;
  border-color: #ffccc7;
}

/* 空状态 */
.empty-container {
  text-align: center;
  padding: 120rpx 0;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.load-more-text {
  color: #999;
  font-size: 26rpx;
}

/* 没有更多数据 */
.no-more {
  text-align: center;
  padding: 40rpx 0;
}

.no-more-text {
  color: #ccc;
  font-size: 24rpx;
}

/* 发货弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: #fff;
  border-radius: 16rpx;
  width: 600rpx;
  max-width: 90vw;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
  cursor: pointer;
}

.modal-body {
  padding: 32rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #1890ff;
  outline: none;
}

.modal-footer {
  display: flex;
  gap: 24rpx;
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: 1rpx solid #d9d9d9;
  background: #fff;
  color: #333;
}

.modal-btn.cancel {
  background: #f5f5f5;
  color: #666;
  border-color: #d9d9d9;
}

.modal-btn.confirm {
  background: #1890ff;
  color: #fff;
  border-color: #1890ff;
}