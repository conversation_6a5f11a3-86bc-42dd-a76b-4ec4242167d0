顾客订单【配送方式为快递】的详细逻辑

一、顾客用户提交订单时，首先以平台库存判断订单是否可创建
1、平台库存查询商品表（products）的字段（platform_stock）；
2、如果平台库存不足，弹出友好提示“商品库存不足，请联系客服确认后再下单”。

二、订单的销售人归属
1、顾客用户所有的订单，业绩都归属其“销售人”（写入此订单的销售人字段）；
2、如果顾客用户没有销售人，则订单直接归属平台总部（没有销售人，也就没有任何门店归属了）

三、订单的门店归属——门店归属是动态的，按以下顺序逐个拆分：
1、首先划到用户的【订阅门店】，如果【订阅门店】库存充足（与订单库存逐个商品对比），则有库存的部分归属【订阅门店】，超过库存的部分，继续按以下顺序拆分：
2、然后找到”销售人“名下的其他门店，逐一按照库存对比，有库存的部分归属该门店，超出的，再次拆分订单，以此类推；
3、当”销售人“名下的所有门店库存都匹配之后，订单量还超出的，则归属到平台总部。

举例说明：（基础情况：顾客"用户A"的销售人是"用户B"，订阅门店是"门店1#"），当用户A提交订单为商品P，数量为5件，此时：
1、如果门店1#的“商品P”库存为1件，则门店1#拆分一个订单（数量为1件）；
2、销售人“用户B”名下另一个门店2#的商品P库存为2件，则门店2#拆分一个订单（数量为2件）；
3、销售人“用户B”没有其他门店，或者其他门店没有商品P的库存，则不能拆分，此时订单数量还差2件，全部归属平台总部，平台总部拆分一个订单（数量为2件），至此，顾客订单5件拆分完毕。
（总结：顾客订单拆分成了3个分订单，门店1#为1件，门店2#为2件，平台总部为2件；这三个订单的业绩都归属销售人“用户B”）

四、库存的扣减
1、根据订单拆分情况，获得订单的门店，按照归属订单的商品数量扣减此门店的库存（针对门店库存表的操作——store_inventory表的cloud_quantity字段），并且同时扣减平台库存（针对商品表的操作——products表的platform_stock字段）
2、归属平台总的订单，则直接扣减平台库存（针对商品表的操作——products表的platform_stock字段）

五、订单的资金分配（除了以下的分配，剩余则是归属平台的利润，这里不作专门说明）
1、门店的商品成本
，，门店获得订单时，订单上的商品【门店采购价】即是门店的商品成本，订单完成时，按此金额直接划入至【门店股本金】账户，记录为销售回款。

2、销售人佣金
，，对于归属门店的订单，按此公式计算：【销售人佣金】=（销售价-采购基准价）*70%
，，对于归属平台总部的订单，按此公式计算：【销售人佣金】=（销售价-采购基准价）*30%
，，订单创建后计入销售人“待结销售分佣”，订单完成后佣金转入用户的账户余额

3、门店的利润
，，计算公式：【门店利润】=订单金额-门店的商品成本-【销售人佣金】
，，订单创建后，门店的利润直接计入【门店利润池】账户（记录为销售利润）


