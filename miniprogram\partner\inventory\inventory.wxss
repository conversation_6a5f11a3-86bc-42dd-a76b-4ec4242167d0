/* partner/inventory/inventory.wxss */
/* 合伙人端门店库存页面样式 */

.inventory-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 门店选择区域样式 */
.store-select-section {
  background-color: #ffffff;
  padding: 15px;
  margin-bottom: 10px;
  border-radius: 6px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.store-select-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.section-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.store-select-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.store-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 36px;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.store-name {
  font-size: 14px;
  color: #333;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-arrow {
  font-size: 12px;
  color: #999;
  margin-left: 5px;
}

/* 搜索栏样式 */
.search-section {
  background-color: #ffffff;
  padding: 10px 15px;
  margin-bottom: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 5px 15px;
  height: 36px;
}

.search-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.search-input {
  flex: 1;
  height: 36px;
  font-size: 14px;
  color: #333;
}

.search-placeholder {
  color: #999;
  font-size: 14px;
}

/* 排序筛选栏样式 */
.sort-filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #ffffff;
  border-bottom: 1px solid #eee;
}

.sort-options {
  display: flex;
  align-items: center;
}

.sort-item {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  margin-right: 15px;
  font-size: 14px;
  color: #666;
}

.sort-item.active {
  color: #FF6B35;
  font-weight: 500;
}

.sort-icon {
  width: 12px;
  height: 12px;
  margin-left: 3px;
}

.action-btns {
  display: flex;
  align-items: center;
}

.batch-save-btn {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #999;
  background-color: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  margin-right: 10px;
}

.batch-save-btn.active {
  background-color: #FF6B35;
  color: #ffffff;
}

.filter-btn {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.filter-icon {
  width: 14px;
  height: 14px;
  margin-left: 3px;
}

/* 商品列表样式 */
.products-section {
  flex: 1;
  padding: 10px;
}

.product-card {
  display: flex;
  flex-wrap: wrap;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  margin-left: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-spec {
  font-size: 12px;
  color: #999;
  margin-bottom: 6px;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}

.retail-price {
  font-size: 12px;
  color: #666;
}

.purchase-price {
  font-size: 12px;
  color: #5698c3;
  font-weight: 500;
}

.inventory-row {
  display: flex;
  justify-content: space-between;
}

.cloud-inventory, .local-inventory {
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
}

.cloud-inventory.modified {
  color: #FF6B35;
}

.new-value {
  color: #FF6B35;
  font-weight: bold;
  margin-left: 4px;
}

/* 库存编辑区域样式 */
.inventory-edit-section {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed #eee;
}

.inventory-edit-controls {
  display: flex;
  align-items: center;
}

.inventory-decrease, .inventory-increase {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #333;
  font-weight: bold;
}

.inventory-decrease:active, .inventory-increase:active {
  background-color: #e0e0e0;
}

.inventory-input {
  width: 60px;
  height: 28px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin: 0 5px;
  text-align: center;
  font-size: 14px;
  color: #333;
}

.inventory-save-btn {
  height: 28px;
  padding: 0 12px;
  border-radius: 4px;
  background-color: #f5f5f5;
  color: #999;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.inventory-save-btn.active {
  background-color: #FF6B35;
  color: #ffffff;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.empty-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #FF6B35;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 12px;
  color: #999;
}

/* 加载更多和没有更多样式 */
.load-more, .no-more {
  text-align: center;
  padding: 15px 0;
}

.load-more-text, .no-more-text {
  font-size: 12px;
  color: #999;
}

/* 筛选抽屉样式 */
.filter-drawer {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  visibility: hidden;
  transition: visibility 0.3s;
}

.filter-drawer.show {
  visibility: visible;
}

.filter-drawer-mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
}

.filter-drawer.show .filter-drawer-mask {
  opacity: 1;
}

.filter-drawer-content {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 80%;
  background-color: #ffffff;
  transform: translateX(100%);
  transition: transform 0.3s;
  display: flex;
  flex-direction: column;
}

.filter-drawer.show .filter-drawer-content {
  transform: translateX(0);
}

.filter-drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.filter-drawer-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.filter-drawer-close {
  padding: 5px;
}

.close-icon {
  width: 16px;
  height: 16px;
}

.filter-drawer-body {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-section-title {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 10px;
}

.price-range-inputs, .inventory-range-inputs {
  display: flex;
  align-items: center;
}

.price-input, .inventory-input {
  flex: 1;
  height: 36px;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 0 10px;
  font-size: 14px;
  color: #333;
}

.price-range-separator, .inventory-range-separator {
  margin: 0 10px;
  color: #999;
}

.category-list {
  display: flex;
  flex-wrap: wrap;
}

.category-item {
  padding: 6px 12px;
  background-color: #f5f5f5;
  border-radius: 15px;
  margin-right: 10px;
  margin-bottom: 10px;
  font-size: 12px;
  color: #666;
}

.category-item.active {
  background-color: #FF6B35;
  color: #ffffff;
}

.filter-drawer-footer {
  display: flex;
  padding: 15px;
  border-top: 1px solid #eee;
}

.reset-btn, .apply-btn {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reset-btn {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 10px;
}

.apply-btn {
  background-color: #FF6B35;
  color: #ffffff;
}