<!--合伙人端 - 我的推荐页面-->
<view class="my-referrals-container">
  <!-- 统计区域 -->
  <view class="stats-section">
    <view class="tab-header">
      <view class="tab-item {{activeTab === 0 ? 'active' : ''}}" data-tab="{{0}}" bindtap="switchTab">
        <text class="tab-title">我推荐的合伙人</text>
        <text class="tab-count">{{partnerTotal}}</text>
      </view>
      <view class="tab-item {{activeTab === 1 ? 'active' : ''}}" data-tab="{{1}}" bindtap="switchTab">
        <text class="tab-title">我推荐的顾客</text>
        <text class="tab-count">{{customerTotal}}</text>
      </view>
    </view>
  </view>

  <!-- 排序筛选栏 -->
  <view class="sort-filter-bar">
    <!-- 我推荐的合伙人排序栏 -->
    <block wx:if="{{activeTab === 0}}">
      <view class="sort-buttons">
        <view class="sort-btn {{partnerSortBy === 'partner_type' ? 'active' : ''}}" data-field="partner_type" bindtap="toggleSort">
          <text class="sort-btn-text">类型</text>
          <image wx:if="{{partnerSortBy === 'partner_type' && partnerSortOrder}}" class="sort-icon" src="/images/icons2/{{partnerSortOrder === 'desc' ? '向下' : '向上'}}.svg"></image>
        </view>
        <view class="sort-btn {{partnerSortBy === 'join_date' ? 'active' : ''}}" data-field="join_date" bindtap="toggleSort">
          <text class="sort-btn-text">日期</text>
          <image wx:if="{{partnerSortBy === 'join_date' && partnerSortOrder}}" class="sort-icon" src="/images/icons2/{{partnerSortOrder === 'desc' ? '向下' : '向上'}}.svg"></image>
        </view>
        <view class="filter-btn" bindtap="showFilter">
          <text class="filter-btn-text">筛选</text>
          <image class="filter-icon" src="/images/icons2/筛选.svg"></image>
        </view>
      </view>
    </block>
    
    <!-- 我推荐的顾客排序栏 -->
    <block wx:if="{{activeTab === 1}}">
      <view class="sort-buttons">
        <view class="sort-btn {{customerSortBy === 'register_date' ? 'active' : ''}}" data-field="register_date" bindtap="toggleSort">
          <text class="sort-btn-text">日期</text>
          <image wx:if="{{customerSortBy === 'register_date' && customerSortOrder}}" class="sort-icon" src="/images/icons2/{{customerSortOrder === 'desc' ? '向下' : '向上'}}.svg"></image>
        </view>
        <view class="sort-btn {{customerSortBy === 'user_id' ? 'active' : ''}}" data-field="user_id" bindtap="toggleSort">
          <text class="sort-btn-text">ID号</text>
          <image wx:if="{{customerSortBy === 'user_id' && customerSortOrder}}" class="sort-icon" src="/images/icons2/{{customerSortOrder === 'desc' ? '向下' : '向上'}}.svg"></image>
        </view>
        <view class="sort-btn {{customerSortBy === 'total_consumption' ? 'active' : ''}}" data-field="total_consumption" bindtap="toggleSort">
          <text class="sort-btn-text">累计消费</text>
          <image wx:if="{{customerSortBy === 'total_consumption' && customerSortOrder}}" class="sort-icon" src="/images/icons2/{{customerSortOrder === 'desc' ? '向下' : '向上'}}.svg"></image>
        </view>
        <view class="filter-btn" bindtap="showFilter">
          <text class="filter-btn-text">筛选</text>
          <image class="filter-icon" src="/images/icons2/筛选.svg"></image>
        </view>
      </view>
    </block>
  </view>

  <!-- 用户列表 -->
  <view class="user-list">
    <!-- 我推荐的合伙人列表 -->
    <block wx:if="{{activeTab === 0}}">
      <view wx:if="{{partnerReferrals.length === 0 && !partnerLoading}}" class="empty-state">
        <text class="empty-text">暂无推荐的合伙人</text>
      </view>
      <view wx:else>
        <view wx:for="{{partnerReferrals}}" wx:key="user_id" class="user-card" data-user-id="{{item.user_id}}" bindtap="viewUserDetail">
          <view class="user-avatar">
            <image src="{{item.avatar || '/images/profile.png'}}" mode="aspectFill" />
          </view>
          <view class="user-info">
            <view class="user-basic">
              <text class="user-nickname">{{item.nickname || '未设置昵称'}}</text>
              <text class="user-id">ID: {{item.user_id}}</text>
            </view>
            <view class="user-details">
              <text class="user-store">门店: {{item.store_name || '暂无门店'}}</text>
              <text class="user-type">类型: {{item.partner_type_name || ''}}</text>
            </view>
            <view class="user-meta">
              <text class="join-date">加入时间: {{item.join_date_formatted}}</text>
            </view>
          </view>
          <view class="user-arrow">></view>
        </view>
      </view>
    </block>

    <!-- 我推荐的顾客列表 -->
    <block wx:if="{{activeTab === 1}}">
      <view wx:if="{{customerReferrals.length === 0 && !customerLoading}}" class="empty-state">
        <text class="empty-text">暂无推荐的顾客</text>
      </view>
      <view wx:else>
        <view wx:for="{{customerReferrals}}" wx:key="user_id" class="user-card" data-user-id="{{item.user_id}}" bindtap="viewUserDetail">
          <view class="user-avatar">
            <image src="{{item.avatar || '/images/profile.png'}}" mode="aspectFill" />
          </view>
          <view class="user-info">
            <view class="user-basic">
              <text class="user-nickname">{{item.nickname || '未设置昵称'}}</text>
              <text class="user-id">ID: {{item.user_id}}</text>
            </view>
            <view class="user-details">
              <text class="register-date">注册时间: {{item.register_date_formatted}}</text>
              <text class="total-consumption">累计消费: ¥{{item.total_consumption_formatted}}</text>
            </view>
          </view>
          <view class="user-arrow">></view>
        </view>
      </view>
    </block>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{(activeTab === 0 && partnerLoading) || (activeTab === 1 && customerLoading)}}" class="loading-state">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 没有更多数据提示 -->
  <view wx:if="{{(activeTab === 0 && !partnerHasMore && partnerReferrals.length > 0) || (activeTab === 1 && !customerHasMore && customerReferrals.length > 0)}}" class="no-more-state">
    <text class="no-more-text">没有更多数据了</text>
  </view>
</view>

<!-- 排序选择器弹窗（备用，当前已改为直接按钮切换） -->
<!-- <view wx:if="{{showSortPicker}}" class="sort-picker-overlay" bindtap="hideSortPicker">
  <view class="sort-picker" catchtap="">
    <view class="sort-picker-header">
      <text class="sort-picker-title">选择排序方式</text>
      <view class="sort-picker-close" bindtap="hideSortPicker">×</view>
    </view>
    <view class="sort-picker-content">
      <view wx:for="{{currentSortOptions}}" wx:key="key" class="sort-option" data-key="{{item.key}}" data-label="{{item.label}}" bindtap="selectSort">
        <text class="sort-option-text">{{item.label}}</text>
        <text wx:if="{{(activeTab === 0 && partnerSortBy === item.key) || (activeTab === 1 && customerSortBy === item.key)}}" class="sort-option-check">✓</text>
      </view>
    </view>
  </view>
</view> -->

<!-- 注释：【我的推荐】页不是tabbar页，不需要底部导航栏 -->
<!-- <partner-tabbar selected="{{4}}" /> -->