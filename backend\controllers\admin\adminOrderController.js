const db = require('../../config/db');
const OrderService = require('../../services/order/orderService');
const PaymentService = require('../../services/order/paymentService');
const InventoryService = require('../../services/order/inventoryService');
const FundAllocationService = require('../../services/order/fundAllocationService');

/**
 * 管理端订单控制器
 * 处理管理员相关的订单操作
 */
class AdminOrderController {
  /**
   * 获取所有订单列表（管理端）
   * GET /api/admin/orders
   */
  static async getAllOrders(req, res) {
    try {
      const { 
        category = 'all', 
        subcategory = 'all', 
        page = 1, 
        limit = 20,
        start_date,
        end_date,
        search_keyword
      } = req.query;
      
      const offset = (parseInt(page) - 1) * parseInt(limit);
      
      // 前端二级分类到数据库状态的映射
      const statusMap = OrderService.getStatusCodeMap();
      
      let orders = [];
      let total = 0;
      
      // 根据一级分类查询不同类型的订单
      if (category === 'express' || category === 'self' || category === 'all') {
        // 查询顾客订单（快递/自提）
        const customerOrders = await AdminOrderController.getCustomerOrders({
          category,
          subcategory,
          statusMap,
          offset,
          limit: parseInt(limit),
          start_date,
          end_date,
          search_keyword
        });
        orders = orders.concat(customerOrders.orders);
        total += customerOrders.total;
      }
      
      if (category === 'purchase' || category === 'all') {
        // 查询采购订单
        const purchaseOrders = await AdminOrderController.getPurchaseOrders({
          subcategory,
          statusMap,
          offset,
          limit: parseInt(limit),
          start_date,
          end_date,
          search_keyword
        });
        orders = orders.concat(purchaseOrders.orders);
        total += purchaseOrders.total;
      }
      
      if (category === 'transfer' || category === 'all') {
        // 查询移库订单
        const transferOrders = await AdminOrderController.getTransferOrders({
          subcategory,
          statusMap,
          offset,
          limit: parseInt(limit),
          start_date,
          end_date,
          search_keyword
        });
        orders = orders.concat(transferOrders.orders);
        total += transferOrders.total;
      }
      
      // 按创建时间排序
      orders.sort((a, b) => b.created_at - a.created_at);
      
      // 如果查询所有订单，需要重新分页
      if (category === 'all') {
        const startIndex = offset;
        const endIndex = offset + parseInt(limit);
        orders = orders.slice(startIndex, endIndex);
      }
      
      res.json({
        success: true,
        data: {
          orders,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / parseInt(limit))
          }
        }
      });
      
    } catch (error) {
      console.error('获取订单列表失败:', error);
      res.status(500).json({ success: false, message: '获取订单列表失败', error: error.message });
    }
  }

  /**
   * 获取顾客订单
   */
  static async getCustomerOrders(params) {
    const { category, subcategory, statusMap, offset, limit, start_date, end_date, search_keyword } = params;
    
    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const queryParams = [];
    
    // 配送方式筛选
    if (category === 'express') {
      whereClause += ' AND co.delivery_method = ?';
      queryParams.push('express');
    } else if (category === 'self') {
      whereClause += ' AND co.delivery_method = ?';
      queryParams.push('self');
    }
    
    // 状态筛选 - 前端现在直接发送中文状态
    if (subcategory !== 'all' && subcategory) {
      // 前端现在直接发送中文状态，无需转换
      whereClause += ' AND co.status = ?';
      queryParams.push(subcategory);
    }
    
    // 时间范围筛选
    if (start_date && end_date) {
      whereClause += ' AND co.created_at BETWEEN ? AND ?';
      queryParams.push(new Date(start_date).getTime(), new Date(end_date).getTime());
    }
    
    // 关键词搜索
    if (search_keyword) {
      whereClause += ' AND (co.order_no LIKE ? OR u.nickname LIKE ? OR u.phone LIKE ?)';
      const keyword = `%${search_keyword}%`;
      queryParams.push(keyword, keyword, keyword);
    }
    
    // 查询顾客订单
    const ordersQuery = `
      SELECT 
        co.*,
        u.nickname as user_nickname,
        u.phone as user_phone,
        u.avatar as user_avatar,
        s.nickname as salesman_nickname,
        s.phone as salesman_phone,
        st.name as store_name,
        st.store_no,

        'customer' as order_type,
        '顾客订单' as order_type_name
      FROM customer_orders co
      LEFT JOIN users u ON co.user_id = u.user_id
      LEFT JOIN users s ON co.salesman_id = s.user_id
      LEFT JOIN stores st ON co.store_no = st.store_no
      ${whereClause}
      ORDER BY co.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    queryParams.push(limit, offset);
    const orders = await db.query(ordersQuery, queryParams);
    
    // 查询总数
    const countQuery = `SELECT COUNT(*) as total FROM customer_orders co LEFT JOIN users u ON co.user_id = u.user_id ${whereClause}`;
    const countResult = await db.query(countQuery, queryParams.slice(0, -2));
    const total = countResult[0].total;
    
    // 查询每个订单的商品详情
    for (const order of orders) {
      const itemsQuery = `
        SELECT 
          coi.id,
          coi.product_id,
          coi.product_name,
          coi.product_image,
          coi.product_price as price,
          coi.quantity,
          coi.subtotal,
          p.images as product_images
        FROM customer_sub_orders cso
        LEFT JOIN customer_order_items coi ON cso.id = coi.sub_order_id
        LEFT JOIN products p ON coi.product_id = p.id
        WHERE cso.main_order_id = ?
      `;
      
      order.items = await db.query(itemsQuery, [order.id]);
      
      // 处理商品图片数据
      order.items.forEach(item => {
        // 优先使用订单商品表中的图片，如果没有则使用商品表中的图片
        let imageUrl = null;
        if (item.product_image) {
          try {
            const images = JSON.parse(item.product_image);
            imageUrl = Array.isArray(images) && images.length > 0 ? images[0] : item.product_image;
          } catch (e) {
            imageUrl = item.product_image;
          }
        } else if (item.product_images) {
          try {
            const images = JSON.parse(item.product_images);
            imageUrl = Array.isArray(images) && images.length > 0 ? images[0] : item.product_images;
          } catch (e) {
            imageUrl = item.product_images;
          }
        }
        item.product_image = imageUrl;
      });
      
      order.payment_methods_text = OrderService.getPaymentMethodText(order.payment_methods);
      order.created_at_formatted = OrderService.formatDate(order.created_at);
      order.updated_at_formatted = OrderService.formatDate(order.updated_at);
      
      // 处理门店信息显示逻辑
      if (!order.store_no || !order.store_name) {
        // 没有关联门店的订单归属平台总部
        order.store_type = 'platform';
        order.store_name = '平台总部';
        order.store_no = 'M100000001';
      } else {
        // 有关联门店的订单
        order.store_type = 'store';
      }
    }
    
    return { orders, total };
  }

  /**
   * 获取采购订单
   */
  static async getPurchaseOrders(params) {
    const { subcategory, statusMap, offset, limit, start_date, end_date, search_keyword } = params;
    
    // 构建查询条件
    let whereClause = 'WHERE so.type = "purchase"';
    const queryParams = [];
    
    // 状态筛选 - 前端现在直接发送中文状态
    if (subcategory !== 'all' && subcategory) {
      // 前端现在直接发送中文状态，无需转换
      whereClause += ' AND so.status = ?';
      queryParams.push(subcategory);
    }
    
    // 时间范围筛选
    if (start_date && end_date) {
      whereClause += ' AND so.created_at BETWEEN ? AND ?';
      queryParams.push(new Date(start_date).getTime(), new Date(end_date).getTime());
    }
    
    // 关键词搜索
    if (search_keyword) {
      whereClause += ' AND (so.order_no LIKE ? OR s.name LIKE ? OR u.nickname LIKE ?)';
      const keyword = `%${search_keyword}%`;
      queryParams.push(keyword, keyword, keyword);
    }

    // 查询采购订单
    const ordersQuery = `
      SELECT 
        so.*,
        s.name as store_name,
        s.level as store_level,
        p.name as product_name,
        p.images as product_image,
        p.store_price,
        p.purchase_price_l1,
        p.purchase_price_l2,
        p.purchase_price_l3,
        p.purchase_price_l4,
        p.purchase_price_l5,
        u.nickname as user_nickname,
        u.phone as user_phone,
        u.avatar as user_avatar,
        'purchase' as order_type,
        '采购订单' as order_type_name
      FROM store_orders so
      LEFT JOIN stores s ON so.store_no = s.store_no
      LEFT JOIN products p ON so.product_id = p.id
      LEFT JOIN users u ON so.user_id = u.user_id
      ${whereClause}
      ORDER BY so.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    queryParams.push(limit, offset);
    const orders = await db.query(ordersQuery, queryParams);
    
    // 查询总数
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM store_orders so 
      LEFT JOIN stores s ON so.store_no = s.store_no 
      LEFT JOIN products p ON so.product_id = p.id 
      LEFT JOIN users u ON so.user_id = u.user_id 
      ${whereClause}
    `;
    const countResult = await db.query(countQuery, queryParams.slice(0, -2));
    const total = countResult[0].total;
    
    // 格式化订单数据
    orders.forEach(order => {
      order.created_at_formatted = OrderService.formatDate(order.created_at);
      order.updated_at_formatted = OrderService.formatDate(order.updated_at);
      
      // 计算采购价格：根据门店级别获取对应的采购价
      if (order.store_level) {
        const purchasePriceField = `purchase_price_${order.store_level.toLowerCase()}`;
        // 如果对应级别的采购价存在且大于0，则使用该价格
        if (order[purchasePriceField] && order[purchasePriceField] > 0) {
          order.purchase_price = order[purchasePriceField];
        } else {
          // 否则使用门店基准价
          order.purchase_price = order.store_price;
        }
      } else {
        // 如果没有门店级别信息，使用门店基准价
        order.purchase_price = order.store_price;
      }
      
      // 重新计算订单金额（使用采购价）
      if (order.purchase_price && order.quantity) {
        order.total_amount = (order.purchase_price * order.quantity).toFixed(2);
      }
    });
    
    return { orders, total };
  }

  /**
   * 审核采购订单
   * POST /api/admin/orders/:orderId/approve
   */
  static async approveOrder(req, res) {
    const connection = await db.getConnection();
    
    try {
      const { orderId } = req.params;
      
      await connection.beginTransaction();
      
      // 查询采购订单详情（明确指定字段避免JOIN时的字段冲突）
      const orderQuery = `
        SELECT so.id, so.order_no, so.store_no, so.product_id, so.quantity, 
               so.total_amount, so.status as order_status, so.type, so.created_at, so.updated_at, so.user_id,
               s.name as store_name, p.name as product_name
        FROM store_orders so
        LEFT JOIN stores s ON so.store_no = s.store_no
        LEFT JOIN products p ON so.product_id = p.id
        WHERE so.id = ? AND so.type = 'purchase'
      `;
      
      const orders = await connection.queryHelper(orderQuery, [orderId]);
      
      if (orders.length === 0) {
        await connection.rollback();
        return res.status(404).json({
          success: false,
          message: '采购订单不存在'
        });
      }
      
      const order = orders[0];
      
      // 检查订单状态（兼容中文和英文状态）
      if (order.order_status !== '待审核') {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          message: '订单状态不允许审核操作，当前状态：' + order.order_status
        });
      }
      
      // 更新订单状态为已审核（使用中文状态保持一致性）
      const updateOrderQuery = `
        UPDATE store_orders 
        SET status = '已审核', updated_at = ? 
        WHERE id = ?
      `;
      
      await connection.queryHelper(updateOrderQuery, [Date.now(), orderId]);
      
      // 增加门店库存
      const checkInventoryQuery = `
        SELECT * FROM store_inventory 
        WHERE store_no = ? AND product_id = ?
      `;
      
      const inventoryResult = await connection.queryHelper(checkInventoryQuery, [order.store_no, order.product_id]);
      
      if (inventoryResult.length > 0) {
        // 更新现有库存
        const updateInventoryQuery = `
          UPDATE store_inventory 
          SET cloud_quantity = cloud_quantity + ?, update_time = ? 
          WHERE store_no = ? AND product_id = ?
        `;
        
        await connection.queryHelper(updateInventoryQuery, [
          order.quantity, 
          Date.now(), 
          order.store_no, 
          order.product_id
        ]);
      } else {
        // 创建新的库存记录
        const insertInventoryQuery = `
          INSERT INTO store_inventory (store_no, product_id, cloud_quantity, create_time, update_time)
          VALUES (?, ?, ?, ?, ?)
        `;
        
        await connection.queryHelper(insertInventoryQuery, [
          order.store_no,
          order.product_id,
          order.quantity,
          Date.now(),
          Date.now()
        ]);
      }
      
      await connection.commit();
      
      res.json({
        success: true,
        message: '采购订单审核成功，库存已更新'
      });
      
    } catch (error) {
      await connection.rollback();
      console.error('审核采购订单失败:', error);
      res.status(500).json({
        success: false,
        message: '审核采购订单失败',
        error: error.message
      });
    } finally {
      connection.release();
    }
  }

  /**
   * 拒绝采购订单
   * POST /api/admin/orders/:orderId/reject
   */
  static async rejectOrder(req, res) {
    const connection = await db.getConnection();
    
    try {
      const { orderId } = req.params;
      
      await connection.beginTransaction();
      
      // 查询采购订单详情（明确指定字段避免JOIN时的字段冲突）
      const orderQuery = `
        SELECT so.id, so.order_no, so.store_no, so.product_id, so.quantity, 
               so.total_amount, so.status as order_status, so.type, so.created_at, so.updated_at, so.user_id,
               s.name as store_name, p.name as product_name
        FROM store_orders so
        LEFT JOIN stores s ON so.store_no = s.store_no
        LEFT JOIN products p ON so.product_id = p.id
        WHERE so.id = ? AND so.type = 'purchase'
      `;
      
      const orders = await connection.queryHelper(orderQuery, [orderId]);
      
      if (orders.length === 0) {
        await connection.rollback();
        return res.status(404).json({
          success: false,
          message: '采购订单不存在'
        });
      }
      
      const order = orders[0];
      
      // 检查订单状态（兼容中文和英文状态）
      if (order.order_status !== '待审核') {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          message: '订单状态不允许拒绝操作，当前状态：' + order.order_status
        });
      }
      
      // 更新订单状态为已拒绝（使用中文状态保持一致性）
      const updateOrderQuery = `
        UPDATE store_orders 
        SET status = '已拒绝', updated_at = ? 
        WHERE id = ?
      `;
      
      await connection.queryHelper(updateOrderQuery, [Date.now(), orderId]);
      
      // 回滚门店支付的股本金
      // 查询门店资金账户
      const fundAccountQuery = `
        SELECT * FROM store_funds 
        WHERE store_no = ?
      `;
      
      const fundAccounts = await connection.queryHelper(fundAccountQuery, [order.store_no]);
      
      if (fundAccounts.length > 0) {
        // 增加门店股本金余额
        const updateFundQuery = `
          UPDATE store_funds 
          SET capital = capital + ?, update_time = ? 
          WHERE store_no = ?
        `;
        
        await connection.queryHelper(updateFundQuery, [
          order.total_amount,
          Date.now(),
          order.store_no
        ]);
        
        // 记录门店资金流水
        const insertRecordQuery = `
          INSERT INTO store_fund_records (
            store_no, type, account_type, amount, 
            description, created_at
          ) VALUES (?, ?, ?, ?, ?, ?)
        `;
        
        await connection.queryHelper(insertRecordQuery, [
          order.store_no,
          'refund',
          'capital',
          order.total_amount,
          `采购订单被拒绝，退回股本金 - 订单号: ${order.order_no}`,
          new Date()
        ]);
      }
      
      await connection.commit();
      
      res.json({
        success: true,
        message: '采购订单已拒绝，股本金已退回'
      });
      
    } catch (error) {
      await connection.rollback();
      console.error('拒绝采购订单失败:', error);
      res.status(500).json({
        success: false,
        message: '拒绝采购订单失败',
        error: error.message
      });
    } finally {
      connection.release();
    }
  }

  /**
   * 获取移库订单
   */
  static async getTransferOrders(params) {
    const { subcategory, statusMap, offset, limit, start_date, end_date, search_keyword } = params;
    
    // 构建查询条件
    let whereClause = 'WHERE so.type = "transfer"';
    const queryParams = [];
    
    // 状态筛选 - 前端现在直接发送中文状态
    if (subcategory !== 'all' && subcategory) {
      // 前端现在直接发送中文状态，无需转换
      whereClause += ' AND so.status = ?';
      queryParams.push(subcategory);
    }
    
    // 时间范围筛选
    if (start_date && end_date) {
      whereClause += ' AND so.created_at BETWEEN ? AND ?';
      queryParams.push(new Date(start_date).getTime(), new Date(end_date).getTime());
    }
    
    // 关键词搜索
    if (search_keyword) {
      whereClause += ' AND (so.order_no LIKE ? OR u.nickname LIKE ? OR s.name LIKE ?)';
      const keyword = `%${search_keyword}%`;
      queryParams.push(keyword, keyword, keyword);
    }
    
    // 查询移库订单
    const ordersQuery = `
      SELECT 
        so.*,
        u.nickname as user_nickname,
        u.phone as user_phone,
        s.name as store_name,
        p.name as product_name,
        p.images as product_image,
        'transfer' as order_type,
        '移库订单' as order_type_name
      FROM store_orders so
      LEFT JOIN users u ON so.user_id = u.user_id
      LEFT JOIN stores s ON so.store_no = s.store_no
      LEFT JOIN products p ON so.product_id = p.id
      ${whereClause}
      ORDER BY so.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    queryParams.push(limit, offset);
    const orders = await db.query(ordersQuery, queryParams);
    
    // 查询总数
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM store_orders so 
      LEFT JOIN users u ON so.user_id = u.user_id 
      LEFT JOIN stores s ON so.store_no = s.store_no 
      LEFT JOIN products p ON so.product_id = p.id 
      ${whereClause}
    `;
    const countResult = await db.query(countQuery, queryParams.slice(0, -2));
    const total = countResult[0].total;
    
    // 格式化订单数据
    orders.forEach(order => {
      order.created_at_formatted = OrderService.formatDate(order.created_at);
      order.updated_at_formatted = OrderService.formatDate(order.updated_at);
    });
    
    return { orders, total };
  }

  /**
   * 获取订单详情（管理端）
   * GET /api/admin/orders/:orderId
   */
  static async getOrderDetail(req, res) {
    try {
      const { orderId } = req.params;
      const { order_type } = req.query;
      
      let orderDetail = null;
      
      if (order_type === 'customer') {
        orderDetail = await AdminOrderController.getCustomerOrderDetail(orderId);
      } else if (order_type === 'purchase' || order_type === 'transfer') {
        orderDetail = await AdminOrderController.getStoreOrderDetail(orderId);
      } else {
        return res.status(400).json({ success: false, message: '无效的订单类型' });
      }
      
      if (!orderDetail) {
        return res.status(404).json({ success: false, message: '订单不存在' });
      }
      
      // 查询资金分配记录
      const fundAllocations = await FundAllocationService.getOrderFundAllocations(orderId);
      orderDetail.fund_allocations = fundAllocations;
      
      res.json({
        success: true,
        data: orderDetail
      });
      
    } catch (error) {
      console.error('获取订单详情失败:', error);
      res.status(500).json({ success: false, message: '获取订单详情失败', error: error.message });
    }
  }

  /**
   * 获取顾客订单详情
   */
  static async getCustomerOrderDetail(orderId) {
    // 查询主订单
    const orderQuery = `
      SELECT 
        co.*,
        u.nickname as user_nickname,
        u.phone as user_phone,
        u.email as user_email
      FROM customer_orders co
      LEFT JOIN users u ON co.user_id = u.user_id
      WHERE co.id = ?
    `;
    
    const orderResult = await db.query(orderQuery, [orderId]);
    if (orderResult.length === 0) {
      return null;
    }
    
    const order = orderResult[0];
    
    // 查询子订单和商品详情
    const subOrdersQuery = `
      SELECT 
        cso.*,
        s.name as store_name,
        s.address as store_address
      FROM customer_sub_orders cso
      LEFT JOIN stores s ON cso.store_no = s.store_no
      WHERE cso.main_order_id = ?
      ORDER BY cso.created_at
    `;
    
    const subOrders = await db.query(subOrdersQuery, [orderId]);
    
    // 查询每个子订单的商品详情
    for (const subOrder of subOrders) {
      const itemsQuery = `
        SELECT 
          coi.*,
          p.name as product_name,
          p.images as product_image,
          p.description as product_description
        FROM customer_order_items coi
        LEFT JOIN products p ON coi.product_id = p.id
        WHERE coi.sub_order_id = ?
      `;
      
      subOrder.items = await db.query(itemsQuery, [subOrder.id]);
    }
    
    // 查询地址信息
    let addressInfo = null;
    if (order.address_id) {
      const addressQuery = 'SELECT * FROM user_addresses WHERE id = ?';
      const addressResult = await db.query(addressQuery, [order.address_id]);
      if (addressResult.length > 0) {
        addressInfo = addressResult[0];
      }
    }
    
    return {
      ...order,
      order_type: 'customer',
      order_type_name: '顾客订单',
      payment_methods_text: OrderService.getPaymentMethodText(order.payment_methods),
      created_at_formatted: OrderService.formatDate(order.created_at),
      updated_at_formatted: OrderService.formatDate(order.updated_at),
      sub_orders: subOrders,
      address: addressInfo
    };
  }

  /**
   * 获取门店订单详情
   */
  static async getStoreOrderDetail(orderId) {
    const orderQuery = `
      SELECT 
        so.*,
        u.nickname as user_nickname,
        u.phone as user_phone,
        u.email as user_email,
        s.name as store_name,
        s.address as store_address,
        s.contact_phone as store_phone,
        p.name as product_name,
        p.images as product_image,
        p.description as product_description,
        p.price as product_price
      FROM store_orders so
      LEFT JOIN users u ON so.user_id = u.user_id
      LEFT JOIN stores s ON so.store_no = s.store_no
      LEFT JOIN products p ON so.product_id = p.id
      WHERE so.id = ?
    `;
    
    const orderResult = await db.query(orderQuery, [orderId]);
    if (orderResult.length === 0) {
      return null;
    }
    
    const order = orderResult[0];
    
    return {
      ...order,
      order_type: order.type,
      order_type_name: order.type === 'purchase' ? '采购订单' : '移库订单',
      created_at_formatted: OrderService.formatDate(order.created_at),
      updated_at_formatted: OrderService.formatDate(order.updated_at)
    };
  }

  /**
   * 更新订单状态（管理端）
   * PUT /api/admin/orders/:orderId/status
   */
  static async updateOrderStatus(req, res) {
    try {
      const { orderId } = req.params;
      const { order_type, status, remark } = req.body;
      
      if (!order_type || !status) {
        return res.status(400).json({ success: false, message: '参数错误' });
      }
      
      // 根据订单类型更新不同的表
      if (order_type === 'customer') {
        // 更新顾客订单状态
        await db.query('UPDATE customer_orders SET status = ?, remark = ?, updated_at = ? WHERE id = ?', 
          [status, remark || '', Date.now(), orderId]);
        
        // 同时更新子订单状态
        await db.query('UPDATE customer_sub_orders SET status = ?, updated_at = ? WHERE main_order_id = ?', 
          [status, Date.now(), orderId]);
        
        // 如果订单状态更新为已完成，处理销售分佣结算
        if (status === '已完成') {
          const FundAllocationService = require('../../services/order/fundAllocationService');
          await FundAllocationService.processOrderCompletion(orderId);
        }
      } else if (order_type === 'purchase' || order_type === 'transfer') {
        // 更新门店订单状态
        await db.query('UPDATE store_orders SET status = ?, remark = ?, updated_at = ? WHERE id = ?', 
          [status, remark || '', Date.now(), orderId]);
      } else {
        return res.status(400).json({ success: false, message: '无效的订单类型' });
      }
      
      res.json({
        success: true,
        message: '订单状态更新成功'
      });
      
    } catch (error) {
      console.error('更新订单状态失败:', error);
      res.status(500).json({ success: false, message: '更新订单状态失败', error: error.message });
    }
  }

  /**
   * 处理退款申请（管理端）
   * POST /api/admin/orders/:orderId/refund
   */
  static async processRefund(req, res) {
    try {
      const { orderId } = req.params;
      const { order_type, action, remark } = req.body; // action: 'approve' | 'reject'
      
      if (!order_type || !action) {
        return res.status(400).json({ success: false, message: '参数错误' });
      }
      
      // 查询退款申请
      const refundQuery = 'SELECT * FROM order_refunds WHERE order_id = ? AND order_type = ?';
      const refundResult = await db.query(refundQuery, [orderId, order_type]);
      
      if (refundResult.length === 0) {
        return res.status(404).json({ success: false, message: '退款申请不存在' });
      }
      
      const refund = refundResult[0];
      
      if (refund.status !== '待审核') {
        return res.status(400).json({ success: false, message: '退款申请已处理' });
      }
      
      const connection = await db.getConnection();
      try {
        await connection.beginTransaction();
        
        if (action === 'approve') {
          // 同意退款
          await connection.execute('UPDATE order_refunds SET status = ?, remark = ?, updated_at = ? WHERE id = ?', 
            ['已同意', remark || '', Date.now(), refund.id]);
          
          // 退款到用户余额
          await connection.execute(
            'UPDATE user_fund_accounts SET account_balance = account_balance + ?, updated_at = ? WHERE user_id = ?',
            [refund.amount, Date.now(), refund.user_id]
          );
          
          // 添加资金变动记录
          await connection.execute(
            'INSERT INTO user_fund_records (user_id, type, amount, balance, description, order_id, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
            [refund.user_id, 'refund', refund.amount, 0, '订单退款', orderId, '已完成', Date.now()]
          );
          
          // 更新订单状态
          if (order_type === 'customer') {
            await connection.execute('UPDATE customer_orders SET status = ?, updated_at = ? WHERE id = ?', 
              ['已取消', Date.now(), orderId]);
          } else {
            await connection.execute('UPDATE store_orders SET status = ?, updated_at = ? WHERE id = ?', 
              ['已取消', Date.now(), orderId]);
          }
          
        } else {
          // 拒绝退款
          await connection.execute('UPDATE order_refunds SET status = ?, remark = ?, updated_at = ? WHERE id = ?', 
            ['已拒绝', remark || '', Date.now(), refund.id]);
        }
        
        await connection.commit();
        
        res.json({
          success: true,
          message: action === 'approve' ? '退款已同意' : '退款已拒绝'
        });
        
      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }
      
    } catch (error) {
      console.error('处理退款申请失败:', error);
      res.status(500).json({ success: false, message: '处理退款申请失败', error: error.message });
    }
  }

  /**
   * 获取订单统计数据
   * GET /api/admin/orders/statistics
   */
  static async getOrderStatistics(req, res) {
    try {
      const { start_date, end_date } = req.query;
      
      let dateCondition = '';
      const params = [];
      
      if (start_date && end_date) {
        dateCondition = 'WHERE created_at BETWEEN ? AND ?';
        params.push(new Date(start_date).getTime(), new Date(end_date).getTime());
      }
      
      // 顾客订单统计
      const customerStatsQuery = `
        SELECT 
          COUNT(*) as total_count,
          SUM(total_amount) as total_amount,
          status,
          delivery_method
        FROM customer_orders 
        ${dateCondition}
        GROUP BY status, delivery_method
      `;
      
      const customerStats = await db.query(customerStatsQuery, params);
      
      // 门店订单统计
      const storeStatsQuery = `
        SELECT 
          COUNT(*) as total_count,
          SUM(total_amount) as total_amount,
          status,
          type
        FROM store_orders 
        ${dateCondition}
        GROUP BY status, type
      `;
      
      const storeStats = await db.query(storeStatsQuery, params);
      
      // 资金分配统计
      const fundStatsQuery = `
        SELECT 
          allocation_type,
          SUM(amount) as total_amount,
          COUNT(*) as count
        FROM fund_allocations 
        ${dateCondition.replace('created_at', 'fund_allocations.created_at')}
        GROUP BY allocation_type
      `;
      
      const fundStats = await db.query(fundStatsQuery, params);
      
      res.json({
        success: true,
        data: {
          customer_orders: customerStats,
          store_orders: storeStats,
          fund_allocations: fundStats
        }
      });
      
    } catch (error) {
      console.error('获取订单统计失败:', error);
      res.status(500).json({ success: false, message: '获取统计数据失败', error: error.message });
    }
  }

  /**
   * 发货订单
   */
  static async shipOrder(req, res) {
    const { orderId } = req.params;
    const { tracking_number, shipping_company } = req.body;
    

    
    if (!orderId) {
      return res.status(400).json({
        success: false,
        message: '订单ID不能为空'
      });
    }
    
    const connection = await db.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // 查询订单信息
      const [order] = await connection.queryHelper(
        'SELECT * FROM customer_orders WHERE id = ? FOR UPDATE',
        [orderId]
      );
      
      if (!order) {
        await connection.rollback();
        return res.status(404).json({
          success: false,
          message: '订单不存在'
        });
      }
      
      // 检查订单状态，只有待发货的订单才能发货
      if (order.status !== '待发货') {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          message: `订单状态为${order.status}，无法发货`
        });
      }
      
      // 检查配送方式，只有快递订单才能发货
      if (order.delivery_method !== 'express') {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          message: '只有快递订单才能发货'
        });
      }
      
      // 更新订单状态为已发货
      const updateData = {
        status: '已发货',
        shipped_at: Date.now(),
        updated_at: Date.now()
      };
      
      // 如果提供了物流信息，一并更新
      if (tracking_number) {
        updateData.tracking_number = tracking_number;
      }
      if (shipping_company) {
        updateData.shipping_company = shipping_company;
      }
      
      const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
      const updateValues = Object.values(updateData);
      updateValues.push(orderId);
      
      await connection.queryHelper(
        `UPDATE customer_orders SET ${updateFields} WHERE id = ?`,
        updateValues
      );

      // 同时更新所有子订单的状态和物流信息
      const subOrderUpdateData = {
        status: '已发货',
        shipped_at: Date.now(),
        updated_at: Date.now()
      };
      
      if (tracking_number) {
        subOrderUpdateData.tracking_number = tracking_number;
      }
      if (shipping_company) {
        subOrderUpdateData.shipping_company = shipping_company;
      }
      
      const subOrderUpdateFields = Object.keys(subOrderUpdateData).map(key => `${key} = ?`).join(', ');
      const subOrderUpdateValues = Object.values(subOrderUpdateData);
      subOrderUpdateValues.push(orderId);
      
      await connection.queryHelper(
        `UPDATE customer_sub_orders SET ${subOrderUpdateFields} WHERE main_order_id = ?`,
        subOrderUpdateValues
      );

      await connection.commit();
      
      res.json({
        success: true,
        message: '订单发货成功',
        data: {
          orderId,
          status: '已发货',
          shipped_at: updateData.shipped_at,
          tracking_number: tracking_number || null,
          shipping_company: shipping_company || null
        }
      });
      
    } catch (error) {
      await connection.rollback();
      console.error('发货订单失败:', error);
      res.status(500).json({
        success: false,
        message: '发货失败',
        error: error.message
      });
    } finally {
      connection.release();
    }
  }

  /**
   * 管理端确认收货（替代顾客操作）
   */
  static async confirmReceipt(req, res) {
    const { orderId } = req.params;
    

    
    if (!orderId) {
      return res.status(400).json({
        success: false,
        message: '订单ID不能为空'
      });
    }
    
    const connection = await db.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // 查询订单信息
      const [order] = await connection.queryHelper(
        'SELECT * FROM customer_orders WHERE id = ? FOR UPDATE',
        [orderId]
      );
      
      if (!order) {
        await connection.rollback();
        return res.status(404).json({
          success: false,
          message: '订单不存在'
        });
      }
      
      // 检查订单状态，只有已发货/待收货的订单才能确认收货
      if (order.status !== '已发货' && order.status !== '待收货') {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          message: `订单状态为${order.status}，无法确认收货`
        });
      }
      
      // 更新订单状态为已完成
      await connection.queryHelper(
        'UPDATE customer_orders SET status = ?, completed_at = ?, updated_at = ? WHERE id = ?',
        ['已完成', Date.now(), Date.now(), orderId]
      );
      
      // 更新子订单状态为已完成
      await connection.queryHelper(
        'UPDATE customer_sub_orders SET status = ?, updated_at = ? WHERE main_order_id = ?',
        ['已完成', Date.now(), orderId]
      );
      
      // 处理订单完成 - 将待结分佣转入账户余额
      await FundAllocationService.processOrderCompletion(orderId);
      
      await connection.commit();
      
      res.json({
        success: true,
        message: '确认收货成功',
        data: {
          orderId,
          status: '已完成',
          completed_at: Date.now()
        }
      });
      
    } catch (error) {
      await connection.rollback();
      console.error('管理端确认收货失败:', error);
      res.status(500).json({
        success: false,
        message: '确认收货失败',
        error: error.message
      });
    } finally {
      connection.release();
    }
  }
}

module.exports = AdminOrderController;