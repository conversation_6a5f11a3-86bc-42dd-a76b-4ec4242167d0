const loginStateManager = require('../../../utils/login-state-manager');

Page({
  data: {
    quickAccessEnabled: false // 快捷通道开关状态
  },

  onLoad() {
    console.log('账户设置页面 onLoad');
    this.loadQuickAccessSetting();
  },

  onShow() {
    console.log('账户设置页面 onShow');
  },

  // 加载快捷通道设置
  loadQuickAccessSetting() {
    const quickAccessEnabled = wx.getStorageSync('partnerQuickAccess') || false;
    this.setData({
      quickAccessEnabled: quickAccessEnabled
    });
    console.log('加载快捷通道设置:', quickAccessEnabled);
  },

  // 快捷通道开关变化处理
  onQuickAccessChange(e) {
    const enabled = e.detail.value;
    console.log('快捷通道开关变化:', enabled);
    
    // 保存设置到本地存储
    wx.setStorageSync('partnerQuickAccess', enabled);
    
    this.setData({
      quickAccessEnabled: enabled
    });
    
    // 显示提示信息
    wx.showToast({
      title: enabled ? '已开启快捷通道' : '已关闭快捷通道',
      icon: 'success',
      duration: 2000
    });
  },



  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除登录状态
          loginStateManager.clearLoginState();
          wx.removeStorageSync('userInfo');
          
          // 清除全局数据
          const app = getApp();
          if (app.globalData) {
            app.globalData.userInfo = null;
            app.globalData.isLogin = false;
          }
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
          
          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      }
    });
  }
});