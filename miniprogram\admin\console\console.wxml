<view class="console-container">
  <!-- 顶部信息栏 -->
  <view class="console-header">
    <view class="user-info-row">
      <image class="avatar-large" src="{{userInfo.avatar || userInfo.avatarUrl || '/images/icons2/默认头像.png'}}" />
      <view class="user-info-content user-info-flex">
        <view class="user-info-main-row user-info-align-center">
          <view class="user-name">{{userInfo.nickname || userInfo.nickName || '未登录'}}</view>
          <view class="switch-badge-group">
            <view class="vip-badge">
              <text>{{identityLabel || ''}}</text>
            </view>
            <image class="switch-role-icon" wx:if="{{showRoleSwitch}}" src="/images/icons2/switch-role.png" mode="aspectFit" bindtap="onGoSwitchLoginPage" />
          </view>
        </view>
        <view class="user-id user-id-bottom">ID：{{userInfo.id || userInfo._id || ''}}</view>
      </view>
    </view>
  </view>

  <!-- 统计卡片 -->
  <view class="stats-row">
    <view class="stat-item">
      <text class="stat-value">{{storeCount}}家</text>
      <text class="stat-label">已开门店</text>
    </view>
    <view class="stat-item">
      <text class="stat-value">{{partnerCount}}人</text>
      <text class="stat-label">合伙人数</text>
    </view>
    <view class="stat-item">
      <text class="stat-value">{{userCount}}人</text>
      <text class="stat-label">累计用户</text>
    </view>
    <view class="stat-item">
      <text class="stat-value">{{totalSales}}元</text>
      <text class="stat-label">累计销售</text>
    </view>
  </view>

  <!-- 订单管理 -->
  <view class="section">
    <view class="section-title-row">
      <text class="section-title">订单管理</text>
      <!-- <view class="section-more">
        <text>全部订单</text>
        <text class="arrow">&gt;</text>
      </view> -->
    </view>
    <view class="order-row">
      <view class="order-item" data-key="kuaidi" bindtap="goToOrders">
        <image class="order-icon" src="/images/icons2/顾客快递.png" />
        <text class="order-label">顾客快递</text>
        <view class="badge" wx:if="{{kuaidiPending > 0}}">{{kuaidiPending}}</view>
      </view>
      <view class="order-item" data-key="ziti" bindtap="goToOrders">
        <image class="order-icon" src="/images/icons2/顾客自提.png" />
        <text class="order-label">顾客自提</text>
        <view class="badge" wx:if="{{zitiPending > 0}}">{{zitiPending}}</view>
      </view>
      <view class="order-item" data-key="caigou" bindtap="goToOrders">
        <image class="order-icon" src="/images/icons2/门店采购.png" />
        <text class="order-label">门店采购</text>
        <view class="badge" wx:if="{{caigouPending > 0}}">{{caigouPending}}</view>
      </view>
      <view class="order-item" data-key="yiku" bindtap="goToOrders">
        <image class="order-icon" src="/images/icons2/门店库存.png" />
        <text class="order-label">门店移库</text>
        <view class="badge" wx:if="{{yikuPending > 0}}">{{yikuPending}}</view>
      </view>
    </view>
  </view>

  <!-- 其他工具 -->
  <view class="section">
    <view class="section-title">其他工具</view>
    <view class="tools-row">
      <view class="tool-item" bindtap="goToPartnerApplications">
        <image class="tool-icon" src="/images/icons2/合伙人申请.png" />
        <text class="tool-label">合伙人申请</text>
        <view class="badge" wx:if="{{partnerApplicationPending > 0}}">{{partnerApplicationPending}}</view>
      </view>
      <view class="tool-item">
        <image class="tool-icon" src="/images/icons2/用户提现.png" />
        <text class="tool-label">用户提现</text>
        <view class="badge" wx:if="{{userWithdrawPending > 0}}">{{userWithdrawPending}}</view>
      </view>
      <view class="tool-item" bindtap="goToStoreFund">
        <image class="tool-icon" src="/images/icons2/门店资金管理.png" />
        <text class="tool-label">门店资金管理</text>
      </view>
      <view class="tool-item">
        <image class="tool-icon" src="/images/icons2/派发红包.png" />
        <text class="tool-label">派发红包</text>
      </view>
    </view>
    <view class="tools-row">
      <view class="tool-item" bindtap="goToPartnerJoin">
        <image class="tool-icon" src="/images/icons2/加入合伙人.png" />
        <text class="tool-label">加入合伙人</text>
      </view>
      <view class="tool-item">
        <image class="tool-icon" src="/images/icons2/客服通道.png" />
        <text class="tool-label">客服通道</text>
      </view>
      <view class="tool-item">
        <image class="tool-icon" src="/images/icons2/销售报表.png" />
        <text class="tool-label">销售报表</text>
      </view>
      <view class="tool-item">
        <image class="tool-icon" src="/images/icons2/账户设置.png" />
        <text class="tool-label">账户设置</text>
      </view>
    </view>
  </view>

  <!-- 平台费用/设置 -->
  <view class="section">
    <view class="platform-row">
      <view class="platform-item">
        <image class="platform-icon" src="/images/icons2/平台费用.png" />
        <text class="platform-label">平台费用</text>
      </view>
      <view class="platform-item">
        <image class="platform-icon" src="/images/icons2/平台设置.png" />
        <text class="platform-label">平台设置</text>
      </view>
    </view>
  </view>

  <!-- 占位 -->
  <view style="height: 120rpx;"></view>

  <admin-tabbar current="console" />
</view>