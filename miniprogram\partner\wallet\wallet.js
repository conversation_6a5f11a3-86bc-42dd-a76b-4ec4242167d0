// partner/wallet/wallet.js
// 合伙人端我的钱包页面

const { partnerApi, apiRequest } = require('../../utils/api');
const { formatDate } = require('../../utils/util');

Page({
  data: {
    loading: true,
    
    // 钱包统计数据
    walletStats: {
      balance: '0.00',              // 余额
      pending_commission: '0.00',   // 待结销售分佣
      total_commission: '0.00',     // 累计销售分佣
      total_dividend: '0.00',       // 累计分红
      total_withdraw: '0.00'        // 累计提现
    },
    
    // 标签栏配置
    activeTab: 'balance', // 当前选中的标签
    tabs: [
      { key: 'balance', name: '余额' },
      { key: 'commission', name: '分佣' },
      { key: 'dividend', name: '分红' },
      { key: 'withdraw', name: '提现' }
    ],
    
    // 明细记录
    records: [],
    loadingRecords: false,
    hasMoreRecords: true,
    page: 1,
    pageSize: 20,
    
    // 当前标签名称（用于空状态显示）
    currentTabName: '余额'
  },

  /**
   * 页面加载
   */
  onLoad: function(options) {
    console.log('合伙人钱包页面加载');
    this.checkLoginStatus();
  },

  /**
   * 页面显示
   */
  onShow: function() {
    // 页面显示时刷新数据
    if (!this.data.loading) {
      this.loadWalletData();
    }
  },

  /**
   * 检查登录状态
   */
  async checkLoginStatus() {
    try {
      const loginStateManager = require('../../utils/login-state-manager');
      const loginState = loginStateManager.getLoginState();
      
      if (!loginState || !loginState.token || !loginState.isLogin) {
        console.log('登录状态无效，需要重新登录');
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      // 验证登录状态是否过期
      const validationResult = await loginStateManager.validateLoginState();
      if (!validationResult.isValid) {
        console.log('登录状态验证失败:', validationResult.message);
        wx.showToast({
          title: validationResult.message || '登录已过期，请重新登录',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      console.log('登录状态验证通过，开始加载钱包数据');
      await this.loadWalletData();
    } catch (error) {
      console.error('检查登录状态失败:', error);
      wx.showToast({
        title: '登录状态异常',
        icon: 'none'
      });
    }
  },

  /**
   * 加载钱包数据
   */
  async loadWalletData() {
    try {
      this.setData({ loading: true });
      
      // 加载钱包统计数据
      await this.loadWalletStats();
      
      // 加载明细记录
      await this.loadRecords(true);
      
    } catch (error) {
      console.error('加载钱包数据失败:', error);
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载钱包统计数据
   */
  async loadWalletStats() {
    try {
      const response = await partnerApi.getPartnerWalletStats();
      
      if (response.success && response.data) {
        this.setData({
          walletStats: {
            balance: this.formatAmount(response.data.balance),
            pending_commission: this.formatAmount(response.data.pending_commission),
            total_commission: this.formatAmount(response.data.total_commission),
            total_dividend: this.formatAmount(response.data.total_dividend),
            total_withdraw: this.formatAmount(response.data.total_withdraw)
          }
        });
      }
    } catch (error) {
      console.error('加载钱包统计数据失败:', error);
      throw error;
    }
  },

  /**
   * 加载明细记录
   */
  async loadRecords(refresh = false) {
    if (refresh) {
      this.setData({
        records: [],
        page: 1,
        hasMoreRecords: true
      });
    }

    if (!this.data.hasMoreRecords || this.data.loadingRecords) {
      return;
    }

    try {
      this.setData({ loadingRecords: true });
      
      const offset = (this.data.page - 1) * this.data.pageSize;
      const response = await partnerApi.getPartnerWalletRecords({
        type: this.data.activeTab,
        limit: this.data.pageSize,
        offset: offset
      });

      if (response.success && response.data) {
        const newRecords = response.data.map(record => ({
          ...record,
          created_at_formatted: this.formatTime(record.created_at),
          amount: this.formatAmount(record.amount),
          balance_after: this.formatAmount(record.balance_after)
        }));

        const allRecords = refresh ? newRecords : [...this.data.records, ...newRecords];
        
        this.setData({
          records: allRecords,
          hasMoreRecords: newRecords.length === this.data.pageSize,
          page: this.data.page + 1
        });
      }
    } catch (error) {
      console.error('加载明细记录失败:', error);
      wx.showToast({
        title: '加载记录失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loadingRecords: false });
    }
  },

  /**
   * 切换标签
   */
  switchTab: function(e) {
    const tabKey = e.currentTarget.dataset.key;
    const currentTab = this.data.tabs.find(tab => tab.key === tabKey);
    
    this.setData({
      activeTab: tabKey,
      currentTabName: currentTab ? currentTab.name : '记录'
    });
    
    // 重新加载记录
    this.loadRecords(true);
  },

  /**
   * 卡片点击事件
   */
  onCardTap: function(e) {
    const type = e.currentTarget.dataset.type;
    
    // 根据卡片类型切换到对应的标签
    let targetTab = 'balance';
    switch(type) {
      case 'balance':
        targetTab = 'balance';
        break;
      case 'pending_commission':
      case 'total_commission':
        targetTab = 'commission';
        break;
      case 'total_dividend':
        targetTab = 'dividend';
        break;
      case 'total_withdraw':
        targetTab = 'withdraw';
        break;
    }
    
    const currentTab = this.data.tabs.find(tab => tab.key === targetTab);
    
    this.setData({
      activeTab: targetTab,
      currentTabName: currentTab ? currentTab.name : '记录'
    });
    
    // 重新加载记录
    this.loadRecords(true);
  },

  /**
   * 申请提现
   */
  onWithdraw: function() {
    // 检查余额
    const balance = parseFloat(this.data.walletStats.balance);
    if (balance <= 0) {
      wx.showToast({
        title: '余额不足，无法提现',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '申请提现',
      content: '功能暂未开放',
      showCancel: false
    });
  },

  /**
   * 账户充值
   */
  onRecharge: function() {
    wx.showModal({
      title: '账户充值',
      content: '功能暂未开放',
      showCancel: false
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.loadWalletData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function() {
    this.loadRecords();
  },

  /**
   * 格式化金额
   */
  formatAmount: function(amount) {
    return parseFloat(amount || 0).toFixed(2);
  },

  /**
   * 格式化时间
   */
  formatTime: function(timeStr) {
    if (!timeStr) return '';
    
    try {
      const date = new Date(timeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hour = String(date.getHours()).padStart(2, '0');
      const minute = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hour}:${minute}`;
    } catch (error) {
      console.error('时间格式化失败:', error);
      return timeStr;
    }
  }
});