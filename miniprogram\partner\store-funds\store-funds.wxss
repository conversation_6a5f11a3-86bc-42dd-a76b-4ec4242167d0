/* partner/store-funds/store-funds.wxss */
/* 合伙人端门店资金页面样式 */

.store-funds-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 20rpx;
}

/* 门店选择区域 */
.store-select-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.store-select-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.store-select-container {
  flex: 1;
  margin-left: 30rpx;
}

.store-picker {
  width: 100%;
}

.store-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.store-name {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.dropdown-arrow {
  font-size: 24rpx;
  color: #666;
  margin-left: 20rpx;
}

/* 门店账户统计数据 */
.fund-stats-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.stats-header {
  margin-bottom: 30rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.stats-item {
  flex: 1;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 0 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stats-item:active {
  transform: scale(0.98);
  box-shadow: 0 0 8rpx rgba(0, 0, 0, 0.15);
}

.stats-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.stats-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #1890ff;
}

/* 资金记录标签栏 */
.record-tabs-section {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.tabs-container {
  display: flex;
  border-bottom: 2rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: color 0.3s ease;
}

.tab-item.active {
  color: #007aff;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #007aff;
  border-radius: 2rpx;
}

/* 资金记录列表 */
.records-section {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.records-list {
  padding: 0 30rpx;
}

.record-item {
  padding: 30rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.record-voucher {
  font-size: 26rpx;
  color: #007aff;
  font-weight: 500;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.record-description {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
}

.record-amount {
  font-size: 30rpx;
  font-weight: 600;
}

.record-amount.positive {
  color: #52c41a;
}

.record-amount.negative {
  color: #ff4d4f;
}

.record-balance {
  display: flex;
  align-items: center;
}

.balance-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
}

.balance-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 30rpx;
}

.load-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 没有更多 */
.no-more {
  text-align: center;
  padding: 40rpx 30rpx;
}

.no-more-text {
  font-size: 26rpx;
  color: #ccc;
}

/* 底部间距 */
.bottom-spacing {
  height: 40rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .stats-grid {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .stats-item {
    padding: 25rpx 15rpx;
  }
  
  .tabs-container {
    flex-wrap: wrap;
  }
  
  .tab-item {
    min-width: 25%;
    padding: 25rpx 15rpx;
  }
}