const { groupApi } = require('../../utils/api');
const { formatTime } = require('../../utils/util');

Page({
  data: {
    groupId: '',
    group: {
      name: '',
      description: '',
      avatar: '',
      cover: '/images/icons/group-default.png',
      memberCount: 0,
      createTime: '',
      creatorInfo: null,
      announcement: ''
    },
    members: [],
    displayMembers: [], // 用于显示的成员列表
    isMember: false,
    isManager: false,
    isLoading: true,
    showAllMembers: false,
    // 封面相关
    coverLocalPath: '', // 裁剪后的本地图片路径
    coverUrl: '' // 当前显示的封面URL
  },

  onLoad: function(options) {
    const { groupId } = options || {};

    if (!groupId) {
      wx.showToast({
        title: '缺少群组ID',
        icon: 'none'
      });
      setTimeout(() => wx.navigateBack(), 1500);
      return;
    }

    this.setData({ groupId });
    this.loadGroupDetail();
  },

  onShow: function() {
    if (this.data.groupId) {
      this.loadGroupDetail();
    }

    // 检查是否有新的裁剪后的封面图片
    if (this.data.coverLocalPath && this.data.coverUrl === this.data.coverLocalPath) {
      // 有新的裁剪图片，先显示缩略图，然后上传
      this.setData({
        'group.avatar': this.data.coverLocalPath // 先显示本地裁剪后的图片
      });
      
      // 上传图片
      this.uploadCoverImage(this.data.coverLocalPath);
    }
  },

  // 上传封面图片
  uploadCoverImage: async function(tempFilePath) {
    wx.showLoading({
      title: '上传中...'
    });

    try {
      // 使用微信云存储上传图片
      const { uploadFile } = require('../../utils/api');
      const fileID = await uploadFile(tempFilePath);

      console.log('图片上传成功:', fileID);

      // 更新群组封面
      await this.updateGroupInfo({ avatar: fileID });
      
      // 上传成功后更新为服务器路径
      this.setData({
        'group.avatar': fileID,
        coverLocalPath: '',
        coverUrl: ''
      });
      
      wx.hideLoading();
      wx.showToast({
        title: '封面更新成功',
        icon: 'success'
      });

    } catch (error) {
      wx.hideLoading();
      console.error('上传图片失败:', error);
      
      // 上传失败时恢复原来的封面
      this.setData({
        'group.avatar': this.data.group.avatar || '/images/icons/group-default.png',
        coverLocalPath: '',
        coverUrl: ''
      });
      
      wx.showToast({
        title: '上传失败',
        icon: 'none'
      });
    }
  },

  // 预览图片
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    if (!url || url.includes('default')) return;

    wx.previewImage({
      urls: [url],
      current: url
    });
  },

  // 显示群组设置
  showGroupSettings: function() {
    wx.showActionSheet({
      itemList: [
        '群组二维码',
        '群公告',
        '退出群组'
      ],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.showGroupQRCode();
            break;
          case 1:
            this.editGroupAnnouncement();
            break;
          case 2:
            this.onLeaveGroup();
            break;
        }
      }
    });
  },

  // 显示群组二维码
  showGroupQRCode: function() {
    // 待实现
    wx.showToast({
      title: '暂不支持此功能',
      icon: 'none'
    });
  },

  // 编辑群公告
  editGroupAnnouncement: function() {
    // 待实现
    wx.showToast({
      title: '暂不支持此功能',
      icon: 'none'
    });
  },

  onAddMember: function() {
    if (!this.data.isManager) {
      wx.showToast({
        title: '没有权限',
        icon: 'none'
      });
      return;
    }

    wx.showToast({
      title: '暂不支持此功能',
      icon: 'none'
    });
  },

  // 更换群封面
  onChangeCover: function() {
    if (!this.data.isManager) {
      wx.showToast({
        title: '只有群主可以更换群封面',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: res => {
        const tempFilePath = res.tempFilePaths[0];
        // 跳转到裁剪页面
        wx.navigateTo({
          url: `/partner/messages/cropper/cropper?src=${encodeURIComponent(tempFilePath)}`
        });
      }
    });
  },

  // 编辑群名称
  onEditGroupName: function() {
    if (!this.data.isManager) {
      wx.showToast({
        title: '只有群主可以修改群名称',
        icon: 'none'
      });
      return;
    }

    const currentName = this.data.group.name || '';

    wx.showModal({
      title: '修改群名称',
      editable: true,
      placeholderText: '请输入群名称...',
      content: currentName,
      confirmText: '保存',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          const newName = res.content.trim();
          if (newName.length === 0) {
            wx.showToast({
              title: '群名称不能为空',
              icon: 'none'
            });
            return;
          }
          if (newName.length > 20) {
            wx.showToast({
              title: '群名称不能超过20个字符',
              icon: 'none'
            });
            return;
          }
          this.updateGroupInfo({ name: newName });
        }
      }
    });
  },

  // 编辑群简介
  onEditGroupDesc: function() {
    if (!this.data.isManager) {
      wx.showToast({
        title: '只有群主可以修改群简介',
        icon: 'none'
      });
      return;
    }

    const currentDesc = this.data.group.description || '';

    wx.showModal({
      title: '修改群简介',
      editable: true,
      placeholderText: '请输入群简介...',
      content: currentDesc,
      confirmText: '保存',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          const newDesc = res.content.trim();
          if (newDesc.length > 100) {
            wx.showToast({
              title: '群简介不能超过100个字符',
              icon: 'none'
            });
            return;
          }
          this.updateGroupInfo({ description: newDesc });
        }
      }
    });
  },

  // 编辑群公告
  onEditAnnouncement: function() {
    if (!this.data.isManager) {
      wx.showToast({
        title: '只有群主可以编辑群公告',
        icon: 'none'
      });
      return;
    }

    const currentAnnouncement = this.data.group.announcement || '';

    wx.showModal({
      title: '编辑群公告',
      editable: true,
      placeholderText: '请输入群公告内容...',
      content: currentAnnouncement,
      confirmText: '保存',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          const newAnnouncement = res.content.trim();
          this.updateAnnouncement(newAnnouncement);
        }
      }
    });
  },

  // 更新群组信息
  updateGroupInfo: function(updateData) {
    wx.showLoading({
      title: '保存中...'
    });

    groupApi.updateGroupInfo(this.data.groupId, updateData)
      .then(result => {
        wx.hideLoading();
        if (result.success) {
          // 更新本地数据
          const updateObj = {};
          Object.keys(updateData).forEach(key => {
            if (key === 'avatar') {
              // 封面图片需要同时更新cover字段
              updateObj[`group.cover`] = updateData[key];
              updateObj[`group.avatar`] = updateData[key];
            } else {
              updateObj[`group.${key}`] = updateData[key];
            }
          });
          this.setData(updateObj);

          let updateType = '群信息';
          if (updateData.name) updateType = '群名称';
          else if (updateData.description) updateType = '群简介';
          else if (updateData.avatar) updateType = '群封面';

          wx.showToast({
            title: `${updateType}已更新`,
            icon: 'success'
          });
        } else {
          throw new Error(result.message || '更新失败');
        }
      })
      .catch(error => {
        wx.hideLoading();
        console.error('更新群组信息失败:', error);
        wx.showToast({
          title: error.message || '更新失败',
          icon: 'none'
        });
      });
  },

  // 更新群公告
  updateAnnouncement: function(announcement) {
    wx.showLoading({
      title: '保存中...'
    });

    groupApi.updateAnnouncement(this.data.groupId, announcement)
      .then(result => {
        wx.hideLoading();
        if (result.success) {
          // 更新本地数据
          this.setData({
            'group.announcement': announcement
          });

          wx.showToast({
            title: '群公告已更新',
            icon: 'success'
          });
        } else {
          throw new Error(result.message || '更新失败');
        }
      })
      .catch(error => {
        wx.hideLoading();
        console.error('更新群公告失败:', error);
        wx.showToast({
          title: error.message || '更新失败',
          icon: 'none'
        });
      });
  },

  // 切换显示所有成员
  toggleShowAllMembers: function() {
    const showAllMembers = !this.data.showAllMembers;
    this.setData({
      showAllMembers: showAllMembers
    });
    this.updateDisplayMembers();
  },

  // 更新显示的成员列表
  updateDisplayMembers: function() {
    const { members, showAllMembers } = this.data;
    const displayMembers = showAllMembers ? members : members.slice(0, 6);
    this.setData({
      displayMembers: displayMembers
    });
  },

  loadGroupDetail: function() {
    if (!this.data.groupId) return;

    wx.showLoading({ title: '加载中...' });

    groupApi.getGroupDetail(this.data.groupId)
      .then(res => {
        if (!res || !res.success || !res.data) {
          throw new Error('获取群组详情失败');
        }

        const groupData = res.data;
        const currentUser = getApp().globalData.userInfo;

        // 格式化创建时间
        const createTime = groupData.createTime ? formatTime(new Date(groupData.createTime)) : '';

        // 处理默认图片
        const cover = groupData.cover || '/images/icons/group-default.png';
        const avatar = groupData.avatar || '/images/icons/group-default.png';

        // 判断用户权限
        const isManager = currentUser && (
          groupData.creatorId === currentUser.id ||
          groupData.adminIds?.includes(currentUser.id)
        );

        // 确保创建者信息正确显示
        let creatorName = '未知用户';
        if (groupData.creator) {
          creatorName = groupData.creator;
        } else if (groupData.creatorInfo && groupData.creatorInfo.nickname) {
          creatorName = groupData.creatorInfo.nickname;
        }

        this.setData({
          group: {
            ...groupData,
            cover,
            avatar,
            createTime,
            creator: creatorName
          },
          isManager,
          isLoading: false
        });

        return this.loadGroupMembers();
      })
      .catch(err => {
        console.error('加载群组详情失败:', err);
        wx.showToast({
          title: err.message || '加载失败',
          icon: 'none'
        });
        this.setData({
          isLoading: false
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  loadGroupMembers: function() {
    if (!this.data.groupId) {
      console.error('loadGroupMembers: groupId为空');
      return;
    }

    // 检查用户登录状态
    const app = getApp();
    const token = wx.getStorageSync('token');

    // 检查token是否存在
    if (!token) {
      console.error('未找到token，需要重新登录');
      wx.showModal({
        title: '登录失效',
        content: '请重新登录后查看群成员',
        showCancel: false,
        success: () => {
          wx.navigateTo({ url: '/pages/auth/auth' });
        }
      });
      return;
    }

    // 处理token错误的函数
    const handleTokenError = () => {
      wx.removeStorageSync('token');
      wx.removeStorageSync('userInfo');
      if (app && app.globalData) {
        app.globalData.isLogin = false;
        app.globalData.userInfo = null;
        app.globalData.token = null;
      }

      wx.showModal({
        title: '登录已过期',
        content: '请重新登录后查看群成员',
        showCancel: false,
        success: () => {
          wx.navigateTo({ url: '/pages/auth/auth' });
        }
      });
    };

    wx.showLoading({
      title: '加载成员中...',
      mask: true
    });

    groupApi.getGroupMembers(this.data.groupId)
      .then(res => {
        if (!res) {
          throw new Error('API响应为空');
        }

        if (!res.success) {
          throw new Error(res.message || '获取群成员列表失败');
        }

        if (!res.data) {
          this.setData({
            members: [],
            isMember: false,
            'group.memberCount': 0
          });
          return;
        }

        const members = Array.isArray(res.data) ? res.data : [];
        const currentUser = app.globalData.userInfo;

        // 处理成员信息
        const processedMembers = members.map(member => ({
          ...member,
          userId: member.userId || member.id,
          avatar: member.avatar || '/images/icons2/默认头像.png',
          nickname: member.nickname || '微信用户',
          role: member.role || 'member'
        }));

        // 按角色排序：群主 > 管理员 > 普通成员
        processedMembers.sort((a, b) => {
          if (a.role === 'owner') return -1;
          if (b.role === 'owner') return 1;
          if (a.role === 'admin' && b.role !== 'admin') return -1;
          if (b.role === 'admin' && a.role !== 'admin') return 1;
          return 0;
        });

        // 检查当前用户是否为群成员
        const isMember = currentUser ? processedMembers.some(m =>
          String(m.userId) === String(currentUser.id)
        ) : false;

        // 检查当前用户是否为管理员
        const currentUserMember = currentUser ? processedMembers.find(m =>
          String(m.userId) === String(currentUser.id)
        ) : null;
        const isManager = currentUserMember && (currentUserMember.role === 'owner' || currentUserMember.role === 'admin');

        this.setData({
          members: processedMembers,
          isMember: isMember,
          isManager: isManager,
          'group.memberCount': processedMembers.length
        });

        // 更新显示的成员列表
        this.updateDisplayMembers();
      })
      .catch(err => {
        console.error('加载群成员失败:', err);

        // 特殊处理"令牌信息为空或无效"错误
        if (err.message && err.message.includes('令牌信息为空或无效')) {
          wx.hideLoading();
          handleTokenError();
          return;
        }

        // 根据错误类型显示不同提示
        let errorMsg = '加载群成员失败';
        if (err.message && err.message.includes('401')) {
          errorMsg = '登录已过期，请重新登录';
          // 对于401错误，也清除登录状态
          handleTokenError();
          return;
        } else if (err.message && err.message.includes('403')) {
          errorMsg = '没有权限查看群成员';
        } else if (err.message && err.message.includes('404')) {
          errorMsg = '群组不存在';
        } else if (err.message) {
          errorMsg = err.message;
        }

        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        });

        // 设置空数据避免页面异常
        this.setData({
          members: [],
          isMember: false,
          isManager: false
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  onJoinGroup: function() {
    if (!this.data.groupId) return;

    wx.showLoading({ title: '加入中...' });

    groupApi.joinGroup(this.data.groupId)
      .then(() => {
        wx.showToast({
          title: '加入成功',
          icon: 'success'
        });
        this.loadGroupDetail();
      })
      .catch(err => {
        wx.showToast({
          title: err.message || '加入失败',
          icon: 'none'
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 添加成员
  onAddMember: function() {
    wx.showToast({
      title: '暂不支持此功能',
      icon: 'none'
    });
  },

  // 删除成员
  onDeleteMember: function() {
    wx.showToast({
      title: '暂不支持此功能',
      icon: 'none'
    });
  },

  onLeaveGroup: function() {
    const { group, currentUser } = this.data;

    // 检查是否是群主
    const isOwner = group.creatorId === currentUser?.id ||
                   this.data.members.some(m =>
                     m.userId === currentUser?.id && m.role === 'owner'
                   );

    if (isOwner) {
      wx.showModal({
        title: '提示',
        content: '您是群主，退出群聊前请先转让群主或解散群聊',
        confirmText: '去转让',
        success: (res) => {
          if (res.confirm) {
            // 群管理功能暂未实现
            wx.showToast({
              title: '群管理功能开发中',
              icon: 'none',
              duration: 2000
            });
          }
        }
      });
      return;
    }

    wx.showModal({
      title: '提示',
      content: '确定要退出该群聊吗？',
      confirmText: '退出',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          this.doLeaveGroup();
        }
      }
    });
  },

  doLeaveGroup: function() {
    wx.showLoading({
      title: '退出中...',
      mask: true
    });

    groupApi.leaveGroup(this.data.groupId)
      .then(() => {
        wx.showToast({
          title: '退出成功',
          icon: 'success',
          duration: 1500
        });
        // 返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      })
      .catch(err => {
        wx.showToast({
          title: err.message || '退出失败',
          icon: 'none'
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  onPullDownRefresh: function() {
    this.loadGroupDetail();
    setTimeout(() => wx.stopPullDownRefresh(), 500);
  },

  formatTime: function(timestamp) {
    if (!timestamp) return '';
    return formatTime(new Date(timestamp));
  },

  onShareAppMessage: function() {
    const { group } = this.data;
    return {
      title: group.name ? `邀请您加入${group.name}` : '群聊邀请',
      path: `/partner/messages/group-detail?groupId=${this.data.groupId}`,
      imageUrl: group.cover || '/images/icons/group-default.png'
    };
  }
});
