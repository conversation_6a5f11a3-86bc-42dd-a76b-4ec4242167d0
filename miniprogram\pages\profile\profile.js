// pages/profile/profile.js
const { userApi, vipApi, pointsApi, productApi } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager');
const request = require('../../utils/request');

Page({
  data: {
    userInfo: null,
    stats: {
      followCount: 125,
      followerCount: 23,
      favoriteCount: 0,
      points: 100,
      balance: 0,
      unpaidCount: 2,
      pendingPickupCount: 1,
      unreceivedCount: 0,
      unratedCount: 0,
      completedCount: 0
    },
    // 新增钱包数据
    wallet: {
      balance: 0,
      redPacket: 0,
      coupon: 0,
      points: 0
    },
    vipInfo: {
      level_code: 'free',
      level_name: '普通用户',
      expire_time: null
    },
    defaultStats: {
      followCount: 0,
      followerCount: 0,
      favoriteCount: 0,
      points: 0,
      balance: 0,
      unpaidCount: 0,
      pendingPickupCount: 0,
      unreceivedCount: 0,
      unratedCount: 0,
      commentCount: 0,
      promotionCount: 0,
      completedCount: 0
    },
    defaultWallet: {
      balance: 0,
      redPacket: 0,
      coupon: 0,
      points: 0
    },
    isLogin: false, // 默认为未登录状态
    loading: false,
    banners: [
      {
        id: 1,
        imageUrl: '/images/icons2/默认商品.png',
        linkType: 'page',
        linkUrl: '/pages/category/category?category=enterprise',
        title: '企业所得税汇算清缴专题'
      },
      {
        id: 2,
        imageUrl: '/images/icons2/默认商品.png',
        linkType: 'page',
        linkUrl: '/pages/category/category?category=platform',
        title: '知识产权保护与商标注册'
      },
      {
        id: 3,
        imageUrl: '/images/icons2/默认商品.png',
        linkType: 'page',
        linkUrl: '/pages/category/category?category=partner',
        title: '企业并购与资产重组'
      },
      {
        id: 4,
        imageUrl: '/images/icons2/默认商品.png',
        linkType: 'page',
        linkUrl: '/pages/category/category?category=peripheral',
        title: '公司注册与工商变更'
      }
    ],
    identityLabel: '普通用户', // 新增：身份标签，设置默认值
    userRoles: [], // 新增：所有身份
    currentRole: null, // 新增：当前端身份
    showRoleSwitch: false, // 新增：是否显示切换登录按钮
  },

  onLoad: function (options) {
    this.checkLoginStatus();
    this.getUserRoles(); // 优先加载多重身份
    this.getBanners(); // 获取轮播图数据
  },
  onShow: function () {
    // 检查登录状态是否发生变化
    const app = getApp();
    const globalData = app.globalData || {};
    // 减少日志输出：简化页面显示日志
    // console.log('个人中心页面显示，当前登录状态:', globalData.isLogin, '页面状态:', this.data.isLogin);
    // console.log('needRefreshProfile标志:', globalData.needRefreshProfile);
    
    // 防抖处理，避免频繁调用
    if (this.onShowTimer) {
      clearTimeout(this.onShowTimer);
    }
    
    this.onShowTimer = setTimeout(() => {
      // 每次显示页面时都检查登录状态，确保页面状态与全局状态一致
      this.checkLoginStatus();
      // 优先拉取多重身份
      this.getUserRoles();
      // 重置标记
      globalData.needRefreshProfile = false;
    }, 100); // 100ms防抖延迟
  },

  // 获取轮播图数据
  getBanners: function() {
    productApi.getBanners('customer_profile').then(res => {
      if (res.success && Array.isArray(res.data) && res.data.length > 0) {
        // 为轮播图数据添加默认字段
        const banners = res.data.map(banner => ({
          ...banner,
          imageUrl: banner.imageUrl || banner.image_url || '/images/icons2/默认商品.png',
          linkUrl: banner.linkUrl || banner.link_url || '',
          title: banner.title || '',
          linkType: banner.linkType || 'page' // 添加默认linkType字段，用于轮播图跳转判断
        }));
        this.setData({ banners });
      } else {
        // 使用默认轮播图数据
        this.setData({
          banners: [
            {
              id: 1,
              imageUrl: '/images/lunbo/001.jpeg',
              linkType: 'page',
              linkUrl: '/pages/category/category?category=enterprise',
              title: '企业所得税汇算清缴专题'
            },
            {
              id: 2,
              imageUrl: '/images/lunbo/002.jpg',
              linkType: 'page',
              linkUrl: '/pages/category/category?category=platform',
              title: '知识产权保护与商标注册'
            },
            {
              id: 3,
              imageUrl: '/images/lunbo/003.png',
              linkType: 'page',
              linkUrl: '/pages/category/category?category=partner',
              title: '企业并购与资产重组'
            },
            {
              id: 4,
              imageUrl: '/images/lunbo/001.jpeg',
              linkType: 'page',
              linkUrl: '/pages/category/category?category=peripheral',
              title: '公司注册与工商变更'
            }
          ]
        });
      }
    }).catch(err => {
      console.error('获取轮播图错误:', err);
      // 使用默认轮播图数据
      this.setData({
        banners: [
          {
            id: 1,
            imageUrl: '/images/lunbo/001.jpeg',
            linkType: 'page',
            linkUrl: '/pages/category/category?category=enterprise',
            title: '企业所得税汇算清缴专题'
          },
          {
            id: 2,
            imageUrl: '/images/lunbo/002.jpg',
            linkType: 'page',
            linkUrl: '/pages/category/category?category=platform',
            title: '知识产权保护与商标注册'
          },
          {
            id: 3,
            imageUrl: '/images/lunbo/003.png',
            linkType: 'page',
            linkUrl: '/pages/category/category?category=partner',
            title: '企业并购与资产重组'
          },
          {
            id: 4,
            imageUrl: '/images/lunbo/001.jpeg',
            linkType: 'page',
            linkUrl: '/pages/category/category?category=peripheral',
            title: '公司注册与工商变更'
          }
        ]
      });
    });
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const app = getApp();
    const globalData = app.globalData || {};
    // 减少日志输出：简化登录状态检查日志
    // console.log('个人中心页面检查登录状态');
    this.setData({ loading: true });

    // 首先检查全局状态
    if (globalData.isLogin && globalData.userInfo) {
      // 减少日志输出：简化全局状态日志
      // console.log('全局状态显示已登录，使用全局用户信息');

      // 更新页面数据
      this.setData({
        userInfo: globalData.userInfo,
        isLogin: true,
        loading: false,
        identityLabel: '普通用户' // 设置默认身份标签
      });

      // 获取用户统计数据
      this.getUserStats();
      
      // 获取用户身份信息
      this.getUserRoles();

      // 仍然验证登录状态，但不阻塞UI显示
      this.validateLoginStateInBackground();
      return;
    }

    // 尝试从本地存储获取登录状态
    const loginState = loginStateManager.getLoginState();
    const userInfo = wx.getStorageSync('userInfo');

    // 如果本地有登录状态，先使用本地状态快速显示UI
    if (loginState && loginState.isLogin && userInfo) {
      // 减少日志输出：简化本地状态日志
      // console.log('从本地存储找到登录状态，先使用本地状态');

      // 更新全局状态
      globalData.userInfo = userInfo;
      globalData.isLogin = true;

      // 更新页面状态
      this.setData({
        userInfo: userInfo,
        isLogin: true,
        loading: false,
        identityLabel: '普通用户' // 设置默认身份标签
      });

      // 获取用户统计数据
      this.getUserStats();
      
      // 获取用户身份信息
      this.getUserRoles();

      // 在后台验证登录状态
      this.validateLoginStateInBackground();
      return;
    }

    // 使用登录状态管理器验证登录状态
    loginStateManager.validateLoginState()
      .then(result => {
        // 减少日志输出：简化验证结果日志
        // console.log('个人中心页面登录状态验证结果:', result);

        if (result.isValid) {
          // 登录状态有效
          // 减少日志输出：简化有效状态日志
          // console.log('登录状态有效，用户信息:', result.userInfo);

          // 更新全局用户信息
          globalData.userInfo = result.userInfo;
          globalData.isLogin = true;          // 更新页面数据
          this.setData({
            userInfo: result.userInfo,
            isLogin: true,
            loading: false,
            identityLabel: '普通用户' // 设置默认身份标签
          });

          // 获取用户统计数据
          this.getUserStats();
          
          // 获取用户身份信息
          this.getUserRoles();
          
          // 获取用户VIP信息
          this.getUserVipInfo();
        } else {
          // 登录状态无效，但不立即清除，而是检查是否有本地状态可用
          console.warn('登录状态验证结果无效:', result.message);

          // 如果是由于网络问题导致的验证失败，尝试使用本地状态
          if (loginState && userInfo) {
            // 减少日志输出：简化本地状态尝试日志
            // console.log('尝试使用本地登录状态');

            // 更新全局状态
            globalData.userInfo = userInfo;
            globalData.isLogin = true;

            // 更新页面状态
            this.setData({
              userInfo: userInfo,
              isLogin: true,
              loading: false
            });

            return;
          }

          // 如果没有可用的本地状态，才清除登录状态
          console.warn('没有可用的本地状态，清除登录状态');
          loginStateManager.clearLoginState();

          // 更新全局状态
          globalData.userInfo = null;
          globalData.isLogin = false;

          // 更新页面状态
          this.setData({
            userInfo: null,
            isLogin: false,
            stats: this.data.defaultStats,
            loading: false
          });

          // 如果是由于ID不一致导致的无效，显示提示
          if (result.message === '用户ID不一致') {
            wx.showModal({
              title: '登录状态异常',
              content: '检测到登录状态异常，将重新登录以确保数据一致性',
              showCancel: false,
              success: () => {
                // 跳转到登录页
                wx.navigateTo({
                  url: '/pages/auth/auth'
                });
              }
            });
          }
        }
      })
      .catch(err => {
        console.error('验证登录状态出错:', err);

        // 网络错误时，尝试使用本地状态
        const loginState = loginStateManager.getLoginState();
        const userInfo = wx.getStorageSync('userInfo');

        if (loginState && userInfo) {
          // 减少日志输出：简化网络错误处理日志
          // console.log('网络错误，使用本地登录状态');

          // 更新全局状态
          globalData.userInfo = userInfo;
          globalData.isLogin = true;

          // 更新页面状态
          this.setData({
            userInfo: userInfo,
            isLogin: true,
            loading: false
          });

          // 获取用户统计数据
          this.getUserStats();
        } else {
          // 如果没有可用的本地状态，才清除登录状态
          loginStateManager.clearLoginState();

          // 更新全局状态
          globalData.userInfo = null;
          globalData.isLogin = false;

          // 更新页面状态
          this.setData({
            userInfo: null,
            isLogin: false,
            stats: this.data.defaultStats,
            loading: false
          });
        }
      });
  },

  // 在后台验证登录状态
  validateLoginStateInBackground: function() {
    // 避免重复验证
    if (this.isValidatingLogin) {
      // 减少日志输出：简化重复验证提示
      // console.log('正在验证登录状态，跳过重复调用');
      return;
    }
    
    this.isValidatingLogin = true;
    
    loginStateManager.validateLoginState()
      .then(result => {
        // 减少日志输出：简化后台验证结果日志
        // console.log('后台验证登录状态结果:', result);
        const app = getApp();
        const globalData = app.globalData || {};

        // 即使后台验证失败，也不立即清除登录状态
        // 这样可以避免用户体验中断
        if (!result.isValid && !result.usingLocalState) {
          console.warn('后台验证发现登录状态无效，但不立即清除:', result.message);

          // 不立即清除登录状态和更新UI，而是在下次应用启动时处理
          // 这样可以避免用户当前会话中突然登出

          // 记录验证失败，但不影响当前会话
          globalData.loginValidationFailed = true;
        } else if (result.userInfo) {
          // 如果有用户信息，确保全局状态和页面状态保持一致
          globalData.userInfo = result.userInfo;
          globalData.isLogin = true;

          // 更新页面状态，确保显示正确的用户信息
          if (!this.data.isLogin || !this.data.userInfo) {
            this.setData({
              userInfo: result.userInfo,
              isLogin: true,
              identityLabel: '普通用户' // 设置默认身份标签
            });

            // 获取用户统计数据（但不再循环调用getUserRoles）
            this.getUserStats();
          }
        }
      })
      .catch(err => {
        console.error('后台验证登录状态出错:', err);
        // 网络错误时不做任何处理，保留本地登录状态
      })
      .finally(() => {
        this.isValidatingLogin = false;
      });
  },

  // 使用本地存储的用户信息 - 已不再使用，由loginStateManager替代
  useLocalUserInfo: function() {
    const app = getApp();

    // 使用登录状态管理器验证登录状态
    loginStateManager.validateLoginState()
      .then(result => {
        if (result.isValid) {
          app.globalData.userInfo = result.userInfo;
          app.globalData.isLogin = true;
          this.setData({
            userInfo: result.userInfo,
            isLogin: true,
            loading: false,
            identityLabel: '普通用户' // 设置默认身份标签
          });

          this.getUserStats();
          
          // 获取用户身份信息
          this.getUserRoles();
        } else {
          // 未登录状态，使用默认数据
          this.setData({
            isLogin: false,
            stats: this.data.defaultStats, // 使用默认数据（全部为0）
            loading: false
          });
        }
      })
      .catch(() => {
        // 未登录状态，使用默认数据
        this.setData({
          isLogin: false,
          stats: this.data.defaultStats, // 使用默认数据（全部为0）
          loading: false
        });
      });
  },

  // 获取用户统计数据（真实数据）
  getUserStats: function() {
    // 防抖处理，避免频繁调用
    if (this.getUserStatsTimer) {
      clearTimeout(this.getUserStatsTimer);
    }
    
    this.getUserStatsTimer = setTimeout(() => {
      this._doGetUserStats();
    }, 200); // 200ms防抖延迟
  },

  // 实际执行获取用户统计数据的方法
  _doGetUserStats: function() {
    const app = getApp();
    const userInfo = app.globalData.userInfo;
    if (!userInfo || !userInfo.id) {
      this.setData({ 
        stats: this.data.defaultStats,
        wallet: this.data.defaultWallet
      });
      return;
    }
    
    console.log('开始获取用户统计数据，用户ID:', userInfo.id);
    
    // 获取用户基本统计信息
    userApi.getUserInfo(userInfo.id).then(res => {
      if (res.success && res.data) {
        // 兼容后端字段
        const d = res.data;
        this.setData({
          stats: {
            followCount: d.followCount || 0,
            followerCount: d.fansCount || d.followerCount || 0,
            favoriteCount: d.favoriteCount || 0,
            points: d.points || 0,
            balance: d.balance || 0,
    
            commentCount: d.commentCount || 0,
            promotionCount: d.promotionCount || 0,
            unpaidCount: d.unpaidCount || 0,
            pendingPickupCount: d.pendingPickupCount || 0,
            unreceivedCount: d.unreceivedCount || 0,
            unratedCount: d.unratedCount || 0,
            completedCount: d.completedCount || 0
          }
        });
      } else {
        this.setData({ stats: this.data.defaultStats });
      }
    }).catch(err => {
      console.error('获取用户统计数据失败:', err);
      this.setData({ stats: this.data.defaultStats });
    });

    // 获取用户钱包信息
    userApi.getUserWallet().then(res => {
      if (res.success && res.data) {
        this.setData({
          wallet: {
            balance: res.data.balance || 0,
            redPacket: res.data.redPacket || 0,
            coupon: res.data.coupon || 0,
            points: res.data.points || 0
          }
        });
      } else {
        this.setData({ wallet: this.data.defaultWallet });
      }
    }).catch(err => {
      console.error('获取用户钱包信息失败:', err);
      this.setData({ wallet: this.data.defaultWallet });
    });
  },

  // 获取用户VIP信息
  getUserVipInfo: function() {
    if (!this.data.isLogin) return;
    vipApi.getUserVipInfo()
      .then(res => {
        if (res.success && res.data) {
          this.setData({
            vipInfo: res.data
          });
        } else {
          this.setData({
            vipInfo: {
              level_code: 'free',
              level_name: '普通用户',
              expire_time: null
            }
          });
        }
      })
      .catch(err => {
        console.error('获取用户VIP信息失败:', err);
      });
  },

  // 跳转到登录页
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/auth/auth'
    });
  },

  // 编辑资料
  editProfile: function() {
    wx.navigateTo({
      url: '/pages/profile/edit'
    });
  },



  // 查看积分
  viewPoints: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/points/points'  // 修正：跳转到积分中心页面
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/points/points'  // 修正：跳转到积分中心页面
    });
  },

  // 查看余额
  viewBalance: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/wallet/wallet'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/wallet/wallet'
    });
  },

  // 查看红包
  viewRedPacket: function() {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/wallet/wallet'
        });
      }
    })) {
      return;
    }
    wx.navigateTo({
      url: '/pages/wallet/wallet'
    });
  },

  // 查看卡券
  viewCoupon: function() {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/wallet/wallet'
        });
      }
    })) {
      return;
    }
    wx.navigateTo({
      url: '/pages/wallet/wallet'
    });
  },

  // 查看钱包详情
  viewWalletDetail: function() {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/wallet/wallet'
        });
      }
    })) {
      return;
    }
    wx.navigateTo({
      url: '/pages/wallet/wallet'
    });
  },

  // 领取红包
  getRedPacket: function() {
    // 显示暂无红包提示
    wx.showToast({
      title: '暂无红包',
      icon: 'none',
      duration: 2000,
      mask: true // 防止用户在提示期间进行其他操作
    });
  },

  // 会员中心
  goToVipCenter: function() {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/vip/center'
        });
      }
    })) {
      return;
    }
    wx.navigateTo({
      url: '/pages/vip/center'
    });
  },

  // 分享本店
  shareStore: function() {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/share/share'
        });
      }
    })) {
      return;
    }
    
    wx.navigateTo({
      url: '/pages/share/share'
    });
  },

  // 我的收藏
  viewFavorites: function() {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/favorites/favorites'
        });
      }
    })) {
      return;
    }
    wx.navigateTo({
      url: '/pages/favorites/favorites'
    });
  },

  // 全部订单
  viewAllOrders: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/order/list?type=all'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/order/list?type=all'
    });
  },

  // 待付款
  viewUnpaidOrders: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/order/list?type=pending_payment'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/order/list?type=pending_payment'
    });
  },

  // 待自提
  viewPendingPickupOrders: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/order/list?type=pending_pickup'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/order/list?type=pending_pickup'
    });
  },

  // 待收货
  viewUnreceivedOrders: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/order/list?type=shipped'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/order/list?type=shipped'
    });
  },

  // 退换货
  viewUnratedOrders: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/order/list?type=refund'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/order/list?type=refund'
    });
  },

  // 已完成订单
  viewCompletedOrders: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/order/list?type=completed'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/order/list?type=completed'
    });
  },

  // 收货地址
  viewAddress: function() {
    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/address/list'
        });
      }
    })) {
      return;
    }

    wx.navigateTo({
      url: '/pages/address/list'
    });
  },

  // 客服中心
  contactService: function() {
    wx.navigateTo({
      url: '/pages/service/index'
    });
  },

  // 设置
  goToSettings: function() {
    const app = getApp();
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '请先登录',
        content: '登录后可进入账号设置',
        showCancel: true,
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({ url: '/pages/auth/auth' });
          }
        }
      });
      return;
    }
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },

  // 关于我们
  goToAbout: function() {
    wx.navigateTo({
      url: '/pages/about/index'
    });
  },

  // 轮播图点击事件
  onBannerTap: function(e) {
    const id = e.currentTarget.dataset.id;
    const url = e.currentTarget.dataset.url;
    const linkType = e.currentTarget.dataset.linkType || 'page'; // 从dataset获取linkType，默认为page

    if (url) {
      if (linkType === 'url') {
        // 打开网页
        wx.navigateTo({
          url: `/pages/webview/webview?url=${encodeURIComponent(url)}`
        });
      } else if (linkType === 'page') {
        // 打开小程序页面
        wx.navigateTo({
          url: url,
          fail: function(err) {
            console.error('轮播图跳转失败', err);
            // 如果navigateTo失败，尝试使用switchTab
            wx.switchTab({
              url: url.split('?')[0],
              fail: function(switchErr) {
                console.error('switchTab也失败了', switchErr);
                wx.showToast({
                  title: '页面跳转失败',
                  icon: 'none'
                });
              }
            });
          }
        });
      }
    }
  },

  // 退出登录
  logout: function() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 使用登录状态管理器登出
          loginStateManager.logout()
            .then(() => {
              // 清除全局数据
              const app = getApp();
              app.globalData.userInfo = null;
              app.globalData.isLogin = false;

              // 更新页面状态
              this.setData({
                userInfo: null,
                isLogin: false,
                stats: this.data.defaultStats // 使用默认数据（全部为0）
              });

              // 确保轮播图数据仍然存在
              if (this.data.banners.length === 0) {
                console.log('退出登录后轮播图数据为空，重新获取');
                this.getBanners();
              }

              wx.showToast({
                title: '已退出登录',
                icon: 'success'
              });
            });
        }
      }
    });
  },

  // 开发者选项已移除



  // 新增：点击身份标签跳转到管理端
  onIdentityLabelTap: function() {
    // 检查用户是否具有管理员角色（通过角色类型判断）
    const hasAdminRole = this.data.userRoles && this.data.userRoles.some(role => role.role_type === 'admin');
    if (hasAdminRole) {
      wx.reLaunch({ url: '/admin/console/console' });
    }
  },

  // 新增：获取多重身份
  getUserRoles: function() {
    // 防抖处理，避免频繁调用
    if (this.getUserRolesTimer) {
      clearTimeout(this.getUserRolesTimer);
    }
    
    this.getUserRolesTimer = setTimeout(() => {
      this._doGetUserRoles();
    }, 150); // 150ms防抖延迟
  },

  // 实际执行获取用户角色的方法
  _doGetUserRoles: function() {
    const that = this;
    const app = getApp();
    // 减少日志输出：简化身份调试日志
    // console.log('[身份调试] 开始获取用户身份，登录状态:', app.globalData.isLogin, '用户信息:', app.globalData.userInfo);
    if (!app.globalData.isLogin || !app.globalData.userInfo) {
      // 减少日志输出：简化跳过日志
      // console.log('[身份调试] 用户未登录或用户信息为空，跳过身份获取');
      return;
    }
    const userId = app.globalData.userInfo.id;
    if (!userId) {
      // 减少日志输出：简化跳过日志
      // console.log('[身份调试] 用户ID为空，跳过身份获取');
      return;
    }
    // 减少日志输出：简化API调用日志
    // console.log('[身份调试] 开始调用getUserRoles API，用户ID:', userId);
    
    // 根据当前页面路径判断preferredRole参数
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentRoute = currentPage.route;
    
    let preferredRole = 'customer'; // 默认为顾客
    let clientType = 'customer'; // 默认为顾客端
    if (currentRoute.startsWith('admin/')) {
      clientType = 'admin';
    } else if (currentRoute.startsWith('partner/')) {
      clientType = 'partner';
    }
    
    const userApi = require('../../utils/api').userApi;
    userApi.getUserRoles({ clientType: clientType }).then(res => {
      // 减少日志输出：简化API响应日志
      // console.log('[身份调试] getUserRoles API响应:', res);
      if (res.success && res.data) {
        const roles = res.data.roles || [];
        let currentRole = res.data.currentRole || null;
        // 减少日志输出：简化角色数据日志
        // console.log('[身份调试] 解析到的角色数据:', { roles, currentRole });
        
        // 根据当前页面路径判断应该显示哪个身份
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const currentRoute = currentPage.route;
        // 减少日志输出：简化页面路径日志
        // console.log('[身份调试] 当前页面路径:', currentRoute);
        
        let targetRole = null;
        let identityLabel = '';
        
        // 判断当前所在的端
        if (currentRoute.startsWith('admin/')) {
          // 管理端 - 显示管理员身份
          // 减少日志输出：简化端判断日志
          // console.log('[身份调试] 当前在管理端，优先显示管理员身份');
          targetRole = roles.find(r => r.role_type === 'admin') || roles.find(r => r.role_type === 'super_admin');
          identityLabel = targetRole ? (targetRole.role_name || targetRole.role_type) : '普通用户';
        } else if (currentRoute.startsWith('partner/')) {
          // 合伙人端 - 显示合伙人身份
          // 减少日志输出：简化端判断日志
          // console.log('[身份调试] 当前在合伙人端，优先显示合伙人身份');
          targetRole = roles.find(r => r.role_type === 'partner');
          identityLabel = targetRole ? (targetRole.role_name || targetRole.role_type) : '普通用户';
        } else {
          // 顾客端 - 只显示顾客身份，不显示管理员或合伙人身份
          // 减少日志输出：简化端判断日志
          // console.log('[身份调试] 当前在顾客端，只显示顾客身份');
          // 在顾客端只查找顾客角色
          targetRole = roles.find(r => r.role_type === 'customer');
          // 如果有顾客角色，显示其级别；否则使用VIP等级或默认值
          if (targetRole) {
            identityLabel = targetRole.role_name || targetRole.role_type;
          } else {
            // 如果没有顾客角色，使用VIP等级
            identityLabel = that.data.vipInfo.level_name || '普通用户';
          }
        }
        
        // 在顾客端，如果没有找到顾客角色，使用VIP等级
        if (!targetRole && !currentRoute.startsWith('admin/') && !currentRoute.startsWith('partner/')) {
          // 减少日志输出：简化VIP等级日志
          // console.log('[身份调试] 顾客端没有找到顾客角色，使用VIP等级');
          identityLabel = that.data.vipInfo.level_name || '普通用户';
        }
        
        // 减少日志输出：简化最终结果日志
        // console.log('[身份调试] userRoles:', roles, 'targetRole:', targetRole, 'identityLabel:', identityLabel);
        that.setData({
          userRoles: roles,
          currentRole: targetRole,
          showRoleSwitch: roles.length > 1,
          identityLabel: identityLabel
        });
        // 强制写入全局变量
        const app = getApp();
        app.globalData.currentRole = targetRole;
        app.globalData.userRoles = roles;
      } else {
        // 多重身份接口无数据，降级显示VIP等级
        let identityLabel = that.data.vipInfo.level_name || '普通用户';
        // 减少日志输出：简化无数据日志
        // console.log('[身份调试] userRoles接口无数据，降级显示VIP:', identityLabel);
        that.setData({
          userRoles: [],
          currentRole: null,
          showRoleSwitch: false,
          identityLabel: identityLabel
        });
        // 强制写入全局变量
        const app = getApp();
        app.globalData.currentRole = null;
        app.globalData.userRoles = [];
      }
    }).catch((error) => {
      console.error('getUserRoles API调用失败:', error);
      
      // 检查是否是需要重新登录的错误
      if (error.code === 'PREFERRED_ROLE_REQUIRED' || 
          (error.message && error.message.includes('请退出重新登录'))) {
        wx.showModal({
          title: '提示',
          content: '登录状态异常，请重新登录',
          showCancel: false,
          success: function() {
            // 清除登录状态
            const app = getApp();
            app.globalData.isLogin = false;
            app.globalData.userInfo = null;
            app.globalData.currentRole = null;
            app.globalData.userRoles = [];
            wx.removeStorageSync('userInfo');
            wx.removeStorageSync('token');
            
            // 跳转到登录页
            wx.reLaunch({
              url: '/pages/login/login'
            });
          }
        });
        return;
      }
      
      // 其他接口异常，降级显示VIP等级
      let identityLabel = that.data.vipInfo.level_name || '普通用户';
      // 减少日志输出：简化异常日志
      // console.log('[身份调试] userRoles接口异常，降级显示VIP:', identityLabel, '当前vipInfo:', that.data.vipInfo);
      that.setData({
        userRoles: [],
        currentRole: null,
        showRoleSwitch: false,
        identityLabel: identityLabel
      });
      // 强制写入全局变量
      const app = getApp();
      app.globalData.currentRole = null;
      app.globalData.userRoles = [];
    });
  },

  // 跳转到登录切换页
  onGoSwitchLoginPage: function() {
    const app = getApp();
    app.globalData.currentRole = this.data.currentRole;
    app.globalData.userRoles = this.data.userRoles;
    wx.navigateTo({ url: '/pages/switch-login/switch-login' });
  },

  // 常见问题
  viewFAQ: function() {
    wx.navigateTo({
      url: '/pages/faq/faq'
    });
  },

  // 加入合伙人
  joinPartner: function() {
    wx.navigateTo({
      url: '/pages/partner/join-partner'
    });
  },

  // 查看我的推广
  viewMyPromotions: function() {
    const app = getApp();
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: '/pages/profile/promotions'
        });
      }
    })) {
      return;
    }
    wx.navigateTo({
      url: '/pages/profile/promotions'
    });
  }
});
