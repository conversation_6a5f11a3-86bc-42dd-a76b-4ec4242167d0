const db = require('../config/db');

/**
 * 管理端数据统计接口
 * 返回：已开门店数、合伙人数、累计用户数
 */
exports.getSummary = async (req, res) => {
  try {
    // 统计门店总数
    const storeRows = await db.query('SELECT COUNT(*) as count FROM stores');
    const storeCount = storeRows[0]?.count || 0;

    // 统计合伙人数（users表role_type=partner 或 user_roles表有partner）
    // 先查users表
    const partnerRows = await db.query('SELECT COUNT(*) as count FROM users WHERE role_type = ?',["partner"]);
    let partnerCount = partnerRows[0]?.count || 0;
    // 再查user_roles表
    const userRolesRows = await db.query('SELECT COUNT(DISTINCT user_id) as count FROM user_roles WHERE role_type = ?',["partner"]);
    partnerCount += userRolesRows[0]?.count || 0;

    // 统计累计用户数
    const userRows = await db.query('SELECT COUNT(*) as count FROM users');
    const userCount = userRows[0]?.count || 0;

    // 统计累计销售额（只统计已支付/已完成订单，门店归属字段为store_no）
    const salesRows = await db.query('SELECT SUM(total_amount) as total FROM store_orders WHERE status IN (?, ?)', ["已支付", "已完成"]);
    const totalSales = salesRows[0]?.total || 0;

    // 按门店统计
    const storeSalesRows = await db.query('SELECT store_no, SUM(total_amount) as total FROM store_orders WHERE status IN (?, ?) GROUP BY store_no', ["已支付", "已完成"]);
    // 按销售人统计（使用user_id字段，因为salesman_id字段不存在）
    const salesmanSalesRows = await db.query('SELECT user_id as salesman_id, SUM(total_amount) as total FROM store_orders WHERE status IN (?, ?) GROUP BY user_id', ["已支付", "已完成"]);
    // 总部统计（store_no='PLAT'）
    const platSalesRows = await db.query('SELECT SUM(total_amount) as total FROM store_orders WHERE status IN (?, ?) AND store_no = ?', ["已支付", "已完成", "PLAT"]);
    const platSales = platSalesRows[0]?.total || 0;

    res.json({
      success: true,
      data: {
        storeCount,
        partnerCount,
        userCount,
        totalSales,
        storeSales: storeSalesRows,
        salesmanSales: salesmanSalesRows,
        platSales
      }
    });
  } catch (error) {
    console.error('获取管理端统计数据失败:', error);
    res.status(500).json({ success: false, message: '获取统计数据失败' });
  }
};

/**
 * 管理端徽标统计接口
 * 返回：各类待处理数量
 */
exports.getBadges = async (req, res) => {
  try {
    // 统计待处理快递订单数（顾客订单中的快递订单）
    const kuaidiPendingRows = await db.query(`
      SELECT COUNT(*) as count 
      FROM customer_orders 
      WHERE status = '待发货' AND delivery_method = 'express'
    `);
    const kuaidiPending = kuaidiPendingRows[0]?.count || 0;

    // 统计待处理自提订单数（顾客订单中的自提订单）
    const zitiPendingRows = await db.query(`
      SELECT COUNT(*) as count 
      FROM customer_orders 
      WHERE status = '待自提' AND delivery_method = 'self'
    `);
    const zitiPending = zitiPendingRows[0]?.count || 0;

    // 统计待审核门店采购订单数（兼容中文和英文状态）
    const caigouPendingRows = await db.query(`
      SELECT COUNT(*) as count 
      FROM store_orders 
      WHERE status = '待审核' AND type = 'purchase'
    `);
    const caigouPending = caigouPendingRows[0]?.count || 0;

    // 统计待处理门店移库订单数（暂时设为0，等待具体业务逻辑）
    const yikuPending = 0;

    // 统计待审核合伙人申请数
    const partnerApplicationPendingRows = await db.query(`
      SELECT COUNT(*) as count 
      FROM partner_applications 
      WHERE status = '待审核'
    `);
    const partnerApplicationPending = partnerApplicationPendingRows[0]?.count || 0;

    // 统计待处理用户提现数
    const userWithdrawPendingRows = await db.query(`
      SELECT COUNT(*) as count 
      FROM user_fund_records 
      WHERE type = 'withdrawal' AND status = '待处理'
    `);
    const userWithdrawPending = userWithdrawPendingRows[0]?.count || 0;

    res.json({
      success: true,
      data: {
        kuaidiPending,
        zitiPending,
        caigouPending,
        yikuPending,
        partnerApplicationPending,
        userWithdrawPending
      }
    });
  } catch (error) {
    console.error('获取管理端徽标数据失败:', error);
    res.status(500).json({ success: false, message: '获取徽标数据失败' });
  }
};