// pages/order/refund.js
const { orderApi } = require('../../utils/api');

Page({
  data: {
    orderId: null,
    orderDetail: null,
    loading: true,
    refundReasons: [
      '不想要了/拍错了',
      '商品缺货',
      '协商一致退款',
      '未按约定时间发货',
      '其他'
    ],
    selectedReason: '',
    customReason: '',
    refundAmount: 0,
    refundType: 'full', // full: 全额退款, partial: 部分退款
    images: [],
    maxImageCount: 3,
    submitting: false
  },

  onLoad: function(options) {
    console.log('订单退款页面加载，参数:', options);
    if (options && options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadOrderDetail(options.id);
    } else {
      wx.showToast({
        title: '订单ID不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载订单详情
  loadOrderDetail: function(orderId) {
    this.setData({ loading: true });
    
    orderApi.getOrderById(orderId).then(res => {
      if (res.success && res.data) {
        const orderDetail = res.data;
        
        // 检查订单状态是否可以申请退款
        const allowRefundStatus = ['pending_shipment', 'shipped', 'completed'];
        if (!allowRefundStatus.includes(orderDetail.status)) {
          wx.showToast({
            title: '该订单状态不可申请退款',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
          return;
        }
        
        this.setData({
          orderDetail,
          refundAmount: orderDetail.total_amount,
          loading: false
        });
      } else {
        this.setData({ loading: false });
        wx.showToast({
          title: '获取订单详情失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取订单详情失败:', err);
      this.setData({ loading: false });
      wx.showToast({
        title: '获取订单详情失败',
        icon: 'none'
      });
    });
  },

  // 选择退款原因
  selectReason: function(e) {
    const reason = e.currentTarget.dataset.reason;
    this.setData({
      selectedReason: reason
    });
  },

  // 自定义原因输入
  inputCustomReason: function(e) {
    this.setData({
      customReason: e.detail.value
    });
  },

  // 退款金额输入
  inputRefundAmount: function(e) {
    let amount = parseFloat(e.detail.value);
    const totalAmount = this.data.orderDetail.total_amount;
    
    // 确保退款金额不超过订单总金额
    if (amount > totalAmount) {
      amount = totalAmount;
      wx.showToast({
        title: '退款金额不能超过订单金额',
        icon: 'none'
      });
    }
    
    this.setData({
      refundAmount: amount
    });
  },

  // 选择退款类型
  selectRefundType: function(e) {
    const type = e.currentTarget.dataset.type;
    let refundAmount = this.data.refundAmount;
    
    if (type === 'full') {
      refundAmount = this.data.orderDetail.total_amount;
    }
    
    this.setData({
      refundType: type,
      refundAmount: refundAmount
    });
  },

  // 上传图片
  chooseImage: function() {
    const { images, maxImageCount } = this.data;
    const remainCount = maxImageCount - images.length;
    
    if (remainCount <= 0) {
      wx.showToast({
        title: `最多上传${maxImageCount}张图片`,
        icon: 'none'
      });
      return;
    }
    
    console.log('开始选择退款凭证图片');
    // 先尝试使用chooseMedia API
    try {
      wx.chooseMedia({
        count: remainCount,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: res => {
          console.log('选择退款凭证图片成功:', res);
          const validPaths = [];
          
          for (let i = 0; i < res.tempFiles.length; i++) {
            const file = res.tempFiles[i];
            if (file.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
              wx.showToast({ title: `第${i+1}张图片不能大于1.2MB`, icon: 'none' });
              continue;
            }
            validPaths.push(file.tempFilePath);
          }
          
          if (validPaths.length > 0) {
            this.uploadImages(validPaths);
          }
        },
        fail: err => {
          console.error('chooseMedia 失败:', err);
          // 检查是否是用户取消操作
          if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
            console.log('用户取消选择图片');
            return; // 用户取消，直接返回
          }
          // 其他错误情况才使用备选方案
          this.chooseRefundImageFallback(remainCount);
        }
      });
    } catch (error) {
      console.error('chooseMedia API 不支持，使用备选方案:', error);
      // 如果chooseMedia不支持，直接使用chooseImage
      this.chooseRefundImageFallback(remainCount);
    }
  },

  // 使用chooseImage作为备选方案
  chooseRefundImageFallback: function(remainCount) {
    console.log('使用chooseImage作为备选方案');
    wx.chooseImage({
      count: remainCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: res => {
        console.log('chooseImage成功:', res);
        const validPaths = [];
        
        for (let i = 0; i < res.tempFilePaths.length; i++) {
          const tempFilePath = res.tempFilePaths[i];
          const tempFile = res.tempFiles[i];
          if (tempFile && tempFile.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
            wx.showToast({ title: `第${i+1}张图片不能大于1.2MB`, icon: 'none' });
            continue;
          }
          validPaths.push(tempFilePath);
        }
        
        if (validPaths.length > 0) {
          this.uploadImages(validPaths);
        }
      },
      fail: err => {
        console.error('chooseImage 失败:', err);
        // 检查是否是用户取消操作
        if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
          console.log('用户取消选择图片');
          return; // 用户取消，直接返回
        }
        // 其他错误情况才显示提示
        wx.showToast({
          title: '选择图片失败，请检查相关权限',
          icon: 'none'
        });
      }
    });
  },

  // 上传图片到服务器
  uploadImages: function(tempFilePaths) {
    const { uploadFile } = require('../../utils/api');
    const uploads = tempFilePaths.map(path => {
      return new Promise((resolve, reject) => {
        wx.showLoading({
          title: '上传中...',
          mask: true
        });
        
        uploadFile(path, 'refund').then(res => {
          if (res.success && res.data && res.data.url) {
            resolve(res.data.url);
          } else {
            reject(new Error('上传失败'));
          }
        }).catch(err => {
          console.error('上传图片失败:', err);
          reject(err);
        });
      });
    });
    
    Promise.all(uploads).then(urls => {
      wx.hideLoading();
      this.setData({
        images: [...this.data.images, ...urls]
      });
    }).catch(() => {
      wx.hideLoading();
      wx.showToast({
        title: '图片上传失败',
        icon: 'none'
      });
    });
  },

  // 删除图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.images;
    images.splice(index, 1);
    this.setData({ images });
  },

  // 预览图片
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.images;
    
    wx.previewImage({
      current: images[index],
      urls: images
    });
  },

  // 提交退款申请
  submitRefund: function() {
    const { 
      orderId, 
      selectedReason, 
      customReason, 
      refundAmount, 
      refundType,
      images,
      orderDetail
    } = this.data;
    
    // 验证表单
    if (!selectedReason && selectedReason !== '其他') {
      wx.showToast({
        title: '请选择退款原因',
        icon: 'none'
      });
      return;
    }
    
    if (selectedReason === '其他' && !customReason.trim()) {
      wx.showToast({
        title: '请填写退款原因',
        icon: 'none'
      });
      return;
    }
    
    if (refundType === 'partial' && (!refundAmount || refundAmount <= 0)) {
      wx.showToast({
        title: '请输入有效的退款金额',
        icon: 'none'
      });
      return;
    }
    
    // 防止重复提交
    if (this.data.submitting) return;
    this.setData({ submitting: true });
    
    // 显示加载中
    wx.showLoading({
      title: '提交中',
      mask: true
    });
    
    // 构建退款参数
    const reason = selectedReason === '其他' ? customReason : selectedReason;
    const params = {
      order_id: orderId,
      reason: reason,
      amount: refundType === 'full' ? orderDetail.total_amount : refundAmount,
      images: images
    };
    
    // 调用退款接口
    orderApi.applyRefund(params).then(res => {
      wx.hideLoading();
      this.setData({ submitting: false });
      
      if (res.success) {
        wx.showToast({
          title: '申请退款成功',
          icon: 'success'
        });
        
        // 返回订单详情页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: res.message || '申请退款失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      this.setData({ submitting: false });
      console.error('申请退款失败:', err);
      wx.showToast({
        title: '申请退款失败',
        icon: 'none'
      });
    });
  }
});