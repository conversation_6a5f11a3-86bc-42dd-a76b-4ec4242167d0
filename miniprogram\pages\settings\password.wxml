<!--pages/settings/password.wxml-->
<view class="password-container">
  <!-- 顶部标题已去除，使用导航栏标题 -->

  <view class="password-content">
    <view class="input-area">
      <!-- 账号输入框 -->
      <view class="input-group">
        <image class="input-icon" src="../../images/icons2/用户.png"></image>
        <input class="input" type="text" placeholder="请输入账号（字母数字组合）" value="{{username}}" bindinput="inputUsername" maxlength="20" />
      </view>
      
      <view wx:if="{{showOldPassword}}" class="input-group">
        <image class="input-icon" src="../../images/icons2/密码.png"></image>
        <input class="input" type="password" placeholder="请输入旧密码" password="true" bindinput="inputOldPassword" />
      </view>
      
      <view class="input-group">
        <image class="input-icon" src="../../images/icons2/密码.png"></image>
        <input class="input" type="password" placeholder="请输入新密码" password="true" bindinput="inputNewPassword" />
      </view>
      
      <view class="input-group">
        <image class="input-icon" src="../../images/icons2/密码.png"></image>
        <input class="input" type="password" placeholder="请确认新密码" password="true" bindinput="inputConfirmPassword" />
      </view>
    </view>
    
    <view class="tips">账号只能包含字母和数字，长度3-20位；密码长度不少于6位，建议使用字母、数字和符号的组合</view>
    
    <view class="save-button {{loading ? 'disabled' : ''}}" bindtap="savePassword">
      <text>{{loading ? '保存中...' : '保存'}}</text>
    </view>
  </view>
</view>
