const { userApi } = require('../../utils/api');

Page({
  data: {
    storeList: [], // 门店列表
    loading: true
  },

  onLoad: function(options) {
    console.log('自提门店选择页面加载');
    this.getStoreList();
  },

  // 获取门店列表
  getStoreList: function() {
    console.log('开始获取门店列表');
    
    this.setData({ loading: true });
    
    userApi.getSalesmanStores()
      .then(res => {
        console.log('销售人门店API响应:', res);
        
        if (res && res.success) {
          if (res.data && res.data.length > 0) {
            console.log('获取到销售人门店列表:', res.data);
            this.setData({
              storeList: res.data,
              loading: false
            });
          } else {
            console.log('销售人暂无门店');
            this.setData({
              storeList: [],
              loading: false
            });
          }
        } else {
          console.log('获取销售人门店失败:', res.message);
          this.setData({
            storeList: [],
            loading: false
          });
        }
      })
      .catch(err => {
        console.error('获取销售人门店失败:', err);
        this.setData({
          storeList: [],
          loading: false
        });
      });
  },

  // 选择门店
  selectStore: function(e) {
    const store = e.currentTarget.dataset.store;
    console.log('选择门店:', store);
    
    // 检查门店库存
    this.checkStoreInventory(store);
  },

  // 检查门店库存
  checkStoreInventory: function(store) {
    console.log('检查门店库存:', store.store_no);
    
    // 获取当前订单的商品信息
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];
    
    if (!prevPage || prevPage.route !== 'pages/order/create') {
      console.error('无法获取订单页面信息');
      this.showInventoryError();
      return;
    }
    
    const cartItems = prevPage.data.cartItems || [];
    if (cartItems.length === 0) {
      console.error('购物车商品为空');
      this.showInventoryError();
      return;
    }
    
    // 显示检查库存的加载提示
    wx.showLoading({
      title: '检查库存中...'
    });
    
    // 调用库存检查API
    const { storeApi } = require('../../utils/api');
    const checkData = {
      store_no: store.store_no,
      products: cartItems.map(item => ({
        product_id: item.productId,
        quantity: item.quantity
      }))
    };
    
    storeApi.checkOfflineInventory(checkData)
      .then(res => {
        wx.hideLoading();
        console.log('库存检查结果:', res);
        
        if (res && res.success) {
          if (res.data && res.data.sufficient) {
            // 库存充足，可以选择该门店
            this.confirmStoreSelection(store);
          } else {
            // 库存不足，显示提示
            this.showInventoryInsufficientDialog();
          }
        } else {
          // API调用失败
          this.showInventoryError();
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('检查库存失败:', err);
        this.showInventoryError();
      });
  },

  // 确认门店选择
  confirmStoreSelection: function(store) {
    console.log('确认选择门店:', store);
    
    // 检查用户与门店是否在同一城市
    const userInfo = wx.getStorageSync('userInfo') || {};
    const userCity = userInfo.city;
    const storeCity = store.city;
    
    if (userCity && storeCity && userCity !== storeCity) {
      // 不在同一城市，显示确认弹窗
      wx.showModal({
        title: '提示',
        content: '您与门店不在同一城市，请确认是否要在此门店自提',
        cancelText: '取消',
        confirmText: '确认',
        success: (res) => {
          if (res.confirm) {
            this.setStoreAndReturn(store);
          }
        }
      });
    } else {
      // 在同一城市或无法判断，直接选择
      this.setStoreAndReturn(store);
    }
  },

  // 设置门店并返回
  setStoreAndReturn: function(store) {
    // 显示选择提示
    wx.showToast({
      title: '已选择门店',
      icon: 'success'
    });
    
    // 返回上一页并传递选中的门店信息
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];
    
    if (prevPage && prevPage.route === 'pages/order/create') {
      prevPage.setData({
        storeInfo: store
      });
    }
    
    // 延迟返回，让用户看到选择成功的提示
    setTimeout(() => {
      wx.navigateBack();
    }, 1000);
  },

  // 显示库存不足提示
  showInventoryInsufficientDialog: function() {
    wx.showModal({
      title: '提示',
      content: '线下库存不足，无法自提，请选择快递方式',
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 显示库存检查错误
  showInventoryError: function() {
    wx.showToast({
      title: '库存检查失败，请重试',
      icon: 'none'
    });
  }
});