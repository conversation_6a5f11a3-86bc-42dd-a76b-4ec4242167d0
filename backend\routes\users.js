/**
 * 用户路由
 */
const express = require('express');
const { body, param } = require('express-validator');
const userController = require('../controllers/userController');
const { checkAuth } = require('../middleware/auth');
const validate = require('../middleware/validation');

const router = express.Router();

// 用户登录
router.post('/login', [
  body('username').isString().withMessage('用户名必须是字符串'),
  body('password').isString().withMessage('密码必须是字符串'),
  validate
], userController.login);



// 手机号登录
router.post('/login/phone', [
  body('phone').isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
  body('code').isString().withMessage('验证码必须是字符串'),
  validate
], userController.loginByPhone);

// 微信登录
router.post('/login/wechat', [
  body('code').isString().withMessage('微信授权码必须是字符串'),
  validate
], userController.loginByWechat);

// 用户注册
router.post('/register', [
  body('username').isString().withMessage('用户名必须是字符串'),
  body('password').isString().withMessage('密码必须是字符串'),
  body('nickname').optional().isString().withMessage('昵称必须是字符串'),
  body('phone').optional().isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
  validate
], userController.register);

// 获取用户信息
router.get('/info', checkAuth, userController.getUserInfo);

// 获取用户钱包信息
router.get('/wallet', checkAuth, userController.getUserWallet);

// 更新用户信息
router.put('/info', [
  checkAuth,
  body('username').optional().isString().withMessage('用户名必须是字符串').isLength({ min: 3, max: 20 }).withMessage('用户名长度应为3-20位').matches(/^[a-zA-Z0-9]+$/).withMessage('用户名只能包含字母和数字'),
  body('nickname').optional().isString().withMessage('昵称必须是字符串'),
  body('avatar').optional().isString().withMessage('头像必须是字符串'),
  body('phone').optional().isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
  body('email').optional().isEmail().withMessage('邮箱格式不正确'),
  body('gender').optional().isIn(['male', 'female', 'other']).withMessage('性别值无效'),
  body('country').optional().isString().withMessage('国家必须是字符串'),
  body('province').optional().isString().withMessage('省份必须是字符串'),
  body('city').optional().isString().withMessage('城市必须是字符串'),
  body('oldPassword').optional().isString().withMessage('旧密码必须是字符串'),
  body('newPassword').optional().isString().withMessage('新密码必须是字符串'),
  validate
], userController.updateUserInfo);

// 发送验证码
router.post('/send-code', [
  body('phone').isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
  validate
], userController.sendVerificationCode);

// 更新密码
router.put('/password', [
  checkAuth,
  body('oldPassword').isString().withMessage('旧密码必须是字符串'),
  body('newPassword').isString().withMessage('新密码必须是字符串'),
  validate
], userController.updatePassword);

// 绑定微信
router.post('/bind/wechat', [
  checkAuth,
  body('code').isString().withMessage('微信授权码必须是字符串'),
  validate
], userController.bindWechat);

// 解绑微信
router.post('/unbind/wechat', checkAuth, userController.unbindWechat);

// 发送验证码
router.post('/send-verify-code', [
  body('phone').isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
  validate
], userController.sendVerifyCode);

// 通过手机号重置密码
router.post('/reset-password', [
  body('phone').isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
  body('code').isString().withMessage('验证码必须是字符串'),
  body('newPassword').isString().withMessage('新密码必须是字符串'),
  validate
], userController.resetPasswordByPhone);

// 关注用户
router.post('/follow', checkAuth, userController.followUser);
// 取消关注
router.post('/unfollow', checkAuth, userController.unfollowUser);
// 检查关注状态
router.get('/follow/status', checkAuth, userController.checkFollowStatus);

// 我的关注列表
router.get('/my-follow', checkAuth, userController.getMyFollowList);
// 我的粉丝列表
router.get('/my-fans', checkAuth, userController.getMyFansList);

// 我的推广用户列表
router.get('/my-promotion', checkAuth, userController.getMyPromotionList);

// 获取用户资金变动记录
router.get('/fund-records', checkAuth, userController.getUserFundRecords);


// 多重身份查询
router.get('/roles', checkAuth, userController.getUserRoles);

// 获取所有用户列表（管理端）
router.get('/list', userController.getAllUsers);

// 管理员更新用户信息（包括余额调整）
router.put('/admin/:userId', [
  param('userId').isInt().withMessage('用户ID必须是整数'),
  body('referrerId').optional().isInt().withMessage('推荐人ID必须是整数'),
  body('salesmanId').optional().isInt().withMessage('销售人ID必须是整数'),
  body('subscribedStore').optional().isString().withMessage('订阅门店必须是字符串'),
  body('balanceAdjustment.amount').optional().isFloat().withMessage('调整金额必须是数字'),
  body('balanceAdjustment.reason').optional().isString().withMessage('调整事由必须是字符串'),
  validate
], userController.adminUpdateUser);

// 批量修改用户状态
router.post('/batch-status', userController.batchUpdateStatus);

// 变更销售人并同步订阅门店
router.post('/change-salesman', userController.changeSalesmanAndStore);

// 地址相关路由
const addressController = require('../controllers/addressController');

// 获取默认收货地址
router.get('/address/default', checkAuth, addressController.getDefaultAddress);

// 获取地址列表
router.get('/address/list', checkAuth, addressController.getAddressList);

// 添加地址
router.post('/address', [
  checkAuth,
  body('name').isString().withMessage('收货人姓名必须是字符串'),
  body('phone').isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
  body('province').isString().withMessage('省份必须是字符串'),
  body('city').isString().withMessage('城市必须是字符串'),
  body('district').isString().withMessage('区县必须是字符串'),
  body('detail').isString().withMessage('详细地址必须是字符串'),
  body('isDefault').optional().isBoolean().withMessage('是否默认地址必须是布尔值'),
  validate
], addressController.addAddress);

// 更新地址
router.put('/address/:id', [
  checkAuth,
  param('id').isInt().withMessage('地址ID必须是整数'),
  body('name').isString().withMessage('收货人姓名必须是字符串'),
  body('phone').isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
  body('province').isString().withMessage('省份必须是字符串'),
  body('city').isString().withMessage('城市必须是字符串'),
  body('district').isString().withMessage('区县必须是字符串'),
  body('detail').isString().withMessage('详细地址必须是字符串'),
  body('isDefault').optional().isBoolean().withMessage('是否默认地址必须是布尔值'),
  validate
], addressController.updateAddress);

// 删除地址
router.delete('/address/:id', [
  checkAuth,
  param('id').isInt().withMessage('地址ID必须是整数'),
  validate
], addressController.deleteAddress);

// 设置默认地址
router.put('/address/:id/default', [
  checkAuth,
  param('id').isInt().withMessage('地址ID必须是整数'),
  validate
], addressController.setDefaultAddress);

// 门店相关路由
const storeController = require('../controllers/storeController');

// 获取用户订阅门店
router.get('/store/subscribed', checkAuth, storeController.getSubscribedStore);

// 获取销售人名下的门店列表
router.get('/store/salesman', checkAuth, storeController.getSalesmanStores);

module.exports = router;
