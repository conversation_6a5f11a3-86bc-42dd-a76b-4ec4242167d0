// app.js
const { userApi } = require('./utils/api');
const loginStateManager = require('./utils/login-state-manager');



// 全局错误处理已移除，避免过度提示用户

App({
  onLaunch: function (options) {
    // 减少日志输出：简化应用启动日志
    // console.log('App Launch');
    // 恢复默认API环境为prod
    try {
      wx.removeStorageSync('apiEnv');
      wx.setStorageSync('apiEnv', 'prod');
      // 减少日志输出：简化API环境切换日志
      // console.log('已切换API环境为远程prod');
    } catch (e) {
      console.warn('设置API环境失败:', e);
    }
    
    // 尝试从本地存储恢复登录状态到全局变量
    try {
      const restored = loginStateManager.restoreLoginStateToGlobal();
      // 减少日志输出：简化登录状态恢复日志
      // console.log('从本地存储恢复登录状态到全局变量:', restored ? '成功' : '无需恢复');
    } catch (e) {
      console.error('恢复登录状态失败:', e);
    }
    // 处理从二维码启动小程序的场景
    if (options.scene === 1047 || options.scene === 1048 || options.scene === 1049) {
      // 扫描小程序码进入
      console.log('通过扫描小程序码进入应用，启动参数:', options);
      if (options.query && options.query.scene) {
        // 解析scene参数
        const scene = decodeURIComponent(options.query.scene);
        console.log('检测到scene参数:', scene);
        
        // 解析新的scene格式：n_storeNo,t_type
        if (scene.includes('n_') && scene.includes('t_')) {
          const parts = scene.split(',');
          const storeInfo = {};
          
          parts.forEach(part => {
            if (part.startsWith('n_')) {
              storeInfo.storeNo = part.substring(2);
            } else if (part.startsWith('t_')) {
              storeInfo.type = part.substring(2);
            }
          });
          
          console.log('解析到的门店信息:', storeInfo);
          
          // 如果是合伙人分享的二维码，保存门店信息
          if (storeInfo.type === 'p' && storeInfo.storeNo) {
            wx.setStorageSync('partnerShareStoreNo', storeInfo.storeNo);
            console.log('保存合伙人分享门店号:', storeInfo.storeNo);
          }
          
          // 兼容旧的推荐人ID格式
          if (scene && !scene.includes('n_') && !scene.includes('t_')) {
            wx.setStorageSync('referrerId', scene);
            console.log('保存推荐人ID:', scene);
          }
        } else {
          // 兼容旧的推荐人ID格式
          wx.setStorageSync('referrerId', scene);
          console.log('保存推荐人ID:', scene);
        }
      }
    }
    
    // 先初始化全局数据
    this.globalData = {
      userInfo: null,
      isLogin: false, // 全局登录状态
      needRefreshProfile: false, // 是否需要刷新"我的"页面
      userIdSet: new Set(), // 已生成的用户ID集合，用于防止重复
      cloudEnvId: 'prod-4g3qet1k59f2d66f', // 始终为prod
      selectedCartItems: null, // 选中的购物车商品数据
      tokenTimestamp: 0, // 添加token时间戳
      theme: {
        primaryColor: '#FF4D4F',
        secondaryColor: '#FFE8E8',
        successColor: '#52C41A',
        warningColor: '#FAAD14',
        errorColor: '#FF4D4F',
        linkColor: '#1890FF',
        backgroundColor: '#F7F7F7',
        textColorPrimary: '#333333',
        textColorRegular: '#666666',
        textColorSecondary: '#999999',
        textColorDisabled: '#CCCCCC'
      }
    };

    // 始终初始化微信云开发环境
    if (wx.cloud) {
      try {
        wx.cloud.init({
          env: this.globalData.cloudEnvId,
          traceUser: true
        });
      } catch (err) {
        try {
          wx.cloud.init({
            env: this.globalData.cloudEnvId,
            traceUser: true
          });
        } catch (e) {}
      }
    }

    // 清除登录弹窗标记，确保应用启动时能够正确显示登录提示
    wx.removeStorageSync('loginModalShown');

    // 获取系统信息，使用异步API避免弃用警告
    try {
      wx.getSystemInfo({
        success: (systemInfo) => {
          this.globalData.systemInfo = systemInfo;
        },
        fail: (err) => {
          console.warn('获取系统信息失败:', err);
          // 设置默认值
          this.globalData.systemInfo = {
            platform: 'unknown',
            pixelRatio: 2,
            windowWidth: 375,
            windowHeight: 667
          };
        }
      });
    } catch (e) {
      console.warn('获取系统信息异常:', e);
      // 设置默认值
      this.globalData.systemInfo = {
        platform: 'unknown',
        pixelRatio: 2,
        windowWidth: 375,
        windowHeight: 667
      };
    }
    
    // 注意：我们不在启动时检查登录状态，这样可以确保首页和快捷菜单等功能不依赖登录状态
    // 用户可以在需要登录的功能处再进行登录
    // 减少日志输出：简化启动完成日志
    // console.log('应用启动完成，不进行登录状态检查，确保首页功能正常加载');
  },

  onShow: function() {
    // 记录当前页面路径（减少日志输出）
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      const currentRoute = currentPage.route;
      // 减少日志输出：简化页面路径日志
      // console.log('当前页面路径:', currentRoute);
      wx.setStorageSync('lastPage', currentRoute);
    }
    
    // 检查快捷通道设置
    this.checkQuickAccess();
  },
  
  // 检查快捷通道设置
  checkQuickAccess: function() {
    // 检查是否开启了快捷通道
    const quickAccessEnabled = wx.getStorageSync('partnerQuickAccess');
    if (!quickAccessEnabled) {
      return;
    }
    
    // 检查当前是否在首页（避免重复跳转）
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      const currentRoute = currentPage.route;
      
      // 如果已经在合伙人端页面，不需要跳转
      if (currentRoute && currentRoute.includes('partner/')) {
        return;
      }
      
      // 如果不在首页，也不进行跳转（避免干扰用户正常使用）
      if (currentRoute !== 'pages/home/<USER>' && currentRoute !== 'pages/splash/splash') {
        return;
      }
    }
    
    // 检查登录状态和用户角色
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    
    if (!userInfo || !token) {
      console.log('快捷通道：用户未登录，不执行跳转');
      return;
    }
    
    // 检查用户是否有合伙人角色
    if (!userInfo.roles || !Array.isArray(userInfo.roles)) {
      console.log('快捷通道：用户角色信息不完整');
      return;
    }
    
    const partnerRole = userInfo.roles.find(role => role.role_type === 'partner');
    if (!partnerRole) {
      console.log('快捷通道：用户不是合伙人，不执行跳转');
      return;
    }
    
    console.log('快捷通道：条件满足，跳转到合伙人端');
    
    // 延迟跳转，避免与页面加载冲突
    setTimeout(() => {
      wx.reLaunch({
        url: '/partner/partner/partner'
      }).catch(err => {
        console.error('快捷通道跳转失败:', err);
      });
    }, 500);
  },
  // 检查登录状态
  checkLoginStatus: function() {
    const that = this;
    // App全局检查登录状态
    console.log('开始全局检查登录状态');
    
    // 记录是否有推荐人ID，用于调试
    const referrerId = wx.getStorageSync('referrerId');
    if (referrerId) {
      console.log('全局检查到推荐人ID:', referrerId);
    }

    // 首先尝试从本地存储获取登录状态
    const loginState = loginStateManager.getLoginState();
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    const tokenTimestamp = wx.getStorageSync('tokenTimestamp') || (loginState ? loginState.timestamp : 0) || 0;

    console.log('本地登录状态检查:', !!loginState, !!userInfo, !!token, '时间戳:', tokenTimestamp);

    // 如果本地有登录状态，先使用本地状态快速更新全局状态
    if (loginState && loginState.isLogin && userInfo && token) {
      // 从本地存储找到登录状态
      console.log('从本地存储找到完整登录状态');

      // 更新全局状态
      that.globalData.userInfo = userInfo;
      that.globalData.isLogin = true;
      that.globalData.token = token;
      that.globalData.tokenTimestamp = tokenTimestamp;

      // 标记需要刷新个人中心页面
      that.globalData.needRefreshProfile = true;
      
      // 检查token是否过期（如果token存在超过24小时，则可能过期）
      const now = Date.now();
      const tokenAge = now - tokenTimestamp;
      const ONE_DAY = 24 * 60 * 60 * 1000; // 24小时，单位毫秒
      
      console.log('token年龄(小时):', tokenAge / (60 * 60 * 1000));
      
      // 检查当前页面是否为订单页面
      const pages = getCurrentPages();
      const isOrderPage = pages.some(page => page.route && page.route.includes('/pages/order/'));
      const TOKEN_AGE_LIMIT = isOrderPage ? 12 * 60 * 60 * 1000 : ONE_DAY; // 订单页面12小时，其他页面24小时
      
      if (tokenAge > TOKEN_AGE_LIMIT) {
        console.warn(`登录状态可能已过期(${isOrderPage ? '订单页面' : '普通页面'})，将进行服务器验证`);
        // 初始化登录状态
        this.initLoginState();
      } else {
        console.log('本地token未过期，无需服务器验证');
      }
    } else {
      console.log('本地登录状态不完整或不存在');
    }
  },

  // 初始化登录状态
  initLoginState: function() {
    const that = this;
    console.log('开始初始化登录状态');
    // 使用登录状态管理器验证登录状态，但不阻塞UI
    loginStateManager.validateLoginState()
      .then(result => {
        // 登录状态验证结果
        console.log('登录状态验证结果:', result.isValid ? '有效' : '无效', result.message);

        if (result.isValid) {
          // 登录状态有效
          console.log('登录状态有效，更新全局状态');

          // 更新全局用户信息
          that.globalData.userInfo = result.userInfo;
          that.globalData.isLogin = true;
          
          // 获取token
          const token = wx.getStorageSync('token');
          if (token) {
            that.globalData.token = token;
          }
          
          // 更新token时间戳
          if (result.usingLocalState) {
            // 如果使用的是本地状态，保留原有时间戳
            const tokenTimestamp = wx.getStorageSync('tokenTimestamp');
            if (tokenTimestamp) {
              that.globalData.tokenTimestamp = tokenTimestamp;
            }
          } else {
            // 如果是服务器验证通过，更新时间戳为当前时间
            const now = Date.now();
            that.globalData.tokenTimestamp = now;
            wx.setStorageSync('tokenTimestamp', now);
            console.log('服务器验证通过，更新token时间戳:', now);
          }

          // 标记需要刷新个人中心页面
          that.globalData.needRefreshProfile = true;
        } else if (!result.usingLocalState) {
          // 登录状态无效，但不立即清除，而是检查是否有本地状态可用
          console.log('登录状态无效，检查是否有可用的本地状态');

          // 如果是由于网络问题导致的验证失败，尝试使用本地状态
          const loginState = loginStateManager.getLoginState();
          const userInfo = wx.getStorageSync('userInfo');
          const token = wx.getStorageSync('token');
          const tokenTimestamp = wx.getStorageSync('tokenTimestamp');
          
          if (loginState && userInfo && token) {
            // 尝试使用本地登录状态
            console.log('使用本地登录状态');

            // 更新全局状态
            that.globalData.userInfo = userInfo;
            that.globalData.isLogin = true;
            that.globalData.token = token;
            that.globalData.tokenTimestamp = tokenTimestamp || loginState.timestamp || Date.now();

            // 标记需要刷新个人中心页面
            that.globalData.needRefreshProfile = true;
          } else {
            // 如果没有可用的本地状态，才清除登录状态
            console.log('没有可用的本地状态，清除登录状态');
            loginStateManager.clearLoginState();

            // 更新全局状态
            that.globalData.userInfo = null;
            that.globalData.isLogin = false;
            that.globalData.token = null;
            that.globalData.tokenTimestamp = 0;

            // 如果是由于ID不一致导致的无效，显示提示
            if (result.message === '用户ID不一致') {
              wx.showModal({
                title: '登录状态异常',
                content: '检测到登录状态异常，将重新登录以确保数据一致性',
                showCancel: false,
                success: () => {
                  // 跳转到登录页
                  wx.redirectTo({
                    url: '/pages/auth/auth'
                  });
                }
              });
            }
          }
        }
      })
      .catch(err => {
        // 验证登录状态出错
        console.error('验证登录状态出错:', err);

        // 网络错误时，尝试使用本地状态
        const loginState = loginStateManager.getLoginState();
        const userInfo = wx.getStorageSync('userInfo');
        const token = wx.getStorageSync('token');
        const tokenTimestamp = wx.getStorageSync('tokenTimestamp') || (loginState ? loginState.timestamp : 0);
        
        console.log('网络错误，检查本地登录状态:', !!loginState, !!userInfo, !!token, '时间戳:', tokenTimestamp);
        
        if (loginState && userInfo && token) {
          // 网络错误，使用本地登录状态
          console.log('网络错误，使用本地完整登录状态');

          // 更新全局状态
          that.globalData.userInfo = userInfo;
          that.globalData.isLogin = true;
          that.globalData.token = token;
          that.globalData.tokenTimestamp = tokenTimestamp || Date.now();

          // 标记需要刷新个人中心页面
          that.globalData.needRefreshProfile = true;
        } else {
          // 如果没有可用的本地状态，才清除登录状态
          console.log('网络错误且无可用本地状态，清除登录状态');
          loginStateManager.clearLoginState();

          // 更新全局状态
          that.globalData.userInfo = null;
          that.globalData.isLogin = false;
          that.globalData.token = null;
          that.globalData.tokenTimestamp = 0;
        }
      });
  },

  // 登录方法
  login: function(code, callback) {
    const that = this;

    // 显示加载中
    wx.showLoading({
      title: '登录中',
      mask: true
    });

    // 使用登录状态管理器登录
    loginStateManager.login(code, userApi.login)
      .then(res => {
        wx.hideLoading();

        if (res.success) {
          // 更新全局数据
          that.globalData.userInfo = res.data.userInfo;
          that.globalData.isLogin = true;
          that.globalData.needRefreshProfile = true; // 标记需要刷新个人中心页面

          console.log('登录成功，用户ID:', res.data.userInfo.id);

          // 触发页面刷新事件，通知首页刷新数据
          const pages = getCurrentPages();
          if (pages.length > 0) {
            const currentPage = pages[pages.length - 1];
            // 如果当前页面是首页，则刷新数据
            if (currentPage.route === 'pages/home/<USER>') {
              currentPage.onLoad(currentPage.options);
            } else {
              // 查找首页实例并刷新
              const homePage = pages.find(page => page.route === 'pages/home/<USER>');
              if (homePage) {
                homePage.onLoad(homePage.options);
              }
            }
          }

          if (callback) {
            callback(true, res.data);
          }
        } else {
          console.warn('登录失败:', res.message);

          if (callback) {
            callback(false, res.message);
          }
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('登录失败:', err);

        if (callback) {
          callback(false, '网络错误，请稍后再试');
        }
      });
  },

  // 检查是否需要登录
  checkNeedLogin: function(callback) {
    console.log('检查登录状态 - 全局登录状态:', this.globalData.isLogin);
    
    // 如果已登录，检查token是否过期
    if (this.globalData.isLogin && this.globalData.userInfo && this.globalData.token) {
      // 检查token是否过期
      const now = Date.now();
      const tokenTimestamp = this.globalData.tokenTimestamp || 0;
      const tokenAge = now - tokenTimestamp;
      const ONE_DAY = 24 * 60 * 60 * 1000; // 24小时，单位毫秒
      
      console.log('token年龄(小时):', tokenAge / (60 * 60 * 1000));
      
      // 如果token未过期，直接执行回调
      if (tokenAge <= ONE_DAY) {
        console.log('已登录且token未过期，直接执行操作');
        if (callback && typeof callback === 'function') {
          callback(true);
        }
        return;
      } else {
        console.log('token已过期，需要重新验证');
      }
    }

    // 首先尝试从本地存储获取登录状态
    const loginState = loginStateManager.getLoginState();
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    const tokenTimestamp = wx.getStorageSync('tokenTimestamp') || (loginState ? loginState.timestamp : 0) || 0;

    // 如果本地有完整登录状态，检查token是否过期
    if (loginState && loginState.isLogin && userInfo && token) {
      console.log('从本地存储检测到登录状态');
      
      // 检查token是否过期
      const now = Date.now();
      const tokenAge = now - tokenTimestamp;
      const ONE_DAY = 24 * 60 * 60 * 1000; // 24小时，单位毫秒
      
      // 如果token未过期，更新全局状态并执行回调
      if (tokenAge <= ONE_DAY) {
        console.log('本地token未过期，更新全局状态');
        // 更新全局状态
        this.globalData.userInfo = userInfo;
        this.globalData.isLogin = true;
        this.globalData.token = token;
        this.globalData.tokenTimestamp = tokenTimestamp;

        if (callback && typeof callback === 'function') {
          callback(true);
        }
        return;
      } else {
        console.log('本地token已过期，需要重新验证');
      }
    }

    // 使用登录状态管理器验证登录状态
    const that = this;
    loginStateManager.validateLoginState()
      .then(result => {
        console.log('登录状态验证结果:', result);
        
        if (result.isValid) {
          // 登录状态有效
          that.globalData.userInfo = result.userInfo;
          that.globalData.isLogin = true;
          
          // 获取token
          const token = wx.getStorageSync('token');
          if (token) {
            that.globalData.token = token;
          }
          
          // 更新token时间戳
          if (result.usingLocalState) {
            // 如果使用的是本地状态，保留原有时间戳
            const tokenTimestamp = wx.getStorageSync('tokenTimestamp');
            if (tokenTimestamp) {
              that.globalData.tokenTimestamp = tokenTimestamp;
            }
          } else {
            // 如果是服务器验证通过，更新时间戳为当前时间
            const now = Date.now();
            that.globalData.tokenTimestamp = now;
            wx.setStorageSync('tokenTimestamp', now);
            console.log('服务器验证通过，更新token时间戳:', now);
          }

          if (callback && typeof callback === 'function') {
            callback(true);
          }
        } else {
          // 登录状态无效，显示登录提示
          console.log('登录状态无效，显示登录提示');
          that.showLoginModal(callback);
        }
      })
      .catch(err => {
        console.error('验证登录状态出错:', err);

        // 网络错误时，再次尝试使用本地状态
        if (loginState && userInfo && token) {
          console.log('网络错误，使用本地登录状态');
          // 更新全局状态
          that.globalData.userInfo = userInfo;
          that.globalData.isLogin = true;
          that.globalData.token = token;
          that.globalData.tokenTimestamp = tokenTimestamp;

          if (callback && typeof callback === 'function') {
            callback(true);
          }
        } else {
          // 显示登录提示
          console.log('没有可用的登录状态，显示登录提示');
          that.showLoginModal(callback);
        }
      });
  },

  // 显示登录提示
  showLoginModal: function(callback) {
    console.log('显示登录提示...');
    wx.showModal({
      title: '提示',
      content: '该操作需要登录，是否前往登录？',
      confirmText: '去登录',
      cancelText: '取消',
      success: function(res) {
        if (res.confirm) {
          console.log('用户选择去登录');
          // 跳转到登录页
          wx.navigateTo({
            url: '/pages/auth/auth'
          });
        } else {
          console.log('用户取消登录');
        }

        if (callback && typeof callback === 'function') {
          callback(false);
        }
      }
    });
  }
});
