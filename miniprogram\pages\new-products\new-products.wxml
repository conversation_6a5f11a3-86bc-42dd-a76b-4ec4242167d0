<!--pages/new-products/new-products.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <icon class="search-icon" type="search" size="16" color="#999"></icon>
      <input class="search-input" placeholder="搜索新品商品" value="{{searchKeyword}}" bindinput="onSearchInput" bindconfirm="onSearchConfirm" />
    </view>
  </view>

  <!-- 筛选栏 -->
  <view class="filter-bar">
    <view class="filter-left">
      <view class="filter-item {{sortType === 'default' ? 'active' : ''}}" bindtap="onSortTap" data-type="default">
        <text>默认</text>
      </view>
      <view class="filter-item {{sortType === 'price_asc' || sortType === 'price_desc' ? 'active' : ''}}" bindtap="onSortTap" data-type="price">
        <text>价格</text>
        <view class="sort-icon">
          <image src="../../images/icons2/向上.svg" class="{{sortType === 'price_asc' ? 'active' : ''}}"></image>
          <image src="../../images/icons2/向下.svg" class="{{sortType === 'price_desc' ? 'active' : ''}}"></image>
        </view>
      </view>
    </view>
    <view class="filter-right">
      <view class="filter-item" bindtap="showFilter">
        <text>筛选</text>
        <image class="filter-icon" src="../../images/icons2/筛选.svg"></image>
      </view>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="product-list">
    <view class="product-item" wx:for="{{products}}" wx:key="id" bindtap="onProductTap" data-id="{{item.id}}">
      <!-- 商品图片 -->
      <view class="product-image-wrapper">
        <image class="product-image" src="{{imgErrorMap[item.id] ? '/images/mo/mogoods.jpg' : (item.imageUrl || '/images/mo/mogoods.jpg')}}" mode="aspectFill" binderror="onProductImgError" data-id="{{item.id}}"></image>
        <!-- 新品标签 -->
        <view class="new-tag">新品</view>
      </view>
      
      <!-- 商品信息 -->
      <view class="product-info">
        <view class="product-name">{{item.name}}</view>
        <view class="product-desc" wx:if="{{item.description}}">{{item.description}}</view>
        
        <!-- 划线价格（单独一行） -->
        <view class="original-price-row" wx:if="{{item.originalPrice && item.originalPrice > item.price}}">
          <view class="original-price">¥{{item.originalPrice}}</view>
        </view>
        
        <!-- 商品标签 -->
        <view class="product-tags" wx:if="{{item.tags && item.tags.length > 0}}">
          <text class="tag" wx:for="{{item.tags}}" wx:key="index" wx:for-item="tag">{{tag}}</text>
        </view>
        
        <!-- 零售价与按钮同行 -->
        <view class="price-actions-row">
          <view class="current-price">¥{{item.price}}</view>
          <view class="product-actions">
            <view class="action-btn favorite-btn" catchtap="onFavoriteTap" data-id="{{item.id}}">
              <image class="action-icon" 
                     src="{{favoriteStatus[item.id] ? '/images/icons2/已收藏.svg' : '/images/icons2/未收藏_red.svg'}}"
                     wx:if="{{!favoriteLoading[item.id]}}"></image>
              <view class="loading-spinner" wx:if="{{favoriteLoading[item.id]}}"></view>
            </view>
            <view class="action-btn cart-btn" catchtap="onAddToCartTap" data-id="{{item.id}}">
              <image class="action-icon" src="/images/icons2/添加_red.svg"></image>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-wrapper" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!hasMore && products.length > 0}}">
    <text>没有更多商品了</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && products.length === 0}}">
    <image class="empty-icon" src="/images/icons2/空.svg"></image>
    <text class="empty-text">暂无新品商品</text>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel-mask" wx:if="{{showFilter}}" bindtap="hideFilter"></view>
  <view class="filter-panel {{showFilter ? 'show' : ''}}">
    <view class="filter-panel-header">
      <text class="filter-panel-title">筛选</text>
      <view class="filter-panel-close" bindtap="hideFilter">
        <image src="/images/icons2/关闭.svg"></image>
      </view>
    </view>
    
    <view class="filter-panel-body">
      <!-- 价格区间 -->
      <view class="filter-section">
        <view class="filter-section-title">价格区间</view>
        <view class="price-range">
          <input class="price-input" type="digit" placeholder="最低价" value="{{filterOptions.minPrice}}" bindinput="inputMinPrice" />
          <text class="price-separator">-</text>
          <input class="price-input" type="digit" placeholder="最高价" value="{{filterOptions.maxPrice}}" bindinput="inputMaxPrice" />
        </view>
      </view>
      
      <!-- 商品特性 -->
      <view class="filter-section">
        <view class="filter-section-title">商品特性</view>
        <view class="filter-options">
          <view class="filter-option {{filterOptions.onlyDiscount ? 'active' : ''}}" bindtap="toggleOnlyDiscount">
            <text>仅显示优惠商品</text>
          </view>
        </view>
      </view>
    </view>
    
    <view class="filter-panel-footer">
      <view class="filter-reset-btn" bindtap="resetFilter">重置</view>
      <view class="filter-apply-btn" bindtap="applyFilter">确定</view>
    </view>
  </view>
</view>