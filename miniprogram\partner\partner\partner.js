const loginStateManager = require('../../utils/login-state-manager');
const { partner<PERSON>pi } = require('../../utils/api');
const partnerErrorHandler = require('../../utils/partner-error-handler');

Page({
  data: {
    userInfo: null,
    isLogin: false,
    loading: true,

    // 资金账户数据
    fundAccount: {
      account_balance: 0.00,
      pending_commission: 0.00,
      total_commission: 0.00,
      total_withdrawal: 0.00,
      total_dividend: 0.00
    },

    // 推荐统计
    referralStats: {
      referral_count: 0,
      store_count: 0
    },

    // 订单统计
    orderStats: {
      pending_payment: 0, // 待付款
      pending_pickup: 0, // 待自提
      pending_shipment: 0, // 待发货
      shipped: 0, // 待收货（已发货）
      completed: 0, // 已完成
      signed: 0, // 已签收
      returns: 0 // 退换货
    },

    // 门店列表 - 确保初始化为空数组
    storeList: [],
    selectedStore: null,

    // 数据加载状态
    dataLoaded: false,
    apiError: false
  },

  onLoad() {
    console.log('=== 合伙人页面 onLoad 开始 ===');
    
    // 首先检查登录状态和合伙人权限
    if (!this.checkLoginAndPartnerPermission()) {
      return; // 如果检查失败，直接返回
    }
    
    // 检查当前用户的身份信息
    this.debugUserRoles();
    
    // 优先从本地存储读取门店信息
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    console.log('本地门店数据检查:', {
      storedStoreList: !!storedStoreList,
      storeCount: storedStoreList ? storedStoreList.length : 0,
      storedSelectedStore: !!storedSelectedStore
    });
    
    if (storedStoreList && storedStoreList.length > 0) {
      const selectedStore = storedSelectedStore || storedStoreList[0];
      this.setData({
        storeList: storedStoreList,
        selectedStore: selectedStore
      });
      console.log('页面加载时从本地存储读取门店信息:', selectedStore);
    }
    
    this.checkLoginStatus();
    console.log('=== 合伙人页面 onLoad 结束 ===');
  },

  // 设置合伙人身份标识
  setPartnerIdentityLabel(userInfo) {
    if (!userInfo || !userInfo.roles) {
      console.warn('用户信息或角色数据不完整');
      return;
    }
    
    // 查找合伙人角色
    const partnerRole = userInfo.roles.find(role => role.role_type === 'partner');
    if (partnerRole) {
      // 设置合伙人身份标识，优先使用role_name，如果为空则使用role_type作为备用
      userInfo.partnerLevel = partnerRole.role_name || partnerRole.role_type;
      console.log('设置合伙人身份标识:', userInfo.partnerLevel);
    } else {
      console.warn(`[警告] 未找到合伙人角色，用户ID: ${userInfo.id}`);
      userInfo.partnerLevel = 'partner'; // 使用role_type作为备用
    }
  },

  // 检查登录状态和合伙人权限
  checkLoginAndPartnerPermission() {
    console.log('=== 检查登录状态和合伙人权限 ===');
    const app = getApp();
    const globalData = app.globalData || {};
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    
    // 检查是否已登录
     if (!token || !userInfo) {
       console.warn('⚠️ 用户未登录，跳转到登录页');
       wx.reLaunch({ url: '/pages/auth/auth?returnTo=partner' });
       return false;
     }
    
    // 检查是否有合伙人权限
    if (userInfo && userInfo.roles) {
      const partnerRole = userInfo.roles.find(r => r.role_type === 'partner');
      if (!partnerRole) {
        console.warn('⚠️ 用户没有合伙人角色！');
        wx.showModal({
          title: '权限提示',
          content: '您当前没有合伙人权限，请联系管理员开通合伙人权限。',
          showCancel: false,
          complete: () => {
            wx.reLaunch({ url: '/pages/profile/profile' });
          }
        });
        return false;
      }
    } else {
      console.warn('⚠️ 用户信息中没有角色数据！');
       wx.showModal({
         title: '权限提示',
         content: '用户权限信息异常，请重新登录。',
         showCancel: false,
         complete: () => {
           wx.reLaunch({ url: '/pages/auth/auth?returnTo=partner' });
         }
       });
      return false;
    }
    
    console.log('=== 登录状态和权限检查通过 ===');
    return true;
  },

  // 调试用户角色信息
  debugUserRoles() {
    console.log('=== 调试用户角色信息 ===');
    const app = getApp();
    const globalData = app.globalData || {};
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    const loginState = loginStateManager.getLoginState();
    
    console.log('全局登录状态:', globalData.isLogin);
    console.log('全局用户信息:', globalData.userInfo);
    console.log('全局当前角色:', globalData.currentRole);
    console.log('本地用户信息:', userInfo);
    console.log('本地token:', !!token);
    console.log('登录状态管理器状态:', loginState);
    
    if (userInfo && userInfo.roles) {
      console.log('用户角色列表:', userInfo.roles);
      const partnerRole = userInfo.roles.find(r => r.role_type === 'partner');
      console.log('合伙人角色:', partnerRole);
      
      if (!partnerRole) {
        console.warn('⚠️ 用户没有合伙人角色！');
        wx.showModal({
          title: '权限提示',
          content: '您当前没有合伙人权限，请联系管理员开通合伙人权限。',
          showCancel: false,
          complete: () => {
            wx.reLaunch({ url: '/pages/profile/profile' });
          }
        });
        return false;
      }
    } else {
      console.warn('⚠️ 用户信息中没有角色数据！');
    }
    
    console.log('=== 调试用户角色信息结束 ===');
    return true;
  },

  // 检查门店数据状态
  checkStoreDataStatus() {
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    console.log('=== 门店数据状态检查 ===');
    console.log('页面数据 - storeList:', this.data.storeList);
    console.log('页面数据 - selectedStore:', this.data.selectedStore);
    console.log('本地存储 - partnerStoreList:', storedStoreList);
    console.log('本地存储 - partnerSelectedStore:', storedSelectedStore);
    console.log('========================');
  },

  onShow() {
    console.log('合伙人页面 onShow');
    
    // 检查门店数据状态
    this.checkStoreDataStatus();
    
    // 检查是否从顾客端切换过来
    const lastPage = wx.getStorageSync('lastPage');
    const isFromCustomerSide = lastPage && lastPage.includes('pages/') && !lastPage.includes('partner/');
    console.log('检测到来源页面:', lastPage, '是否从顾客端切换:', isFromCustomerSide);
    
    this.checkLoginStatus();
    
    // 如果从顾客端切换过来，强制刷新门店数据
    if (isFromCustomerSide && this.data.isLogin) {
      console.log('从顾客端切换过来，强制刷新门店数据');
      this.loadPartnerData(true);
      return;
    }
    
    // 从本地存储读取门店信息，但不覆盖已有的数据
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    if (storedStoreList && storedStoreList.length > 0) {
      const selectedStore = storedSelectedStore || storedStoreList[0];
      
      // 只有当当前页面没有门店数据，或者存储的数据与当前数据不同时才更新
      if (!this.data.storeList || this.data.storeList.length === 0 || 
          !this.data.selectedStore || this.data.selectedStore.id !== selectedStore.id) {
        this.setData({
          storeList: storedStoreList,
          selectedStore: selectedStore
        });
        console.log('从本地存储更新门店信息:', selectedStore);
      } else {
        console.log('门店信息无需更新，保持当前选择:', this.data.selectedStore);
      }
    }
    
    // 再次检查门店数据状态
    this.checkStoreDataStatus();
  },

  checkLoginStatus() {
    console.log('检查登录状态...');
    const app = getApp();
    const globalData = app.globalData || {};
    this.setData({ loading: true });
    
    if (globalData.isLogin && globalData.userInfo) {
      console.log('从全局数据获取登录状态:', globalData.userInfo);
      
      // 设置合伙人身份标识
      const userInfo = { ...globalData.userInfo };
      this.setPartnerIdentityLabel(userInfo);
      
      this.setData({
        userInfo: userInfo,
        isLogin: true,
        loading: false
      });
      this.loadPartnerData();
      return;
    }
    
    const loginState = loginStateManager.getLoginState();
    const userInfo = wx.getStorageSync('userInfo');
    console.log('从本地存储获取登录状态:', { loginState, userInfo });
    
    if (loginState && loginState.isLogin && userInfo) {
      globalData.userInfo = userInfo;
      globalData.isLogin = true;
      
      // 设置合伙人身份标识
      const updatedUserInfo = { ...userInfo };
      this.setPartnerIdentityLabel(updatedUserInfo);
      
      this.setData({
        userInfo: updatedUserInfo,
        isLogin: true,
        loading: false
      });
      this.loadPartnerData();
      return;
    }
    
    loginStateManager.validateLoginState().then(result => {
      console.log('验证登录状态结果:', result);
      if (result.isValid) {
        globalData.userInfo = result.userInfo;
        globalData.isLogin = true;
        
        // 设置合伙人身份标识
        const validatedUserInfo = { ...result.userInfo };
        this.setPartnerIdentityLabel(validatedUserInfo);
        
        this.setData({
          userInfo: validatedUserInfo,
          isLogin: true,
          loading: false
        });
        this.loadPartnerData();
      } else {
        loginStateManager.clearLoginState();
        globalData.userInfo = null;
        globalData.isLogin = false;
        this.setData({
          userInfo: null,
          isLogin: false,
          loading: false
        });
      }
    }).catch((error) => {
      console.error('验证登录状态失败:', error);
      this.setData({
        userInfo: null,
        isLogin: false,
        loading: false
      });
    });
  },

  // 加载合伙人数据
  async loadPartnerData(forceRefreshStores = false) {
    console.log('开始加载合伙人数据，强制刷新门店:', forceRefreshStores);

    if (!this.data.isLogin) {
      console.log('用户未登录，跳过数据加载');
      this.setData({
        loading: false,
        apiError: false,
        dataLoaded: false
      });
      return;
    }

    // 显示加载状态
    partnerErrorHandler.showLoading('加载中...');

    // 重置错误状态
    this.setData({
      apiError: false,
      loading: true
    });

    try {
      // 从本地存储读取门店信息
      const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
      const storedStoreList = wx.getStorageSync('partnerStoreList');
      
      // 如果不强制刷新且本地有门店数据，先使用本地数据
      if (!forceRefreshStores && storedStoreList && storedStoreList.length > 0) {
        console.log('使用本地存储的门店数据');
        this.setData({
          storeList: storedStoreList,
          selectedStore: storedSelectedStore || storedStoreList[0]
        });
      }
      
      // 并行调用所有API，包括推荐统计的详细数据
      const promises = [
        partnerApi.getPartnerStats(),
        partnerApi.getOrderStats(),
        partnerApi.getPartnerStores(),
        partnerApi.getPartnerJoinedStores(),
        // 添加推荐统计的详细API调用
        partnerApi.getMyPartnerReferrals({ page: 1, pageSize: 1 }), // 只获取第一页用于统计
        partnerApi.getMyCustomerReferrals({ page: 1, pageSize: 1 })  // 只获取第一页用于统计
      ];
      
      const results = await Promise.all(promises);
      const [partnerStats, orderStats, partnerStores, joinedStores, partnerReferrals, customerReferrals] = results;
      
      console.log('API响应结果:', {
        partnerStats,
        orderStats,
        partnerStores,
        joinedStores,
        partnerReferrals,
        customerReferrals
      });
      
      if (partnerStats.success) {
        console.log('设置资金账户数据:', partnerStats.data.fundAccount);
        
        // 计算正确的推荐统计数据
        let referralCount = 0;
        if (partnerReferrals.success && customerReferrals.success) {
          const partnerCount = partnerReferrals.data.total || 0;
          const customerCount = customerReferrals.data.total || 0;
          referralCount = partnerCount + customerCount;
          console.log('推荐统计计算:', {
            partnerCount,
            customerCount,
            totalReferralCount: referralCount
          });
        } else {
          console.warn('推荐统计API调用失败，使用原始数据');
          referralCount = partnerStats.data.referralStats.referral_count || 0;
        }
        
        this.setData({
          fundAccount: partnerStats.data.fundAccount,
          referralStats: {
            ...partnerStats.data.referralStats,
            referral_count: referralCount // 使用修正后的推荐人数
          }
        });
      } else {
        console.error('合伙人统计API失败:', partnerStats);
      }
      
      if (orderStats.success) {
        console.log('设置订单统计数据:', orderStats.data);
        this.setData({
          orderStats: orderStats.data
        });
      } else {
        console.error('订单统计API失败:', orderStats);
      }
      
      // 处理门店数据
      // 合并两个API的门店数据，并去重
      let allStores = [];
      let hasStoreApiError = false;
      
      if (partnerStores && partnerStores.success && partnerStores.data) {
        allStores = [...partnerStores.data];
      } else {
        console.error('获取合伙人门店列表失败:', partnerStores);
        hasStoreApiError = true;
      }
      
      if (joinedStores && joinedStores.success && joinedStores.data) {
        // 将joinedStores中的门店添加到allStores中，避免重复
        joinedStores.data.forEach(store => {
          // 检查是否已存在相同store_no的门店
          const existingIndex = allStores.findIndex(s => s.store_no === store.store_no);
          if (existingIndex === -1) {
            // 不存在则添加
            allStores.push(store);
          }
        });
      } else {
        console.error('获取合伙人加入的门店列表失败:', joinedStores);
        hasStoreApiError = true;
      }
      
      console.log('合并后的门店列表数据:', allStores);
      
      if (allStores.length > 0) {
        // 尝试保持之前选择的门店
        let selectedStore = null;
        if (storedSelectedStore) {
          // 在新获取的门店列表中查找之前选择的门店
          const previousSelectedStore = allStores.find(store => 
            store.id === storedSelectedStore.id || store.store_no === storedSelectedStore.store_no
          );
          if (previousSelectedStore) {
            selectedStore = previousSelectedStore;
            console.log('保持之前选择的门店:', selectedStore);
          } else {
            // 如果之前选择的门店不在新列表中，选择第一个
            selectedStore = allStores[0];
            console.log('之前选择的门店不在新列表中，选择第一个:', selectedStore);
          }
        } else {
          // 没有之前的选择，选择第一个门店
          selectedStore = allStores[0];
        }
        
        this.setData({
          storeList: allStores,
          selectedStore: selectedStore
        });
        
        // 保存到本地存储，供其他页面使用
        wx.setStorageSync('partnerStoreList', allStores);
        wx.setStorageSync('partnerSelectedStore', selectedStore);
        console.log('门店数据已保存到本地存储:', {
          storeList: allStores,
          selectedStore: selectedStore
        });
      } else {
        console.warn('没有获取到门店数据');
        
        // 如果API调用失败且本地也没有数据，显示错误提示
        if (hasStoreApiError && (!storedStoreList || storedStoreList.length === 0)) {
          wx.showModal({
            title: '门店数据获取失败',
            content: '无法获取门店信息，请检查网络连接或稍后重试。是否重新加载？',
            confirmText: '重新加载',
            cancelText: '稍后再试',
            success: (res) => {
              if (res.confirm) {
                // 用户选择重新加载，递归调用自己
                this.loadPartnerData(true);
              }
            }
          });
        } else if (!storedStoreList || storedStoreList.length === 0) {
          // 没有门店数据的情况
          wx.showToast({
            title: '暂无门店数据，请联系管理员',
            icon: 'none',
            duration: 3000
          });
        }
      }
      
      console.log('数据加载完成，当前页面数据:', this.data);

      // 设置数据加载完成状态
      this.setData({
        dataLoaded: true,
        loading: false,
        apiError: false
      });

    } catch (error) {
      console.error('加载合伙人数据失败:', error);

      // 设置错误状态
      this.setData({
        apiError: true,
        loading: false,
        dataLoaded: false
      });

      // 检查是否是网络错误
      if (error.code === -1 || error.message.includes('网络')) {
        wx.showModal({
          title: '网络连接失败',
          content: '请检查网络连接后重试',
          confirmText: '重新加载',
          cancelText: '稍后再试',
          success: (res) => {
            if (res.confirm) {
              this.loadPartnerData(true);
            }
          }
        });
      } else if (error.code === 401) {
        // 登录失效，不需要额外处理，request.js已经处理了
        console.log('登录失效，已由request.js处理');
      } else {
        wx.showToast({
          title: error.message || '数据加载失败',
          icon: 'none',
          duration: 3000
        });
      }
    } finally {
      partnerErrorHandler.hideLoading();
      // 确保loading状态被重置
      this.setData({ loading: false });
    }
  },

  // 门店选择
  onStoreChange(e) {
    const index = e.detail.value;
    const selectedStore = this.data.storeList[index];
    
    console.log('门店选择前 - 当前门店:', this.data.selectedStore);
    console.log('门店选择前 - 新选择门店:', selectedStore);
    
    this.setData({ selectedStore });
    
    // 保存到本地存储，供其他页面读取
    wx.setStorageSync('partnerSelectedStore', selectedStore);
    wx.setStorageSync('partnerStoreList', this.data.storeList);
    
    console.log('合伙人页面选择门店:', selectedStore);
    console.log('门店选择后 - 当前门店:', this.data.selectedStore);
  },

  // 跳转到门店管理
  goToStoreManage(e) {
    const type = e.currentTarget.dataset.type;

    console.log('跳转到门店管理，类型:', type);
    console.log('当前门店数据状态:', {
      storeList: this.data.storeList,
      selectedStore: this.data.selectedStore,
      dataLoaded: this.data.dataLoaded,
      apiError: this.data.apiError
    });

    // 检查门店数据
    if (!partnerErrorHandler.checkStoreData(this.data, () => {
      this.loadPartnerData(true).then(() => {
        // 重新加载后再次检查
        if (this.data.storeList && this.data.storeList.length > 0) {
          // 重新调用当前方法
          this.goToStoreManage(e);
        } else {
          partnerErrorHandler.showError('仍无门店数据，请联系管理员');
        }
      });
    })) {
      return;
    }
    
    const storeNo = partnerErrorHandler.getStoreNo(this.data.selectedStore);
    if (!storeNo) {
      partnerErrorHandler.showError('门店信息不完整');
      return;
    }
    
    // 处理不同类型的跳转
    if (type === 'partners') {
      // 跳转到门店合伙人页面
      wx.navigateTo({
        url: `/partner/store-partners/store-partners?storeNo=${storeNo}`
      });
      return;
    } else if (type === 'inventory') {
      // 跳转到门店库存页面
      wx.navigateTo({
        url: `/partner/inventory/inventory?storeNo=${storeNo}`
      });
      return;
    } else if (type === 'orders') {
      // 跳转到门店订单页面
      wx.navigateTo({
        url: `/partner/store-orders/store-orders?storeNo=${storeNo}`
      });
      return;
    } else if (type === 'fund') {
      // 跳转到门店资金页面
      wx.navigateTo({
        url: `/partner/store-funds/store-funds?storeNo=${storeNo}`
      });
      return;
    } else if (type === 'share') {
      // 分享门店功能
      this.shareStore();
      return;
    } else if (type === 'settings') {
      // 门店设置功能
      wx.showToast({
        title: '门店设置功能开发中',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 其他类型的跳转保持原有逻辑
    wx.navigateTo({
      url: `/admin/store/${type}?storeNo=${storeNo}`
    });
  },

  // 分享门店
  shareStore() {
    console.log('[合伙人端] 分享门店');
    
    // 检查门店数据状态
    if (!this.data.storeList || this.data.storeList.length === 0) {
      console.log('[合伙人端] 门店数据为空，尝试重新加载');
      wx.showModal({
        title: '提示',
        content: '门店数据为空，是否重新获取？',
        success: (res) => {
          if (res.confirm) {
            this.loadPartnerData(true).then(() => {
              // 重新加载后再次尝试分享
              this.shareStore();
            }).catch(() => {
              wx.showToast({
                title: '获取门店数据失败',
                icon: 'none'
              });
            });
          }
        }
      });
      return;
    }
    
    if (!this.data.selectedStore) {
      wx.showToast({
        title: '请先选择门店',
        icon: 'none'
      });
      return;
    }
    
    const store = this.data.selectedStore;
    console.log('[合伙人端] 选中的门店信息:', store);
    
    // 将门店信息编码为URL参数
    const storeInfoObj = {
      id: store.id,
      store_no: store.store_no,
      name: store.name,
      address: store.address,
      level: store.level,
      cover_image: store.cover_image
    };
    
    console.log('[合伙人端] 准备传递的门店信息对象:', storeInfoObj);
    
    const storeInfo = encodeURIComponent(JSON.stringify(storeInfoObj));
    console.log('[合伙人端] 编码后的门店信息:', storeInfo);
    
    const shareUrl = `/pages/share/share?source=partner&storeInfo=${storeInfo}`;
    console.log('[合伙人端] 跳转URL:', shareUrl);
    
    // 跳转到分享页面，传递门店信息
    wx.navigateTo({
      url: shareUrl
    }).catch(err => {
      console.error('跳转分享页面失败:', err);
      wx.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    });
  },

  // 跳转到顾客订单页面
  goToOrderManage(e) {
    console.log('跳转到顾客订单管理');
    console.log('当前门店数据状态:', {
      storeList: this.data.storeList,
      selectedStore: this.data.selectedStore,
      dataLoaded: this.data.dataLoaded,
      apiError: this.data.apiError
    });

    // 检查门店数据
    if (!partnerErrorHandler.checkStoreData(this.data, () => {
      this.loadPartnerData(true).then(() => {
        // 重新加载后再次检查
        if (this.data.storeList && this.data.storeList.length > 0) {
          const status = e.currentTarget.dataset.status;
          wx.navigateTo({
            url: `/partner/customer-orders/customer-orders?status=${status}`
          });
        } else {
          partnerErrorHandler.showError('仍无门店数据，请联系管理员');
        }
      });
    })) {
      return;
    }
    
    const status = e.currentTarget.dataset.status;
    wx.navigateTo({
      url: `/partner/customer-orders/customer-orders?status=${status}`
    });
  },

  onSwitchLogin() {
    // 在跳转前确保获取用户的角色信息
    this.ensureUserRolesAndNavigate();
  },

  // 确保用户角色信息完整并跳转到切换登录页面
  ensureUserRolesAndNavigate() {
    const app = getApp();
    const { userApi } = require('../../utils/api');
    
    // 检查全局数据中是否已有完整的角色信息
    if (app.globalData.userRoles && app.globalData.userRoles.length > 0 && app.globalData.currentRole) {
      console.log('角色信息已存在，直接跳转');
      wx.navigateTo({ url: '/pages/switch-login/switch-login' });
      return;
    }
    
    // 显示加载提示
    wx.showLoading({ title: '获取身份信息...' });
    
    // 获取用户角色信息（指定客户端类型为合伙人端）
    userApi.getUserRoles({ clientType: 'partner' })
      .then(res => {
        console.log('获取用户角色信息成功:', res);
        // 修正：检查res.data.roles而不是res.data
        if (res.success && res.data && res.data.roles && res.data.roles.length > 0) {
          // 设置用户角色信息到全局数据
          app.globalData.userRoles = res.data.roles;
          
          // 设置当前角色，优先使用后端返回的currentRole
          if (res.data.currentRole) {
            app.globalData.currentRole = res.data.currentRole;
          } else {
            // 如果后端没有返回currentRole，则查找合伙人角色
            const partnerRole = res.data.roles.find(role => role.role_type === 'partner');
            if (partnerRole) {
              app.globalData.currentRole = partnerRole;
            } else {
              // 如果没有合伙人角色，使用第一个角色
              app.globalData.currentRole = res.data.roles[0];
            }
          }
          
          console.log('已设置全局角色信息:', {
            userRoles: app.globalData.userRoles,
            currentRole: app.globalData.currentRole
          });
          
          wx.hideLoading();
          wx.navigateTo({ url: '/pages/switch-login/switch-login' });
        } else {
          console.error('角色数据格式错误:', res);
          wx.hideLoading();
          wx.showToast({ title: '获取身份信息失败', icon: 'none' });
        }
      })
      .catch(err => {
        console.error('获取用户角色信息失败:', err);
        wx.hideLoading();
        
        // 检查是否是需要重新登录的错误
        if (err.code === 'PREFERRED_ROLE_REQUIRED' || (err.message && err.message.includes('请退出重新登录'))) {
          wx.showModal({
            title: '提示',
            content: '请退出重新登录',
            showCancel: false,
            success: () => {
              // 清除登录状态
              const app = getApp();
              app.globalData.isLogin = false;
              app.globalData.userInfo = null;
              app.globalData.userRoles = [];
              app.globalData.currentRole = null;
              wx.removeStorageSync('userInfo');
              wx.removeStorageSync('token');
              
              // 跳转到登录页
              wx.reLaunch({ url: '/pages/login/login' });
            }
          });
          return;
        }
        
        wx.showToast({ title: '获取身份信息失败', icon: 'none' });
      });
  },

  goToLogin() {
    wx.navigateTo({ url: '/pages/auth/auth' });
  },

  // 其他功能按钮事件处理
  goToAbout() {
    console.log('点击关于我们按钮');
    wx.navigateTo({ url: '/pages/about/index' });
  },

  goToFAQ() {
    console.log('点击常见问题按钮');
    // 跳转到常见问题页面
    wx.navigateTo({
      url: '/pages/faq/faq'
    }).catch(err => {
      console.error('跳转常见问题页面失败:', err);
      wx.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    });
  },

  goToOnlineService() {
    console.log('点击在线客服按钮');
    // 跳转到客服中心页面
    wx.navigateTo({
      url: '/pages/service/index'
    }).catch(() => {
      wx.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    });
  },

  goToAccountSettings() {
    wx.navigateTo({ 
      url: '/pages/settings/account/account'
    });
  },

  // 跳转到合伙人钱包页
  goToWallet() {
    console.log('点击钱包统计区，跳转到合伙人钱包页');
    wx.navigateTo({
      url: '/partner/wallet/wallet'
    }).catch(err => {
      console.error('跳转合伙人钱包页失败:', err);
      wx.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    });
  },

  // 跳转到我的推荐页面
  goToMyReferrals() {
    console.log('点击我的推荐，跳转到我的推荐页面');
    wx.navigateTo({
      url: '/partner/my-referrals/my-referrals'
    }).catch(err => {
      console.error('跳转我的推荐页面失败:', err);
      wx.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    });
  },

  // 跳转到我的门店页面
  goToMyStores() {
    console.log('点击我的门店，跳转到我的门店页面');
    wx.navigateTo({
      url: '/partner/my-stores/my-stores'
    }).catch(err => {
      console.error('跳转我的门店页面失败:', err);
      wx.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    });
  }
});