<!-- admin/partner-applications/application-detail.wxml -->
<view class="container">
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
  
  <block wx:if="{{!loading && application}}">
    <view class="card">
      <view class="card-header">
        <view class="title">申请信息</view>
        <view class="status {{application.status}}">
          {{application.status === 'pending' ? '待审核' : (application.status === 'approved' ? '已通过' : '已拒绝')}}
        </view>
      </view>
      
      <view class="card-body">
        <view class="applicant-info">
          <image class="avatar" src="{{application.avatar || '../../images/profile.png'}}"></image>
          <view class="user-info">
            <view class="name">{{application.nickname || application.name}}</view>
            <view class="nickname" wx:if="{{application.nickname && application.nickname !== application.name}}">真实姓名：{{application.name}}</view>
            <view class="user-id">用户ID：{{application.user_id}}</view>
          </view>
        </view>
        
        <view class="info-item">
          <text class="label">联系电话</text>
          <text class="value phone" bindtap="makePhoneCall">{{application.phone}} <text class="call-icon">📞</text></text>
        </view>
        
        <view class="info-item">
          <text class="label">意向区域</text>
          <text class="value">{{application.province}} {{application.city}} {{application.district}}</text>
        </view>
        
        <view class="info-item">
          <text class="label">申请时间</text>
          <text class="value">{{application.created_at ? wxs.formatDate(application.created_at) : ''}}</text>
        </view>
        
        <view class="info-item" wx:if="{{application.status !== 'pending'}}">
          <text class="label">审核时间</text>
          <text class="value">{{application.updated_at ? wxs.formatDate(application.updated_at) : ''}}</text>
        </view>
        
        <view class="info-item" wx:if="{{application.status === 'rejected' && application.admin_remark}}">
          <text class="label">拒绝理由</text>
          <text class="value remark">{{application.admin_remark}}</text>
        </view>
      </view>
    </view>
    
    <!-- 审核按钮 -->
    <view class="action-buttons" wx:if="{{application.status === 'pending'}}">
      <button class="btn-reject" bindtap="reviewApplication" data-status="rejected" loading="{{submitting}}">拒绝</button>
      <button class="btn-approve" bindtap="reviewApplication" data-status="approved" loading="{{submitting}}">通过</button>
    </view>
  </block>
  
  <!-- 备注输入弹窗 -->
  <view class="remark-modal" wx:if="{{showRemarkInput}}">
    <view class="remark-content">
      <view class="remark-title">请输入拒绝理由</view>
      <textarea class="remark-input" placeholder="请输入拒绝理由，将会展示给申请人" value="{{remark}}" bindinput="onRemarkInput"></textarea>
      <view class="remark-buttons">
        <button class="btn-cancel" bindtap="cancelRemark">取消</button>
        <button class="btn-confirm" bindtap="confirmRemark">确认</button>
      </view>
    </view>
  </view>
</view>

<wxs module="wxs">
  function formatDate(timestamp) {
    if (!timestamp) return '';
    var date = getDate(parseInt(timestamp));
    return date.getFullYear() + '-' + 
           padZero(date.getMonth() + 1) + '-' + 
           padZero(date.getDate()) + ' ' + 
           padZero(date.getHours()) + ':' + 
           padZero(date.getMinutes());
  }
  
  function padZero(num) {
    return num < 10 ? '0' + num : '' + num;
  }
  
  module.exports = {
    formatDate: formatDate
  };
</wxs>