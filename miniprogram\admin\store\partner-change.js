const { storeApi, partnerApi } = require('../../utils/api');
const loginStateManager = require('../../utils/login-state-manager'); // 修正为相对路径

Page({
  data: {
    storeSearchValue: '',
    filteredStoreList: [],
    showStoreDropdown: false,
    selectedStoreIndex: null,
    selectedStore: null,
    partnerList: [],
    loadingPartners: false
  },
  onLoad() {
    // 页面加载时恢复全局登录状态
    loginStateManager.restoreLoginStateToGlobal();
  },
  // 门店搜索输入
  onStoreSearchInput(e) {
    const val = e.detail.value.trim();
    this.setData({ storeSearchValue: val });
    if (val.length < 1) {
      this.setData({ filteredStoreList: [], showStoreDropdown: false });
      return;
    }
    require('../../utils/api').storeApi.searchStores(val).then(res => {
      if (res.success && Array.isArray(res.data)) {
        const list = res.data.map(item => ({ ...item, code: item.code || item.store_no || item.storeNo || '' }));
        this.setData({ filteredStoreList: list, showStoreDropdown: true });
      } else {
        this.setData({ filteredStoreList: [], showStoreDropdown: true });
      }
    }).catch(() => {
      this.setData({ filteredStoreList: [], showStoreDropdown: true });
    });
  },
  // 选中门店
  selectStore(e) {
    const idx = e.currentTarget.dataset.index;
    const store = this.data.filteredStoreList[idx];
    this.setData({
      selectedStoreIndex: idx,
      selectedStore: store,
      storeSearchValue: store.name,
      showStoreDropdown: false
    });
    this.loadPartners(store.code || store.store_no || store.storeNo || '');
  },
  // 点击页面空白处关闭下拉
  onCloseDropdown() {
    this.setData({ showStoreDropdown: false });
  },
  // 加载合伙人列表
  loadPartners(storeNo) {
    this.setData({ loadingPartners: true, partnerList: [] });
    partnerApi.getPartnersByStoreId(storeNo).then(res => {
      if (res.success && Array.isArray(res.data)) {
        this.setData({ partnerList: res.data, loadingPartners: false });
      } else {
        this.setData({ partnerList: [], loadingPartners: false });
      }
    }).catch(() => {
      this.setData({ partnerList: [], loadingPartners: false });
    });
  },
  onAddPartner() {
    wx.navigateTo({
      url: '/admin/partner-join/partner-join'
    });
  }
});