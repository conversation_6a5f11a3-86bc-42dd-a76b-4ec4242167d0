/* components/filter-drawer/filter-drawer.wxss */
/* 商品筛选抽屉样式 */

.filter-drawer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.filter-drawer.show {
  visibility: visible;
  opacity: 1;
}

/* 遮罩层 */
.filter-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.filter-drawer.show .filter-mask {
  opacity: 1;
}

/* 筛选内容 */
.filter-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  max-width: 360px;
  height: 100%;
  background-color: #FFFFFF;
  border-radius: 16px 0 0 16px;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.15);
  padding-right: 16px;
}

.filter-drawer.show .filter-content {
  transform: translateX(0);
}

/* 头部 */
.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40px 20px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.filter-close {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  font-size: 20px;
  color: #999;
}

/* 筛选内容区域 */
.filter-body {
  flex: 1;
  padding: 12px 20px;
  overflow-y: auto;
}

/* 筛选区块 */
.filter-section {
  margin-bottom: 18px;
}

.section-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

/* 价格范围 */
.price-inputs {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  margin-right: 16px;
}

.price-input {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.price-label {
  font-size: 11px;
  color: #666;
  margin-bottom: 3px;
}

.price-value {
  height: 32px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  padding: 0 6px;
  font-size: 13px;
  color: #333;
  background-color: #f8f8f8;
}

.price-separator {
  margin: 0 8px;
  color: #999;
  font-size: 13px;
}

.price-slider {
  margin: 0 22px 0 6px;
}

/* 排序选项 */
.sort-options {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-right: 16px;
}

.sort-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background-color: #f8f8f8;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.sort-option.active {
  background-color: #F0F9FC;
  border: 1px solid #5698c3;
}

.sort-label {
  font-size: 13px;
  color: #333;
}

.sort-check {
  width: 18px;
  height: 18px;
  background-color: #5698c3;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  color: #FFFFFF;
  font-size: 11px;
  font-weight: bold;
}

/* 库存状态 */
.stock-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px 10px 0;
}

.stock-label {
  font-size: 13px;
  color: #333;
}

.stock-switch {
  width: 38px;
  height: 22px;
  background-color: #e0e0e0;
  border-radius: 11px;
  position: relative;
  transition: all 0.3s ease;
}

.stock-switch.active {
  background-color: #5698c3;
}

.switch-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 18px;
  height: 18px;
  background-color: #FFFFFF;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.stock-switch.active .switch-thumb {
  left: 18px;
}

/* 分类选项 */
.category-options {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-right: 16px;
}

.category-option {
  padding: 6px 10px;
  background-color: #f8f8f8;
  border-radius: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.category-option.active {
  background-color: #F0F9FC;
  border: 1px solid #5698c3;
}

.category-label {
  font-size: 11px;
  color: #333;
}

.category-check {
  width: 14px;
  height: 14px;
  background-color: #5698c3;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-check .check-icon {
  color: #FFFFFF;
  font-size: 9px;
  font-weight: bold;
}

/* 底部操作按钮 */
.filter-footer {
  display: flex;
  gap: 12px;
  padding: 20px 30px 30px 20px;
  border-top: 1px solid #f0f0f0;
}

.reset-btn, .apply-btn {
  flex: 1;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
  min-width: 80px;
}

.reset-btn {
  background-color: #f8f8f8;
  color: #666;
  border: 1px solid #e0e0e0;
}

.reset-btn:active {
  background-color: #f0f0f0;
}

.apply-btn {
  background-color: #5698c3;
  color: #FFFFFF;
}

.apply-btn:active {
  background-color: #1e8fa8;
}
