// pages/settings/password.js
const { userApi } = require('../../utils/api');

Page({
  data: {
    username: '', // 用户账号
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
    loading: false,
    showOldPassword: false // 不再需要验证旧密码
  },

  onLoad: function() {
    // 设置顶部导航栏标题
    wx.setNavigationBarTitle({ title: '账号密码设置' });

    // 获取当前用户信息
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      // 设置当前用户名
      this.setData({
        username: userInfo.username || ''
      });
    }
  },

  // 输入用户名
  inputUsername: function(e) {
    let username = e.detail.value;
    // 只允许字母和数字
    username = username.replace(/[^a-zA-Z0-9]/g, '');
    this.setData({
      username: username
    });
  },

  // 输入旧密码
  inputOldPassword: function(e) {
    this.setData({
      oldPassword: e.detail.value
    });
  },

  // 输入新密码
  inputNewPassword: function(e) {
    this.setData({
      newPassword: e.detail.value
    });
  },

  // 确认新密码
  inputConfirmPassword: function(e) {
    this.setData({
      confirmPassword: e.detail.value
    });
  },

  // 保存密码
  savePassword: function() {
    const { username, oldPassword, newPassword, confirmPassword } = this.data;

    // 验证用户名
    if (!username || username.trim() === '') {
      wx.showToast({
        title: '请输入账号',
        icon: 'none'
      });
      return;
    }

    // 验证用户名格式（只能包含字母和数字）
    if (!/^[a-zA-Z0-9]+$/.test(username)) {
      wx.showToast({
        title: '账号只能包含字母和数字',
        icon: 'none'
      });
      return;
    }

    // 验证用户名长度
    if (username.length < 3 || username.length > 20) {
      wx.showToast({
        title: '账号长度应为3-20位',
        icon: 'none'
      });
      return;
    }

    // 不再需要验证旧密码，用户已通过手机验证码验证身份

    if (!newPassword) {
      wx.showToast({
        title: '请输入新密码',
        icon: 'none'
      });
      return;
    }

    if (newPassword.length < 6) {
      wx.showToast({
        title: '新密码长度不能少于6位',
        icon: 'none'
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      wx.showToast({
        title: '两次输入的密码不一致',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    // 检查token是否存在
    const token = wx.getStorageSync('token');
    console.log('修改密码时的令牌:', token ? token.substring(0, 10) + '...' : '未提供');

    if (!token) {
      wx.showToast({
        title: '未登录，请先登录',
        icon: 'none'
      });
      this.setData({ loading: false });

      // 跳转到登录页
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/auth/auth'
        });
      }, 1500);
      return;
    }

    console.log('开始修改密码和用户名');

    this.doUpdatePassword(newPassword, username);
  },

  // 执行密码和用户名修改
  doUpdatePassword: function(newPassword, username) {
    // 获取当前用户信息
    const currentUserInfo = wx.getStorageSync('userInfo');
    const currentUsername = currentUserInfo ? currentUserInfo.username : '';
    
    // 检查用户名是否有变化
    const usernameChanged = username !== currentUsername;
    
    // 构建更新数据
    const updateData = {
      newPassword: newPassword
    };
    
    // 如果用户名有变化，添加到更新数据中
    if (usernameChanged) {
      updateData.username = username;
    }
    
    // 调用更新接口
    userApi.updateUserInfo(updateData)
      .then(res => {
        console.log('修改用户信息响应:', res);
        this.setData({ loading: false });

        if (res.success) {
          // 更新本地用户信息
          if (currentUserInfo) {
            currentUserInfo.username = username;
            wx.setStorageSync('userInfo', currentUserInfo);
          }
          
          // 标记需要刷新个人中心页面
          const app = getApp();
          app.globalData.needRefreshProfile = true;

          // 更新登录状态
          const loginStateManager = require('../../utils/login-state-manager');
          const token = wx.getStorageSync('token');
          const userInfo = wx.getStorageSync('userInfo');

          if (token && userInfo) {
            console.log('更新登录状态');
            loginStateManager.saveLoginState(userInfo, token, true);
          }

          wx.showToast({
            title: usernameChanged ? '账号和密码修改成功' : '密码修改成功',
            icon: 'success'
          });

          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.message || '修改失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('修改用户信息请求失败:', err);
        this.setData({ loading: false });
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  }
})
