// partner/store-orders/store-orders.js
// 合伙人端门店订单页面
const { orderApi, partnerApi, storeApi } = require('../../utils/api');
const app = getApp();

Page({
  data: {
    // 门店相关
    storeList: [],
    selectedStore: null,
    
    // 搜索相关
    searchKeyword: '',
    
    // 排序筛选相关
    sortOptions: [
      { id: 'date', name: '日期', direction: 'desc' },
      { id: 'amount', name: '金额', direction: 'desc' }
    ],
    currentSort: 'date',
    currentSortDirection: 'desc',
    showFilterDrawer: false,
    filterOptions: {
      dateRange: [null, null],
      productKeyword: '',
    },
    
    // 订单分类标签
    orderTypes: [
      { key: 'purchase', name: '门店采购订单' },
      { key: 'transfer', name: '门店移库订单' }
    ],
    currentOrderType: 'purchase',
    
    // 二级分类标签（直接使用中文状态，与数据库保持一致）
    purchaseSubTypes: [
      { key: 'all', name: '全部' },
      { key: '待审核', name: '待审核' },
      { key: '已审核', name: '已审核' },
      { key: '已拒绝', name: '已拒绝' },
      { key: '已取消', name: '已取消' }
    ],
    transferSubTypes: [
      { key: 'all', name: '全部' },
      { key: '待审核', name: '待审核' },
      { key: '已发货', name: '已发货' },
      { key: '已拒绝', name: '已拒绝' },
      { key: '已取消', name: '已取消' },
      { key: '已到店', name: '已到店' }
    ],
    currentSubType: 'all',
    
    // 订单列表
    orderList: [],
    loading: true,
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    isEmpty: false,
    
    // 用户信息
    userInfo: null
  },

  onLoad: function(options) {
    console.log('门店订单页面加载，参数:', options);
    
    // 获取用户信息
    this.setData({
      userInfo: app.globalData.userInfo || {}
    });
    
    // 从本地存储同步门店数据
    this.syncStoreData();
    
    // 延迟加载订单数据，确保门店数据已同步
    setTimeout(() => {
      this.loadOrders();
    }, 100);
  },

  onShow: function() {
    console.log('门店订单页面显示');
    
    // 检查是否从其他页面切换过来，需要刷新数据
    const lastPage = wx.getStorageSync('lastPage');
    const isFromOtherPage = lastPage && !lastPage.includes('store-orders');
    
    if (isFromOtherPage) {
      console.log('从其他页面切换过来，刷新订单数据');
      this.refreshOrders();
    }
    
    // 记录当前页面路径
    wx.setStorageSync('lastPage', 'partner/store-orders/store-orders');
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.refreshOrders().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreOrders();
    }
  },

  /**
   * 同步门店数据
   */
  syncStoreData: function() {
    // 从本地存储获取门店数据
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    console.log('同步门店数据，当前本地存储:', { storedSelectedStore, storedStoreList });
    
    // 如果本地存储中有门店信息，使用本地存储数据
    if (storedStoreList && storedStoreList.length > 0) {
      const selectedStore = storedSelectedStore || storedStoreList[0];
      this.setData({
        storeList: storedStoreList,
        selectedStore: selectedStore
      });
      
      console.log('从本地存储同步门店信息:', {
        selectedStore: selectedStore,
        storeNo: selectedStore?.store_no
      });
      
      // 确保门店数据已设置后再加载订单
      if (selectedStore && selectedStore.store_no) {
        console.log('门店数据已同步，门店编号:', selectedStore.store_no);
      }
      return;
    }
    
    // 否则重新获取门店数据
    console.log('本地存储中没有门店信息，重新获取');
    this.getPartnerStores();
  },

  /**
   * 获取合伙人门店列表
   */
  getPartnerStores: function() {
    // 显示加载状态
    wx.showLoading({
      title: '获取门店数据...',
      mask: true
    });
    
    // 同时调用两个获取门店的API
    return Promise.all([
      partnerApi.getPartnerStores(),
      partnerApi.getPartnerJoinedStores()
    ]).then(([storesRes, joinedStoresRes]) => {
      // 合并两个API的门店数据，并去重
      let allStores = [];
      let hasApiError = false;
      
      if (storesRes && storesRes.success && storesRes.data) {
        allStores = [...storesRes.data];
      } else {
        console.error('获取合伙人门店列表失败:', storesRes);
        hasApiError = true;
      }
      
      if (joinedStoresRes && joinedStoresRes.success && joinedStoresRes.data) {
        // 将joinedStores中的门店添加到allStores中，避免重复
        joinedStoresRes.data.forEach(store => {
          // 检查是否已存在相同store_no的门店
          const existingIndex = allStores.findIndex(s => s.store_no === store.store_no);
          if (existingIndex === -1) {
            // 不存在则添加
            allStores.push(store);
          }
        });
      } else {
        console.error('获取合伙人加入的门店列表失败:', joinedStoresRes);
        hasApiError = true;
      }
      
      console.log('合并后的门店列表数据:', allStores);
      
      if (allStores.length > 0) {
        // 选择第一个门店
        let selectedStore = null;
        
        // 获取本地存储的选中门店，用于后续保持用户选择
        const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
        
        // 尝试保持之前选择的门店
        if (storedSelectedStore) {
          selectedStore = allStores.find(store => 
            store.id === storedSelectedStore.id || store.store_no === storedSelectedStore.store_no
          );
        }
        
        // 如果没有找到匹配的门店，选择第一个
        if (!selectedStore && allStores.length > 0) {
          selectedStore = allStores[0];
        }
        
        this.setData({
          storeList: allStores,
          selectedStore: selectedStore
        });
        
        // 保存到本地存储
        wx.setStorageSync('partnerStoreList', allStores);
        wx.setStorageSync('partnerSelectedStore', selectedStore);
        
        // 门店数据加载完成后，重新加载订单数据
        if (selectedStore) {
          this.loadOrders();
        }
        
        return selectedStore;
      } else {
        // 没有门店数据的处理
        if (hasApiError) {
          // API调用失败
          wx.showModal({
            title: '门店数据获取失败',
            content: '无法获取门店信息，请检查网络连接。是否重新获取？',
            confirmText: '重新获取',
            cancelText: '返回上页',
            success: (res) => {
              if (res.confirm) {
                this.getPartnerStores();
              } else {
                wx.navigateBack();
              }
            }
          });
        } else {
          // API成功但没有门店数据
          wx.showModal({
            title: '暂无门店数据',
            content: '您还没有关联的门店，请联系管理员或返回上一页。',
            confirmText: '返回上页',
            showCancel: false,
            success: () => {
              wx.navigateBack();
            }
          });
        }
      }
    }).catch(err => {
      console.error('获取门店列表失败:', err);
      
      // 根据错误类型显示不同的提示
      if (err.code === -1 || (err.message && err.message.includes('网络'))) {
        wx.showModal({
          title: '网络连接失败',
          content: '请检查网络连接后重试',
          confirmText: '重新获取',
          cancelText: '返回上页',
          success: (res) => {
            if (res.confirm) {
              this.getPartnerStores();
            } else {
              wx.navigateBack();
            }
          }
        });
      } else if (err.code === 401) {
        // 登录失效，不需要额外处理，request.js已经处理了
        console.log('登录失效，已由request.js处理');
      } else {
        wx.showToast({
          title: err.message || '获取门店列表失败',
          icon: 'none',
          duration: 3000
        });
      }
    }).finally(() => {
      wx.hideLoading();
    });
  },

  /**
   * 门店选择
   */
  onStoreChange: function(e) {
    const index = e.detail.value;
    const selectedStore = this.data.storeList[index];
    
    this.setData({ 
      selectedStore,
      pageNum: 1,
      hasMore: true,
      orderList: []
    });
    
    // 保存到本地存储，供其他页面读取
    wx.setStorageSync('partnerSelectedStore', selectedStore);
    
    // 重新加载订单数据
    this.loadOrders();
  },

  /**
   * 加载订单数据
   */
  loadOrders: function() {
    this.setData({
      pageNum: 1,
      hasMore: true,
      orderList: []
    });
    return this.getOrders();
  },

  /**
   * 刷新订单数据
   */
  refreshOrders: function() {
    this.setData({
      pageNum: 1,
      hasMore: true,
      orderList: []
    });
    return this.getOrders();
  },

  /**
   * 加载更多订单
   */
  loadMoreOrders: function() {
    if (!this.data.hasMore || this.data.loading) {
      return Promise.resolve();
    }
    
    this.setData({
      loading: true
    });
    
    return this.getOrders(true).then(res => {
      if (res && res.success) {
        this.setData({
          pageNum: this.data.pageNum + 1
        });
      }
      return res;
    });
  },

  /**
   * 获取订单数据
   */
  getOrders: function(isLoadMore = false) {
    if (!isLoadMore) {
      this.setData({
        loading: true,
        isEmpty: false
      });
    }
    
    const params = {
      page: isLoadMore ? this.data.pageNum + 1 : 1,
      limit: this.data.pageSize,
      keyword: this.data.searchKeyword
    };
    
    // 添加订单类型筛选
    if (this.data.currentOrderType && this.data.currentOrderType !== 'all') {
      params.orderType = this.data.currentOrderType;
    }
    
    // 添加子类型筛选（状态筛选）
    if (this.data.currentSubType && this.data.currentSubType !== 'all') {
      params.subType = this.data.currentSubType;
    }
    
    console.log('当前订单类型筛选:', this.data.currentOrderType);
    console.log('当前子类型筛选:', this.data.currentSubType);
    
    // 只有在有排序字段和方向时才添加排序参数
    if (this.data.currentSort && this.data.currentSortDirection) {
      params.sortBy = this.data.currentSort + '_' + this.data.currentSortDirection;
    }
    
    // 如果选择了特定门店，添加门店参数
    if (this.data.selectedStore && this.data.selectedStore.store_no) {
      params.storeNo = this.data.selectedStore.store_no;
      console.log('使用门店筛选:', this.data.selectedStore.store_no);
    } else {
      console.log('未选择门店，显示所有订单');
    }
    
    // 添加筛选条件
    if (this.data.filterOptions.dateRange && 
        this.data.filterOptions.dateRange[0] && 
        this.data.filterOptions.dateRange[1]) {
      params.startDate = this.data.filterOptions.dateRange[0];
      params.endDate = this.data.filterOptions.dateRange[1];
    }
    
    if (this.data.filterOptions.productKeyword) {
      params.productKeyword = this.data.filterOptions.productKeyword;
    }

    console.log('获取门店订单参数:', params);
    console.log('当前选中门店信息:', this.data.selectedStore);

    // 如果没有选择门店，显示提示但仍然尝试获取数据
    if (!this.data.selectedStore || !this.data.selectedStore.store_no) {
      console.warn('未选择门店，将获取所有订单数据');
      // 不设置storeNo参数，让后端返回所有订单
      delete params.storeNo;
    }

    // 调用门店订单API
    return orderApi.getStoreOrders(params).then(res => {
      console.log('门店订单API响应:', res);
      console.log('响应数据详情:', res?.data);
      
      if (res && res.success && res.data) {
        const newOrders = this.processOrderData(res.data.orders || []);
        const total = res.data.total || 0;
        
        if (isLoadMore) {
          this.setData({
            orderList: [...this.data.orderList, ...newOrders],
            hasMore: this.data.orderList.length + newOrders.length < total,
            loading: false
          });
        } else {
          this.setData({
            orderList: newOrders,
            hasMore: newOrders.length >= this.data.pageSize && newOrders.length < total,
            isEmpty: newOrders.length === 0,
            loading: false
          });
        }
      } else {
        console.error('获取门店订单失败:', res ? res.message : '未知错误');
        this.setData({
          loading: false,
          isEmpty: true
        });
        wx.showToast({
          title: res && res.message ? res.message : '获取订单失败',
          icon: 'none'
        });
      }
      
      return res;
    }).catch(err => {
      console.error('获取门店订单异常:', err);
      this.setData({
        loading: false,
        isEmpty: true
      });
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
      return err;
    });
  },

  /**
   * 处理订单数据，添加显示所需的字段
   */
  processOrderData: function(orders) {
    if (!orders || !Array.isArray(orders)) return [];
    
    return orders.map(order => {
      // 处理订单状态文本
      let statusText = '';
      let orderTypeText = '';
      
      // 设置订单类型文本
      if (order.order_type === 'purchase') {
        orderTypeText = '采购订单';
        // 采购订单状态（根据文档：待审核、已取消、已审核、已拒绝）
      // 统一处理"待审核"、"未审核"状态
        switch(order.status) {
          case 'pending_review':
          case 'yixiadan':
          case 'daishenhe':
          case 'weishenhe':
          case '待审核':
          case '未审核': 
            statusText = '待审核'; 
            break;
          case 'cancelled':
          case 'yiquxiao': 
            statusText = '已取消'; 
            break;
          case 'reviewed':
          case 'yishenhe': 
            statusText = '已审核'; 
            break;
          case 'rejected':
          case 'yijujue': 
            statusText = '已拒绝'; 
            break;
          default: 
            statusText = order.status || '未知状态';
        }
      } else if (order.order_type === 'transfer') {
        orderTypeText = '移库订单';
        // 移库订单状态（根据文档：待审核、已取消、已发货、已拒绝、已到店）
      // 统一处理"待审核"、"未审核"状态
        switch(order.status) {
          case 'pending_review':
          case 'pending_shipment':
          case 'yixiadan':
          case 'daishenhe':
          case 'weishenhe':
          case '待审核':
          case '未审核': 
            statusText = '待审核'; 
            break;
          case 'cancelled':
          case 'yiquxiao': 
            statusText = '已取消'; 
            break;
          case 'shipped':
          case 'yifahuo': 
            statusText = '已发货'; 
            break;
          case 'rejected':
          case 'yijujue': 
            statusText = '已拒绝'; 
            break;
          case 'received':
          case 'yidaodian': 
            statusText = '已到店'; 
            break;
          default: 
            statusText = order.status || '未知状态';
        }
      } else {
        orderTypeText = '普通订单';
        statusText = order.status || '未知状态';
      }
      
      // 处理订单项目，计算金额
      const items = order.items || [];
      items.forEach(item => {
        // 使用采购价格作为商品单价显示（根据业务需求文档）
        const displayPrice = item.purchase_price || item.purchasePrice || item.price || 0;
        item.displayPrice = parseFloat(displayPrice).toFixed(2);
        item.amount = (parseFloat(displayPrice) * parseInt(item.quantity)).toFixed(2);
      });
      
      // 格式化日期时间
      const createdAt = order.created_at ? new Date(order.created_at) : null;
      const reviewTime = order.review_time ? new Date(order.review_time) : null;
      
      // 为中文状态添加对应的CSS类名
      let statusCssClass = '';
      if (order.status === '待审核' || order.status === '未审核' || order.status === 'pending_review' || order.status === 'daishenhe' || order.status === 'weishenhe' || order.status === 'yixiadan') {
        statusCssClass = 'pending_review';
      } else if (order.status === '已取消' || order.status === 'cancelled' || order.status === 'yiquxiao') {
        statusCssClass = 'cancelled';
      } else if (order.status === '已审核' || order.status === 'reviewed' || order.status === 'yishenhe') {
        statusCssClass = 'reviewed';
      } else if (order.status === '已拒绝' || order.status === 'rejected' || order.status === 'yijujue') {
        statusCssClass = 'rejected';
      } else if (order.status === '已发货' || order.status === 'shipped' || order.status === 'yifahuo') {
        statusCssClass = 'shipped';
      } else if (order.status === '已到店' || order.status === 'received' || order.status === 'yidaodian') {
        statusCssClass = 'received';
      } else if (order.status === 'pending_shipment') {
        statusCssClass = 'pending_shipment';
      } else {
        statusCssClass = order.status || 'unknown';
      }
      
      return {
        ...order,
        status_text: statusText,
        order_type_text: orderTypeText,
        status_css_class: statusCssClass,
        items: items,
        created_at: createdAt ? this.formatDateTime(createdAt) : '未知时间',
        review_time: reviewTime ? this.formatDateTime(reviewTime) : '',
        // 确保门店名称和操作人信息存在
        store_name: order.store_name || '未知门店',
        operator_name: order.operator_name || order.operator_nickname || '未知用户',
        operator_id: order.operator_id || order.operator_user_id || 'N/A'
      };
    });
  },
  
  /**
   * 格式化日期时间
   */
  formatDateTime: function(date) {
    if (!date) return '';
    
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  /**
   * 搜索订单
   */
  onSearch: function() {
    console.log('搜索订单:', this.data.searchKeyword);
    this.loadOrders();
  },

  /**
   * 搜索输入
   */
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },
  
  /**
   * 搜索确认
   */
  onSearchConfirm: function() {
    this.onSearch();
  },

  /**
   * 排序方式切换 - 支持三种状态循环：升序、降序、取消排序
   */
  onSortChange: function(e) {
    const sortType = e.currentTarget.dataset.sort;
    console.log('切换排序方式:', sortType);
    
    // 如果点击的是当前排序方式，则按照三种状态循环
    if (sortType === this.data.currentSort) {
      let newSort = '';
      let newDirection = '';
      
      // 三种状态循环：降序 -> 升序 -> 取消排序
      if (this.data.currentSortDirection === 'desc') {
        newSort = sortType;
        newDirection = 'asc';
        console.log('切换为升序');
      } else if (this.data.currentSortDirection === 'asc') {
        newSort = '';
        newDirection = '';
        console.log('取消排序');
      } else {
        newSort = sortType;
        newDirection = 'desc';
        console.log('切换为降序');
      }
      
      this.setData({
        currentSort: newSort,
        currentSortDirection: newDirection
      });
    } else {
      // 如果点击的是不同的排序方式，则更新排序方式并默认为降序
      this.setData({
        currentSort: sortType,
        currentSortDirection: 'desc'
      });
    }
    
    // 重新加载订单数据
    this.refreshOrders();
  },

  /**
   * 显示筛选抽屉
   */
  onShowFilter: function() {
    this.setData({
      showFilterDrawer: true
    });
  },

  /**
   * 隐藏筛选抽屉
   */
  onHideFilter: function() {
    this.setData({
      showFilterDrawer: false
    });
  },

  /**
   * 应用筛选
   */
  onApplyFilter: function() {
    console.log('应用筛选:', this.data.filterOptions);
    this.setData({
      pageNum: 1,
      hasMore: true,
      orderList: []
    });
    
    this.loadOrders();
    this.onHideFilter();
  },

  /**
   * 重置筛选
   */
  onResetFilter: function() {
    this.setData({
      filterOptions: {
        dateRange: [null, null],
        productKeyword: '',
      },
      pageNum: 1,
      hasMore: true,
      orderList: []
    });
    
    this.loadOrders();
    this.onHideFilter();
  },
  
  /**
   * 日期选择
   */
  onDateChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const date = e.detail.value;
    
    if (field === 'start') {
      this.setData({
        'filterOptions.dateRange[0]': date
      });
    } else if (field === 'end') {
      this.setData({
        'filterOptions.dateRange[1]': date
      });
    }
  },
  
  /**
   * 商品关键词输入
   */
  onProductKeywordInput: function(e) {
    this.setData({
      'filterOptions.productKeyword': e.detail.value
    });
  },

  /**
   * 切换订单类型
   */
  switchOrderType: function(e) {
    const type = e.currentTarget.dataset.type;
    console.log('切换订单类型:', type);
    
    if (type === this.data.currentOrderType) {
      return;
    }
    
    this.setData({
      currentOrderType: type,
      currentSubType: 'all',
      pageNum: 1,
      hasMore: true,
      orderList: []
    });
    
    this.loadOrders();
  },

  /**
   * 切换二级分类
   */
  switchSubType: function(e) {
    const subType = e.currentTarget.dataset.subtype;
    console.log('切换二级分类:', subType);
    
    if (subType === this.data.currentSubType) return;
    
    this.setData({
      currentSubType: subType,
      pageNum: 1,
      hasMore: true,
      orderList: []
    });
    
    this.loadOrders();
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail: function(e) {
    const orderId = e.currentTarget.dataset.id;
    const orderType = e.currentTarget.dataset.type || this.data.currentOrderType;
    console.log('查看订单详情:', orderId, '类型:', orderType);
    
    wx.navigateTo({
      url: `/partner/store-orders/detail?id=${orderId}&type=${orderType}`
    });
  },

  /**
   * 取消订单
   */
  cancelOrder: function(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('取消订单:', orderId);
    
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？取消后不可恢复。',
      success: (res) => {
        if (res.confirm) {
          this.performCancelOrder(orderId);
        }
      }
    });
  },

  /**
   * 执行取消订单操作
   */
  performCancelOrder: function(orderId) {
    wx.showLoading({ title: '取消中...' });
    
    orderApi.cancelOrder(orderId).then(res => {
      wx.hideLoading();
      if (res.success) {
        wx.showToast({
          title: '订单已取消',
          icon: 'success'
        });
        // 刷新订单列表
        this.refreshOrders();
      } else {
        wx.showToast({
          title: res.message || '取消失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('取消订单失败:', err);
      wx.showToast({
        title: '取消失败，请重试',
        icon: 'none'
      });
    });
  },

  // 注意：发货操作由管理员执行，合伙人端不提供发货功能

  /**
   * 确认到店
   */
  receiveOrder: function(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('确认到店:', orderId);
    
    wx.showModal({
      title: '确认到店',
      content: '确定商品已到达门店吗？',
      success: (res) => {
        if (res.confirm) {
          this.performReceiveOrder(orderId);
        }
      }
    });
  },

  /**
   * 执行确认到店操作
   */
  performReceiveOrder: function(orderId) {
    wx.showLoading({ title: '确认中...' });
    
    orderApi.receiveOrder(orderId).then(res => {
      wx.hideLoading();
      if (res.success) {
        wx.showToast({
          title: '确认成功',
          icon: 'success'
        });
        // 刷新订单列表
        this.refreshOrders();
      } else {
        wx.showToast({
          title: res.message || '确认失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('确认到店失败:', err);
      wx.showToast({
        title: '确认失败，请重试',
        icon: 'none'
      });
    });
   }
});