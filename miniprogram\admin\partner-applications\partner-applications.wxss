/* admin/partner-applications/partner-applications.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 状态筛选标签样式 */
.status-tabs {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 8rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.tab-item.active {
  background-color: #1890ff;
  color: #fff;
  font-weight: 500;
}

.tab-item:not(.active):hover {
  background-color: #f5f5f5;
}

.applications-list {
  padding-bottom: 30rpx;
}

.application-item {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.application-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.applicant-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.name-phone {
  display: flex;
  flex-direction: column;
}

.name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.phone {
  font-size: 26rpx;
  color: #666;
  margin-top: 6rpx;
}

.user-id {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

.status {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.status.pending {
  background-color: #fef6e6;
  color: #fa8c16;
}

.status.approved {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status.rejected {
  background-color: #fff1f0;
  color: #f5222d;
}

.application-body {
  padding-top: 20rpx;
}

.region, .time {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 28rpx;
}

.label {
  color: #999;
  width: 160rpx;
}

.value {
  color: #333;
  flex: 1;
}

.empty, .loading, .no-more {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}