/* pages/new-products/new-products.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* ==================== 搜索栏 ==================== */
.search-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
  margin: 0;
  padding: 12px 25px;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 12px;
}

.search-icon {
  margin-right: 8px;
}

.search-input {
  flex: 1;
  font-size: 14px;
  color: #333;
}

/* ==================== 筛选栏 ==================== */
.filter-bar {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  z-index: 99;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  margin: 0;
  padding: 12px 25px;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-left {
  display: flex;
  align-items: center;
}

.filter-right {
  display: flex;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
  margin-right: 24px;
  padding: 4px 8px;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.filter-item.active {
  background-color: #999999;
  color: #fff;
}

.filter-item text {
  font-size: 14px;
  color: #666;
}

.filter-item.active text {
  color: #fff;
}

.filter-icon {
  width: 16px;
  height: 16px;
  margin-left: 4px;
}

.sort-icon {
  display: flex;
  flex-direction: column;
  margin-left: 4px;
}

.sort-icon image {
  width: 12px;
  height: 8px;
  opacity: 0.4;
}

.sort-icon image.active {
  opacity: 1;
}

.sort-icon {
  margin-left: 4px;
  font-size: 12px;
  opacity: 0.6;
}

.sort-icon.active {
  opacity: 1;
}

/* ==================== 商品列表 ==================== */
.product-list {
  padding: 0 5px;
  margin-top: 120px;
}

.product-item {
  display: flex;
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 12px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
}

.product-item:active {
  transform: scale(0.98);
}

/* 商品图片 */
.product-image-wrapper {
  position: relative;
  width: 100px;
  height: 100px;
  margin-right: 12px;
  flex-shrink: 0;
}

.product-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  background-color: #f8f8f8;
}

.new-tag {
  position: absolute;
  top: 4px;
  left: 4px;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: bold;
  box-shadow: 0 1px 3px rgba(255, 107, 107, 0.3);
}

/* 商品信息 */
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-desc {
  font-size: 12px;
  color: #999;
  line-height: 1.3;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 划线价格行 */
.original-price-row {
  margin-bottom: 4px;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

/* 零售价与按钮同行 */
.price-actions-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.current-price {
  font-size: 18px;
  font-weight: bold;
  color: #ff4757;
  flex: 1;
}

/* 商品标签 */
.product-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 8px;
}

.tag {
  background-color: #f0f0f0;
  color: #666;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
}

/* ==================== 商品操作按钮 ==================== */
.product-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 32px;
  flex-shrink: 0;
  margin-right: 0px;
}

.action-btn {
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: transparent;
  border: none;
  cursor: pointer;
}

.favorite-btn {
  background: transparent;
  border: none;
}

.favorite-btn:active {
  transform: scale(0.9);
}

.cart-btn {
  background: transparent;
  border: none;
}

.cart-btn:active {
  transform: scale(0.9);
}

.action-icon {
  width: 21px;
  height: 21px;
}

.favorite-btn .action-icon {
  filter: hue-rotate(-10deg) saturate(1.5) brightness(1.1);
  color: #FF3333;
}

.favorite-btn:active .action-icon {
  opacity: 0.8;
}

.cart-btn .action-icon {
  filter: hue-rotate(10deg) saturate(1.3) brightness(1.2);
  color: #FF0000;
}

.cart-btn:active .action-icon {
  opacity: 0.8;
}

/* 加载动画 */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #FF6B35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.tag {
  font-size: 10px;
  color: #666;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 8px;
  line-height: 1;
}

/* ==================== 加载状态 ==================== */
.loading-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loading-text {
  font-size: 14px;
  color: #999;
}

/* ==================== 没有更多 ==================== */
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: #999;
  font-size: 12px;
}

/* ==================== 空状态 ==================== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.empty-icon {
  width: 80px;
  height: 80px;
  opacity: 0.3;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* ==================== 筛选面板 ==================== */
.filter-panel-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.filter-panel {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 80%;
  background-color: #FFFFFF;
  z-index: 1001;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
}

.filter-panel.show {
  transform: translateX(0);
}

.filter-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #F5F5F5;
}

.filter-panel-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.filter-panel-close {
  width: 24px;
  height: 24px;
}

.filter-panel-close image {
  width: 100%;
  height: 100%;
}

.filter-panel-body {
  flex: 1;
  overflow-y: auto;
}

.filter-section {
  padding: 16px;
  border-bottom: 1px solid #F5F5F5;
}

.filter-section-title {
  font-size: 15px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12px;
}

.price-range {
  display: flex;
  align-items: center;
}

.price-input {
  flex: 1;
  height: 36px;
  background-color: #F5F5F5;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
}

.price-separator {
  margin: 0 8px;
  color: #999999;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
}

.filter-option {
  padding: 8px 16px;
  margin: 4px 8px 4px 0;
  background-color: #F5F5F5;
  border-radius: 16px;
  font-size: 14px;
  color: #666666;
  transition: all 0.2s ease;
}

.filter-option.active {
  background-color: #ff6600;
  color: #FFFFFF;
}

.filter-panel-footer {
  display: flex;
  padding: 16px;
  border-top: 1px solid #F5F5F5;
}

.filter-reset-btn {
  flex: 1;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: #F5F5F5;
  color: #666666;
  border-radius: 4px;
  margin-right: 8px;
  font-size: 16px;
}

.filter-apply-btn {
  flex: 2;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: #ff6600;
  color: #FFFFFF;
  border-radius: 4px;
  margin-left: 8px;
  font-size: 16px;
}