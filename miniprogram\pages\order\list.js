// pages/order/list.js
const { orderApi } = require('../../utils/api');

Page({
  data: {
    orderList: [],
    loading: true,
    currentTab: 'all',
    tabs: [
      { key: 'all', name: '全部' },
      { key: 'pending_payment', name: '待付款' },
      { key: 'pending_shipment', name: '待发货' }, // 新增待发货状态
      { key: 'pending_pickup', name: '待自提' }, // 修改为"待自提"
      { key: 'shipped', name: '待收货' },
      { key: 'completed', name: '已完成' },
      { key: 'cancelled', name: '已取消' },
      { key: 'refund', name: '退款/售后' }
    ],
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    isEmpty: false,
    statusMap: {
      'all': '', // 全部订单不传递status参数
      'pending_payment': '待付款', // 使用中文状态值
      'pending_shipment': '待发货', // 新增待发货状态映射
      'pending_pickup': '待发货', // 待自提查询待发货状态，但会通过delivery_method=self过滤
      'shipped': '已发货',
      'completed': '已完成',
      'cancelled': '已取消',
      'refund': '退款/售后'
    },
    // 搜索相关
    searchKeyword: '',
    searchTimer: null,
    isSearching: false,
    // 滑动标识相关
    showLeftIndicator: false,
    showRightIndicator: false
  },

  onLoad: function(options) {
    console.log('订单列表页面加载，参数:', options);
    // 设置当前标签页，支持pending_shipment参数映射为pending_pickup
    if (options && options.type) {
      let tabType = options.type;
      // 将传统的unshipped或pending_shipment映射为待自提
      if (options.type === 'unshipped' || options.type === 'pending_shipment') {
        tabType = 'pending_pickup';
      }
      if (this.data.statusMap[tabType] !== undefined) {
        this.setData({
          currentTab: tabType
        });
      }
    }
    
    // 加载订单数据
    this.loadOrders();
    
    // 初始化滑动标识
    this.checkTabsOverflow();
  },

  onReady: function() {
    // 页面渲染完成后检查滑动标识
    this.checkTabsOverflow();
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.setData({
      orderList: [],
      pageNum: 1,
      hasMore: true,
      isEmpty: false
    });
    this.loadOrders().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreOrders();
    }
  },

  // 切换标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab === this.data.currentTab) return;

    this.setData({
      currentTab: tab,
      orderList: [],
      pageNum: 1,
      hasMore: true,
      isEmpty: false,
      loading: true
    });

    this.loadOrders();
    // 切换后检查是否需要显示滑动指示
    this.checkTabsOverflow();
  },

  // 加载订单数据
  loadOrders: function() {
    const { currentTab, statusMap, pageNum, pageSize, searchKeyword } = this.data;
    const status = statusMap[currentTab];



    this.setData({ loading: true });

    const params = {
      page: pageNum,
      limit: pageSize
    };

    // 只有当status不为空字符串时才添加status参数
    if (status && status !== '') {
      params.status = status;
      console.log('添加状态过滤:', status);
    } else {
      console.log('不添加状态过滤，查询全部订单');
    }

    // 如果是待自提标签，增加配送方式过滤
    if (currentTab === 'pending_pickup') {
      params.delivery_method = 'self';
      console.log('添加配送方式过滤: self');
    }

    // 添加搜索关键词
    if (searchKeyword && searchKeyword.trim()) {
      params.search = searchKeyword.trim();
      console.log('添加搜索关键词:', searchKeyword.trim());
    }

    console.log('最终请求参数:', params);
    console.log('=== 调试信息结束 ===');
    
    return orderApi.getOrders(params).then(res => {
      console.log('订单列表页面 - 获取订单响应:', res);
      
      if (res.success && res.data) {
        let list = [];
        let total = 0;
        
        // 处理不同的返回数据格式
        if (Array.isArray(res.data)) {
          // 如果直接返回数组
          list = res.data;
          total = res.data.length;
        } else if (res.data.list) {
          // 如果返回包含list和total的对象
          list = res.data.list;
          total = res.data.total || list.length;
        } else {
          // 其他情况，尝试处理
          list = res.data.orders || res.data.orderList || [];
          total = res.data.total || list.length;
        }
        
        // 格式化订单数据中的价格信息
        const formattedList = list.map(order => {
          // 处理商品数据，确保字段名一致
          const products = order.products || order.items || [];
          if (Array.isArray(products)) {
            order.items = products.map(product => {
              // 为每个商品添加格式化后的总价
              product.formattedTotalPrice = ((product.price || 0) * (product.quantity || 0)).toFixed(2);
              return product;
            });
            // 保持products字段的兼容性
            order.products = order.items;
          }
          
          // 添加状态文本映射（处理中文状态）
          const statusTextMap = {
            '待支付': '待付款',
            '待付款': '待付款',
            '待发货': order.delivery_method === 'self' ? '待自提' : '待发货',
            '待自提': '待自提',
            '已发货': '待收货',
            '已完成': '已完成',
            '已取消': '已取消',
            '退款/售后': '退款/售后'
          };
          order.status_text = statusTextMap[order.status] || order.status;
          
          return order;
        });
        
        console.log('处理后的订单数据:', { list: formattedList, total });
        
        const hasMore = list.length >= pageSize && pageNum * pageSize < total;
        const isEmpty = list.length === 0 && pageNum === 1;
        
        this.setData({
          orderList: formattedList,
          hasMore,
          isEmpty,
          loading: false
        });
      } else {
        console.warn('订单列表为空或请求失败');
        this.setData({
          isEmpty: true,
          loading: false
        });
      }
    }).catch(err => {
      console.error('获取订单列表失败:', err);
      this.setData({
        loading: false,
        isEmpty: true
      });
      wx.showToast({
        title: '获取订单失败',
        icon: 'none'
      });
    });
  },

  // 加载更多订单
  loadMoreOrders: function() {
    if (!this.data.hasMore || this.data.loading) return;

    const { currentTab, statusMap, pageNum, pageSize, orderList, searchKeyword } = this.data;
    const status = statusMap[currentTab];

    this.setData({
      loading: true,
      pageNum: pageNum + 1
    });

    const params = {
      page: pageNum + 1,
      limit: pageSize
    };

    if (status && status !== '') {
      params.status = status;
    }

    // 如果是待自提标签，增加配送方式过滤
    if (currentTab === 'pending_pickup') {
      params.delivery_method = 'self';
    }

    // 添加搜索关键词
    if (searchKeyword && searchKeyword.trim()) {
      params.search = searchKeyword.trim();
    }

    return orderApi.getOrders(params).then(res => {
      console.log('订单列表页面 - 加载更多订单, 参数:', params);
      
      if (res.success && res.data) {
        let list = [];
        let total = 0;
        
        // 处理不同的返回数据格式
        if (Array.isArray(res.data)) {
          // 如果直接返回数组
          list = res.data;
          total = res.data.length;
        } else if (res.data.list) {
          // 如果返回包含list和total的对象
          list = res.data.list;
          total = res.data.total || list.length;
        } else {
          // 其他情况，尝试处理
          list = res.data.orders || res.data.orderList || [];
          total = res.data.total || list.length;
        }
        
        // 格式化新加载的订单数据中的价格信息
        const formattedList = list.map(order => {
          // 处理商品数据，确保字段名一致
          const products = order.products || order.items || [];
          if (Array.isArray(products)) {
            order.items = products.map(product => {
              // 为每个商品添加格式化后的总价
              product.formattedTotalPrice = ((product.price || 0) * (product.quantity || 0)).toFixed(2);
              return product;
            });
            // 保持products字段的兼容性
            order.products = order.items;
          }
          
          // 添加状态文本映射
          const statusTextMap = {
            '待支付': '待付款',
            '待付款': '待付款',
            '待发货': order.delivery_method === 'self' ? '待自提' : '待发货',
            '待自提': '待自提',
            '已发货': '待收货',
            '已完成': '已完成',
            '已取消': '已取消',
            '退款/售后': '退款/售后'
          };
          order.status_text = statusTextMap[order.status] || order.status;
          
          return order;
        });
        
        console.log('处理后的更多订单数据:', { list: formattedList, total });
        
        const newList = [...orderList, ...formattedList];
        const hasMore = list.length >= pageSize && newList.length < total;
        
        this.setData({
          orderList: newList,
          hasMore,
          loading: false
        });
      } else {
        console.warn('加载更多订单为空或请求失败');
        this.setData({
          loading: false,
          hasMore: false
        });
      }
    }).catch(err => {
      console.error('加载更多订单失败:', err);
      this.setData({
        loading: false
      });
      wx.showToast({
        title: '加载更多失败',
        icon: 'none'
      });
    });
  },

  // 查看订单详情
  viewOrderDetail: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order/detail?id=${orderId}`
    });
  },

  // 取消订单
  cancelOrder: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '提示',
      content: '确定要取消该订单吗？',
      success: (res) => {
        if (res.confirm) {
          orderApi.cancelOrder(orderId).then(res => {
            if (res.success) {
              wx.showToast({
                title: '订单已取消',
                icon: 'success'
              });
              // 刷新订单列表
              this.setData({
                pageNum: 1,
                orderList: []
              });
              this.loadOrders();
            } else {
              wx.showToast({
                title: res.message || '取消失败',
                icon: 'none'
              });
            }
          }).catch(() => {
            wx.showToast({
              title: '取消失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 去支付
  goToPay: function(e) {
    const orderId = e.currentTarget.dataset.id;
    // 添加来源页面参数，标识从【我的订单】页进入
    wx.navigateTo({
      url: `/pages/order/pay?id=${orderId}&from=orderList`
    });
  },

  // 确认收货
  confirmReceipt: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '提示',
      content: '确认已收到商品吗？',
      success: (res) => {
        if (res.confirm) {
          orderApi.confirmReceipt(orderId).then(res => {
            if (res.success) {
              wx.showToast({
                title: '确认收货成功',
                icon: 'success'
              });
              // 刷新订单列表
              this.setData({
                pageNum: 1,
                orderList: []
              });
              this.loadOrders();
            } else {
              wx.showToast({
                title: res.message || '操作失败',
                icon: 'none'
              });
            }
          }).catch(() => {
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 申请退款
  applyRefund: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order/refund?id=${orderId}`
    });
  },

  // 查看物流
  viewLogistics: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order/logistics?id=${orderId}`
    });
  },

  // 去评价
  goToRate: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order/rate?id=${orderId}`
    });
  },

  // 删除订单
  deleteOrder: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.showModal({
      title: '提示',
      content: '确定要删除该订单吗？删除后将无法恢复',
      success: (res) => {
        if (res.confirm) {
          orderApi.deleteOrder(orderId).then(res => {
            if (res.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              // 刷新订单列表
              this.setData({
                pageNum: 1,
                orderList: []
              });
              this.loadOrders();
            } else {
              wx.showToast({
                title: res.message || '删除失败',
                icon: 'none'
              });
            }
          }).catch(() => {
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 查看退款进度
  viewRefundDetail: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order/refund-detail?id=${orderId}`
    });
  },

  // 自提说明弹窗
  showPickupGuide: function() {
    wx.showModal({
      title: '自提说明',
      content: '1. 选择自提后，请前往指定门店取货；\n2. 到店请出示订单二维码或订单号；\n3. 门店营业时间以门店页面为准；\n4. 有疑问请联系在线客服。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 阻止事件冒泡（用于tab-help）
  stopTap: function() {},
  stopPropagation: function(e) {
    // 阻止事件冒泡，防止点击操作按钮时触发订单卡片的点击事件
  },

  // 再次购买
  buyAgain: function(e) {
    const orderId = e.currentTarget.dataset.id;
    // 获取订单详情，然后将商品添加到购物车
    orderApi.getOrderById(orderId).then(res => {
      if (res.success && res.data && res.data.items) {
        // 这里可以实现将订单中的商品重新添加到购物车的逻辑
        // 暂时跳转到首页让用户重新选购
        wx.switchTab({
          url: '/pages/index/index'
        });
        wx.showToast({
          title: '请重新选购商品',
          icon: 'none'
        });
      } else {
        wx.showToast({
          title: '获取订单信息失败',
          icon: 'none'
        });
      }
    }).catch(() => {
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    });
  },

  // 监听顶部tabs滚动，动态显示左右滑动标识
  onTabsScroll: function(e) {
    const { scrollLeft, scrollWidth } = e.detail;
    try {
      const query = wx.createSelectorQuery();
      query.select('.tabs-container').boundingClientRect();
      query.exec((res) => {
        if (!res || !res[0]) return;
        const rect = res[0];
        const containerWidth = rect.width;
        const maxScrollLeft = scrollWidth - containerWidth;
        this.setData({
          showLeftIndicator: scrollLeft > 5,
          showRightIndicator: scrollLeft < maxScrollLeft - 5
        });
      });
    } catch (err) {
      console.warn('计算tabs滚动指示失败:', err);
    }
  },

  // 检查tabs是否溢出容器，决定是否显示右侧指示
  checkTabsOverflow: function() {
    try {
      const query = wx.createSelectorQuery();
      query.select('.tabs').boundingClientRect();
      query.select('.tabs-container').boundingClientRect();
      query.exec((res) => {
        if (!res || res.length < 2) return;
        const tabsRect = res[0];
        const containerRect = res[1];
        if (!tabsRect || !containerRect) return;
        const needScroll = tabsRect.width > containerRect.width + 2;
        this.setData({
          showLeftIndicator: false,
          showRightIndicator: needScroll
        });
      });
    } catch (e) {
      console.warn('检查tabs溢出失败:', e);
    }
  },

  // 搜索输入
  onSearchInput: function(e) {
    const keyword = e.detail.value;
    this.setData({ searchKeyword: keyword });
    
    // 防抖搜索
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer);
    }
    
    this.setData({
      searchTimer: setTimeout(() => {
        this.setData({
          isSearching: true,
          orderList: [],
          pageNum: 1,
          hasMore: true,
          isEmpty: false
        });
        this.loadOrders();
      }, 500) // 500ms防抖
    });
  },

  // 清空搜索
  clearSearch: function() {
    this.setData({
      searchKeyword: '',
      orderList: [],
      pageNum: 1,
      hasMore: true,
      isEmpty: false,
      isSearching: false
    });
    this.loadOrders();
  }
});