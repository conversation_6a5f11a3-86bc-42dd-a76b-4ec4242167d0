const { orderApi } = require('../../utils/api');
const app = getApp();

Page({
  data: {
    // 搜索相关
    searchKeyword: '',

    // 筛选相关
    storeList: [], // 确保初始化为空数组
    selectedStore: 'all',
    selectedStoreLabel: '所有门店',
    selectedStoreIndex: 0, // 添加门店选择器的索引
    deliveryMethod: 'all',
    deliveryMethodLabel: '所有订单',
    deliveryMethodIndex: 0,
    dateRange: 'all',
    dateRangeLabel: '所有日期',
    dateRangeIndex: 0,

    // 订单状态标签
    currentStatus: 'all',
    statusTabs: [
      { key: 'all', name: '全部' },
      { key: '待付款', name: '待付款' },
      { key: '待发货', name: '待发货' },
      { key: '待自提', name: '待自提' },
      { key: '已发货', name: '待收货' },
      { key: '已完成', name: '已完成' },
      { key: '已签收', name: '已签收' },
      { key: '退款/售后', name: '退换货' },
      { key: '已取消', name: '已取消' }
    ],

    // 订单列表
    orderList: [],
    loading: true,
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    isEmpty: false,

    // 用户信息
    userInfo: null,

    // 数据加载状态
    storeDataLoaded: false,
    storeApiError: false
  },

  onLoad: function(options) {
    console.log('合伙人端顾客订单页面加载，参数:', options);
    
    // 获取用户信息
    const userInfo = app.globalData.userInfo || {};
    console.log('当前用户信息:', userInfo);
    
    this.setData({
      userInfo: userInfo
    });
    
    // 恢复订单快捷入口跳转逻辑：根据传入的status参数设置当前状态
    if (options && options.status) {
      console.log('从订单快捷入口跳转，设置状态为:', options.status);
      this.setData({
        currentStatus: options.status
      });
    } else {
      // 如果没有传入status参数，默认为"全部"
      this.setData({
        currentStatus: 'all'
      });
    }
    
    // 加载门店列表
    this.loadStoreList();
    
    // 加载订单数据
    this.loadOrders();
  },

  onShow: function() {
    console.log('合伙人端顾客订单页面显示');
    
    // 检查是否从其他页面切换过来，需要刷新数据
    const lastPage = wx.getStorageSync('lastPage');
    const isFromOtherPage = lastPage && lastPage.includes('partner/') && !lastPage.includes('customer-orders');
    
    if (isFromOtherPage) {
      console.log('从其他页面切换过来，刷新订单数据');
      this.refreshOrders();
    }
    
    // 记录当前页面路径
    wx.setStorageSync('lastPage', 'partner/customer-orders');
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.refreshOrders().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreOrders();
    }
  },

  /**
   * 加载门店列表
   */
  loadStoreList: function() {
    console.log('加载门店列表');
    
    // 直接从API获取最新的门店列表
    this.fetchStoreList();
  },

  /**
   * 从API获取门店列表
   */
  fetchStoreList: function() {
    console.log('从API获取门店列表');

    // 重置错误状态
    this.setData({
      storeApiError: false
    });

    const { partnerApi } = require('../../utils/api');

    // 同时调用两个获取门店的API
    Promise.all([
      partnerApi.getPartnerStores().catch(err => {
        console.error('获取合伙人门店失败:', err);
        return { success: false, data: [] };
      }),
      partnerApi.getPartnerJoinedStores().catch(err => {
        console.error('获取加入门店失败:', err);
        return { success: false, data: [] };
      })
    ]).then(([storesRes, joinedStoresRes]) => {
      // 合并两个API的门店数据，并去重
      let allStores = [];
      
      if (storesRes && storesRes.success && storesRes.data) {
        allStores = [...storesRes.data];
      }
      
      if (joinedStoresRes && joinedStoresRes.success && joinedStoresRes.data) {
        // 将joinedStores中的门店添加到allStores中，避免重复
        joinedStoresRes.data.forEach(store => {
          // 检查是否已存在相同store_no的门店
          const existingIndex = allStores.findIndex(s => s.store_no === store.store_no);
          if (existingIndex === -1) {
            // 不存在则添加
            allStores.push(store);
          }
        });
      }
      
      console.log('合并后的门店列表数据:', allStores);
      
      // 保存到本地存储
      wx.setStorageSync('partnerStoreList', allStores);
      
      // 门店筛选器始终默认为"所有门店"
      let selectedStoreNo = 'all';
      let selectedStoreLabel = '所有门店';
      
      // 转换为下拉选择器格式
      const storeOptions = [
        { value: 'all', label: '所有门店' },
        { value: 'platform', label: '平台总部' }, // 新增平台总部选项
        ...allStores.map(store => ({
          value: store.store_no,
          label: store.name || store.store_name
        }))
      ];
      
      // 注释：不再从本地存储获取之前选择的门店，始终默认为"所有门店"
      // const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
      // if (storedSelectedStore && storedSelectedStore.store_no !== 'all') {
      //   const matchedStore = allStores.find(store => 
      //     store.id === storedSelectedStore.id || store.store_no === storedSelectedStore.store_no
      //   );
      //   
      //   if (matchedStore) {
      //     selectedStoreNo = matchedStore.store_no;
      //     selectedStoreLabel = matchedStore.name || matchedStore.store_name;
      //   }
      // }
      
      this.setData({
        storeList: storeOptions,
        selectedStore: selectedStoreNo,
        selectedStoreLabel: selectedStoreLabel,
        selectedStoreIndex: 0, // 默认选择第一个选项（所有门店）
        storeDataLoaded: true,
        storeApiError: false
      });

      console.log('门店列表已更新:', storeOptions);
    }).catch(err => {
      console.error('获取门店列表失败:', err);

      // 设置错误状态
      this.setData({
        storeApiError: true,
        storeDataLoaded: false
      });

      // 使用默认数据
      const defaultStoreList = [
        { value: 'all', label: '所有门店' }
      ];

      this.setData({
        storeList: defaultStoreList,
        selectedStore: 'all',
        selectedStoreLabel: '所有门店',
        selectedStoreIndex: 0
      });
    });
  },

  /**
   * 加载订单数据
   */
  loadOrders: function() {
    console.log('加载顾客订单数据');
    
    this.setData({
      loading: true,
      orderList: [],
      pageNum: 1,
      hasMore: true,
      isEmpty: false
    });

    this.fetchOrders();
  },

  /**
   * 刷新订单数据
   */
  refreshOrders: function() {
    console.log('刷新订单数据');
    return this.loadOrders();
  },

  /**
   * 加载更多订单
   */
  loadMoreOrders: function() {
    if (!this.data.hasMore || this.data.loading) return;
    
    console.log('加载更多订单');
    this.setData({
      pageNum: this.data.pageNum + 1
    });
    
    this.fetchOrders(true);
  },

  /**
   * 获取订单数据
   */
  fetchOrders: function(isLoadMore = false) {
    // 显示加载状态
    if (!isLoadMore) {
      wx.showLoading({
        title: '加载中...',
        mask: true
      });
    }
    
    const params = {
      page: this.data.pageNum,
      limit: this.data.pageSize,
      status: this.data.currentStatus === 'all' ? '' : this.data.currentStatus,
      // 处理门店筛选逻辑：当选择"平台总部"时，传递特殊标识；选择"所有门店"时传递空值；其他情况传递具体门店编号
      store_no: this.data.selectedStore === 'all' ? '' : (this.data.selectedStore === 'platform' ? 'PLATFORM_HQ' : this.data.selectedStore),
      delivery_method: this.data.deliveryMethod === 'all' ? '' : this.data.deliveryMethod,
      date_range: this.data.dateRange === 'all' ? '' : this.data.dateRange,
      keyword: this.data.searchKeyword
    };

    console.log('获取顾客订单参数:', params);

    // 调用合伙人端顾客订单API
    orderApi.getPartnerCustomerOrders(params).then(res => {
      // 隐藏加载状态
      wx.hideLoading();
      
      console.log('顾客订单数据:', res);
      
      if (res && res.success && res.data) {
        const newOrders = res.data.orders || [];
        const total = res.data.total || 0;
        
        // 处理订单数据，添加显示所需的额外信息
        const processedOrders = newOrders.map(order => {
          // 处理门店名称显示 - 当门店名称为空、未知门店或归属平台总部时，显示"平台总部"
          if (!order.store_name || order.store_name === '未知门店' || order.store_name === '' || order.store_name === null) {
            order.store_name = '平台总部';
          }
          
          // 处理订单状态显示 - 确保与标签页显示一致
          let statusText = '未知状态';
          
          switch(order.status) {
            case '待支付':
            case '待付款':
              statusText = '待付款';
              break;
            case '待发货':
              statusText = '待发货';
              break;
            case '待自提':
              statusText = '待自提';
              break;
            case '已发货':
              statusText = '已发货';
              break;
            case '待收货':
              statusText = '待收货';
              break;
            case '已完成':
              statusText = '已完成';
              break;
            case '已签收':
              statusText = '已签收';
              break;
            case '退款/售后':
              statusText = '退换货';
              break;
            case '已取消':
              statusText = '已取消';
              break;
            default:
              statusText = order.status_text || order.status || '未知状态';
          }
          order.statusText = statusText;
          
          // 处理配送方式显示
          let deliveryText = '未知配送';
          switch(order.delivery_method) {
            case 'express':
              deliveryText = '快递配送';
              break;
            case 'self':
              deliveryText = '门店自提';
              break;
            default:
              deliveryText = order.delivery_text || '未知配送';
          }
          order.deliveryText = deliveryText;
          
          // 处理子订单的门店名称显示
          if (order.sub_orders && order.sub_orders.length > 0) {
            order.sub_orders = order.sub_orders.map(subOrder => {
              if (!subOrder.store_name || subOrder.store_name === '未知门店' || subOrder.store_name === '' || subOrder.store_name === null) {
                subOrder.store_name = '平台总部';
              }
              return subOrder;
            });
          }
          
          // 处理是否为当前用户销售的订单
          const currentUserId = this.data.userInfo.id || this.data.userInfo.userId;
          console.log('当前用户ID:', currentUserId, '订单销售人ID:', order.salesman_id);
          order.isSalesmanOrder = order.salesman_id == currentUserId;
          
          // 处理商品信息，确保字段名与WXML模板一致，优化图片显示
          if (order.items && order.items.length > 0) {
            order.items = order.items.map(item => ({
              id: item.id || Math.random().toString(36).substr(2, 9),
              product_name: item.product_name,
              product_image: item.product_image || item.image_url || item.image, // 优先使用product_image，其次image_url，最后image
              price: item.product_price,
              quantity: item.quantity,
              subtotal: item.subtotal
            }));
          }
          
          // 处理创建时间格式
          if (order.created_at) {
            const date = new Date(order.created_at);
            order.created_at = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
          }
          
          return order;
        });
        
        if (isLoadMore) {
          this.setData({
            orderList: [...this.data.orderList, ...processedOrders],
            hasMore: this.data.orderList.length + processedOrders.length < total,
            loading: false
          });
        } else {
          this.setData({
            orderList: processedOrders,
            hasMore: processedOrders.length === this.data.pageSize,
            isEmpty: processedOrders.length === 0,
            loading: false
          });
        }
      } else {
        console.error('获取顾客订单失败:', res ? res.message : '未知错误');
        this.setData({
          loading: false,
          isEmpty: true
        });
        wx.showToast({
          title: res && res.message ? res.message : '获取订单失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      // 隐藏加载状态
      wx.hideLoading();
      
      console.error('获取顾客订单异常:', err);
      this.setData({
        loading: false,
        isEmpty: true
      });
      wx.showToast({
        title: '网络异常，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 搜索订单
   */
  onSearch: function() {
    console.log('搜索订单:', this.data.searchKeyword);
    this.loadOrders();
  },

  /**
   * 搜索输入
   */
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 门店选择
   */
  onStoreChange: function(e) {
    const selectedIndex = parseInt(e.detail.value);
    console.log('选择门店索引:', selectedIndex);
    
    // 根据索引获取门店信息
    const selectedStoreOption = this.data.storeList[selectedIndex];
    if (!selectedStoreOption) {
      console.error('无效的门店选择索引:', selectedIndex);
      return;
    }
    
    const storeNo = selectedStoreOption.value;
    const selectedStoreLabel = selectedStoreOption.label;
    
    console.log('选择门店:', storeNo, selectedStoreLabel);
    
    this.setData({
      selectedStore: storeNo,
      selectedStoreLabel: selectedStoreLabel,
      selectedStoreIndex: selectedIndex
    });
    
    this.loadOrders();
  },

  /**
   * 配送方式选择
   */
  onDeliveryMethodChange: function(e) {
    const methodIndex = e.detail.value;
    let deliveryMethod = 'all';
    let deliveryMethodLabel = '所有订单';
    
    if (methodIndex == 1) {
      deliveryMethod = 'express';
      deliveryMethodLabel = '快递订单';
    } else if (methodIndex == 2) {
      deliveryMethod = 'self'; // 门店自提配送方式
      deliveryMethodLabel = '门店自提订单';
    }
    
    console.log('选择配送方式:', deliveryMethod);
    
    this.setData({
      deliveryMethod: deliveryMethod,
      deliveryMethodLabel: deliveryMethodLabel,
      deliveryMethodIndex: methodIndex
    });
    
    this.loadOrders();
  },

  /**
   * 日期范围选择
   */
  onDateRangeChange: function(e) {
    const rangeIndex = e.detail.value;
    let dateRange = 'all';
    let dateRangeLabel = '所有日期';
    
    if (rangeIndex == 1) {
      dateRange = 'today';
      dateRangeLabel = '今天';
    } else if (rangeIndex == 2) {
      dateRange = 'yesterday';
      dateRangeLabel = '昨天';
    } else if (rangeIndex == 3) {
      dateRange = 'week';
      dateRangeLabel = '本周';
    } else if (rangeIndex == 4) {
      dateRange = 'month';
      dateRangeLabel = '本月';
    }
    
    console.log('选择日期范围:', dateRange);
    
    this.setData({
      dateRange: dateRange,
      dateRangeLabel: dateRangeLabel,
      dateRangeIndex: rangeIndex
    });
    
    this.loadOrders();
  },

  /**
   * 切换订单状态标签
   */
  switchStatusTab: function(e) {
    const status = e.currentTarget.dataset.status;
    console.log('切换订单状态:', status);
    
    if (status === this.data.currentStatus) return;
    
    this.setData({
      currentStatus: status
    });
    
    this.loadOrders();
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail: function(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('查看订单详情:', orderId);
    
    wx.navigateTo({
      url: `/partner/customer-orders/detail?id=${orderId}`
    });
  },

  /**
   * 返回上一页
   */
  goBack: function() {
    wx.navigateBack();
  }
});