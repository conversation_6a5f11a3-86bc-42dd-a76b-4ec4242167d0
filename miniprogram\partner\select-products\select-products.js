// pages/partner/select-products/select-products.js
// 合伙人端选品页面
const { productApi, partnerApi } = require('../../utils/api');
const request = require('../../utils/request');

Page({
  data: {
    // 系统信息
    statusBarHeight: 0,
    navBarHeight: 44,
    menuButtonInfo: null,
    windowWidth: 0,
    
    // 门店相关
    storeList: [],
    selectedStore: null,
    
    // 搜索相关
    searchKeyword: '',
    searchWidth: 0,
    
    // 轮播图数据
    banners: [],
    
    // 快捷菜单数据
    quickMenus: [],
    
    // 分类数据
    categories: [],
    selectedCategory: null,
    
    // 商品数据
    products: [],
    hotProducts: [], // 热门商品
    newProducts: [], // 新品推荐
    
    // 加载状态
    loading: true,
    refreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // 用户信息
    globalUserInfo: {},
    
    // 商品标签
    productTabs: ['全部'],
    categoryTabs: [], // 保存分类数据用于筛选
    currentProductTab: 0,
    imgErrorMap: {},
    showBackToTop: false,
    
    // 筛选相关
    showFilterDrawer: false,
    filterOptions: {
      priceRange: [0, 1000],
      sortBy: 'default', // default, price_asc, price_desc, sales_desc
      onlyInStock: false,
      selectedCategories: [] // 筛选抽屉中选中的分类ID数组
    }
  },

  /**
   * 页面加载
   */
  onLoad: function (options) {
    console.log('合伙人选品页面加载');
    
    // 获取系统信息
    this.initSystemInfo();
    
    // 优先从本地存储读取门店信息
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    if (storedStoreList && storedStoreList.length > 0) {
      const selectedStore = storedSelectedStore || storedStoreList[0];
      this.setData({
        storeList: storedStoreList,
        selectedStore: selectedStore
      });
      console.log('页面加载时从本地存储读取门店信息:', selectedStore);
      console.log('门店级别:', selectedStore.level);
      
      // 门店数据已加载，可以获取商品数据
      this.initDataWithoutStores();
      this.getNewProducts();
      this.getProductsByTab(0);
    } else {
      // 只有在完全没有门店数据时才获取
      console.log('本地存储没有门店数据，获取门店数据');
      this.getPartnerStores().then(() => {
        // 门店数据获取完成后再获取商品数据
        this.initDataWithoutStores();
        this.getNewProducts();
        this.getProductsByTab(0);
      });
    }
  },

  /**
   * 检查门店数据状态
   */
  checkStoreDataStatus: function() {
    // 只在调试模式下输出详细日志
    if (wx.getStorageSync('debugMode')) {
      const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
      const storedStoreList = wx.getStorageSync('partnerStoreList');
      
      console.log('=== 门店数据状态检查 ===');
      console.log('页面数据 - storeList:', this.data.storeList);
      console.log('页面数据 - selectedStore:', this.data.selectedStore);
      console.log('本地存储 - partnerStoreList:', storedStoreList);
      console.log('本地存储 - partnerSelectedStore:', storedSelectedStore);
      console.log('========================');
    }
  },

  /**
   * 页面显示
   */
  onShow: function() {
    console.log('合伙人选品页面显示');
    
    // 检查是否从顾客端切换过来
    const lastPage = wx.getStorageSync('lastPage');
    const isFromCustomerSide = lastPage && lastPage.includes('pages/') && !lastPage.includes('partner/');
    console.log('检测到来源页面:', lastPage, '是否从顾客端切换:', isFromCustomerSide);
    
    // 如果从顾客端切换过来，强制刷新门店数据
    if (isFromCustomerSide) {
      console.log('从顾客端切换过来，强制刷新门店数据');
      this.getPartnerStores(true); // 强制刷新
      return;
    }
    
    // 从本地存储读取门店信息，但不覆盖已有的数据
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    if (storedStoreList && storedStoreList.length > 0) {
      const selectedStore = storedSelectedStore || storedStoreList[0];
      
      // 只有当当前页面没有门店数据，或者存储的数据与当前数据不同时才更新
      if (!this.data.storeList || this.data.storeList.length === 0 || 
          !this.data.selectedStore || this.data.selectedStore.id !== selectedStore.id) {
        
        // 检查门店级别是否发生变化
        const levelChanged = !this.isStoreLevelSame(this.data.selectedStore, selectedStore);
        console.log('门店级别是否发生变化:', levelChanged, {
          previousLevel: this.data.selectedStore?.level,
          newLevel: selectedStore.level
        });
        
        this.setData({
          storeList: storedStoreList,
          selectedStore: selectedStore
        });
        console.log('从本地存储更新门店信息:', selectedStore);
        console.log('门店级别:', selectedStore.level);
        
        // 如果门店级别发生变化，重新加载商品数据
        if (levelChanged) {
          console.log('门店级别发生变化，重新加载商品数据');
          this.refreshProducts();
        }
      } else {
        console.log('门店信息无需更新，保持当前选择:', this.data.selectedStore);
      }
    }
    
    // 检查门店数据状态
    this.checkStoreDataStatus();
  },

  /**
   * 页面滚动监听，控制回到顶部按钮显示
   */
  onPageScroll: function(e) {
    this.setData({ showBackToTop: e.scrollTop > 300 });
  },

  /**
   * 回到顶部
   */
  onBackToTop: function() {
    wx.pageScrollTo({ scrollTop: 0, duration: 300 });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    console.log('下拉刷新');
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true
    });
    
    this.initData().finally(() => {
      wx.stopPullDownRefresh();
      this.setData({ refreshing: false });
    });
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }
    
    console.log('上拉加载更多商品');
    this.loadMoreProducts();
  },

  /**
   * 初始化系统信息
   */
  initSystemInfo: function() {
    // 使用异步API避免弃用警告
    wx.getSystemInfo({
      success: (systemInfo) => {
        const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
        const searchWidth = systemInfo.windowWidth * 0.7;
        
        wx.setStorageSync('statusBarHeight', systemInfo.statusBarHeight);
        
        this.setData({
          statusBarHeight: systemInfo.statusBarHeight,
          searchWidth: searchWidth,
          menuButtonInfo: menuButtonInfo,
          windowWidth: systemInfo.windowWidth
        });
      },
      fail: (err) => {
        console.warn('获取系统信息失败:', err);
        // 使用默认值
        const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
        this.setData({
          statusBarHeight: 44,
          searchWidth: 375 * 0.7,
          menuButtonInfo: menuButtonInfo,
          windowWidth: 375
        });
      }
    });
  },

  /**
   * 初始化数据
   */
  initData: function() {
    const promises = [
      this.getPartnerStores(),
      this.getBanners(),
      this.getQuickMenus(),
      this.getCategories(),
      this.getProducts(), // 添加获取商品列表
      this.getHotProducts(),
      this.getNewProducts()
    ];
    
    return Promise.all(promises).finally(() => {
      this.setData({ loading: false });
    });
  },

  /**
   * 初始化数据（不包含门店数据）
   */
  initDataWithoutStores: function() {
    const promises = [
      this.getBanners(),
      this.getQuickMenus(),
      this.getCategories(),
      this.getProducts(), // 添加获取商品列表
      this.getHotProducts(),
      this.getNewProducts()
    ];
    
    return Promise.all(promises).finally(() => {
      this.setData({ loading: false });
    });
  },

  /**
   * 获取合伙人门店列表
   */
  getPartnerStores: function(forceRefresh = false) {
    // 获取本地存储的选中门店，用于后续保持用户选择
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    
    console.log('获取门店数据:', forceRefresh ? '强制刷新' : '正常获取');
    
    // 显示加载状态
    wx.showLoading({
      title: '加载门店数据...',
      mask: true
    });
    
    let hasApiError = false; // 标记API调用是否失败
    
    // 同时调用两个获取门店的API
    return Promise.all([
      partnerApi.getPartnerStores().catch(err => {
        console.error('获取合伙人门店失败:', err);
        hasApiError = true;
        return { success: false, data: [] };
      }),
      partnerApi.getPartnerJoinedStores().catch(err => {
        console.error('获取加盟门店失败:', err);
        hasApiError = true;
        return { success: false, data: [] };
      })
    ]).then(([storesRes, joinedStoresRes]) => {
      // 合并两个API的门店数据，并去重
      let allStores = [];
      
      if (storesRes && storesRes.success && storesRes.data) {
        allStores = [...storesRes.data];
      }
      
      if (joinedStoresRes && joinedStoresRes.success && joinedStoresRes.data) {
        // 将joinedStores中的门店添加到allStores中，避免重复
        joinedStoresRes.data.forEach(store => {
          // 检查是否已存在相同store_no的门店
          const existingIndex = allStores.findIndex(s => s.store_no === store.store_no);
          if (existingIndex === -1) {
            // 不存在则添加
            allStores.push(store);
          }
        });
      }
      
      console.log('合并后的门店列表数据:', allStores);
      
      // 如果没有门店数据，根据是否有API错误显示不同提示
      if (allStores.length === 0) {
        if (hasApiError) {
          // API调用失败，显示重试选项
          wx.showModal({
            title: '门店数据获取失败',
            content: '无法获取门店信息，请检查网络连接后重试',
            confirmText: '重新获取',
            cancelText: '稍后再试',
            success: (res) => {
              if (res.confirm) {
                // 用户选择重新获取，递归调用
                this.getPartnerStores(true);
              }
            }
          });
        } else {
          // API调用成功但无数据
          wx.showToast({
            title: '暂无门店数据',
            icon: 'none',
            duration: 2000
          });
        }
        
        this.setData({
          storeList: [],
          selectedStore: null
        });
        
        return null;
      }
      
      // 尝试保持之前选择的门店
      let selectedStore = null;
      if (storedSelectedStore) {
        // 在新获取的门店列表中查找之前选择的门店
        const previousSelectedStore = allStores.find(store => 
          store.id === storedSelectedStore.id || store.store_no === storedSelectedStore.store_no
        );
        if (previousSelectedStore) {
          selectedStore = previousSelectedStore;
          console.log('保持之前选择的门店:', selectedStore);
        } else {
          // 如果之前选择的门店不在新列表中，选择第一个
          selectedStore = allStores.length > 0 ? allStores[0] : null;
          console.log('之前选择的门店不在新列表中，选择第一个:', selectedStore);
        }
      } else {
        // 正常情况，选择第一个门店
        selectedStore = allStores.length > 0 ? allStores[0] : null;
      }
      
      this.setData({
        storeList: allStores,
        selectedStore: selectedStore
      });
      
      // 保存到本地存储
      wx.setStorageSync('partnerStoreList', allStores);
      wx.setStorageSync('partnerSelectedStore', selectedStore);
      console.log('门店数据已保存到本地存储:', {
        storeList: allStores,
        selectedStore: selectedStore
      });
      
      // 门店数据加载完成后，重新加载商品数据
      if (selectedStore) {
        console.log('门店数据加载完成，重新加载商品数据');
        this.refreshProducts();
      }
      
      return selectedStore; // 返回选中的门店
    }).catch(err => {
      console.error('获取门店列表失败:', err);
      
      // 根据错误类型显示不同提示
      if (err.statusCode === 401) {
        wx.showModal({
          title: '登录已失效',
          content: '请重新登录后再试',
          showCancel: false,
          success: () => {
            wx.reLaunch({
              url: '/pages/login/login'
            });
          }
        });
      } else if (err.errMsg && err.errMsg.includes('network')) {
        wx.showModal({
          title: '网络连接失败',
          content: '请检查网络连接后重试',
          confirmText: '重新获取',
          cancelText: '稍后再试',
          success: (res) => {
            if (res.confirm) {
              this.getPartnerStores(true);
            }
          }
        });
      } else {
        wx.showToast({
          title: '获取门店数据失败',
          icon: 'none',
          duration: 2000
        });
      }
      
      throw err;
    }).finally(() => {
      // 隐藏加载状态
      wx.hideLoading();
    });
  },

  /**
   * 检查门店级别是否相同
   * @param {Object} store1 门店1
   * @param {Object} store2 门店2
   * @returns {Boolean} 级别是否相同
   */
  isStoreLevelSame: function(store1, store2) {
    if (!store1 || !store2) {
      return false;
    }
    return store1.level === store2.level;
  },

  /**
   * 门店选择
   */
  onStoreChange: function(e) {
    const index = e.detail.value;
    const selectedStore = this.data.storeList[index];
    const previousStore = this.data.selectedStore;
    
    console.log('=== 门店切换开始 ===');
    console.log('门店选择前 - 当前门店:', previousStore);
    console.log('门店选择前 - 新选择门店:', selectedStore);
    
    // 检查门店级别是否发生变化
    const levelChanged = !this.isStoreLevelSame(previousStore, selectedStore);
    console.log('门店级别对比:', {
      previousStore: previousStore ? {
        id: previousStore.id,
        name: previousStore.name,
        level: previousStore.level
      } : null,
      newStore: {
        id: selectedStore.id,
        name: selectedStore.name,
        level: selectedStore.level
      },
      levelChanged: levelChanged
    });
    
    this.setData({ selectedStore });
    
    // 保存到本地存储，供其他页面读取
    wx.setStorageSync('partnerSelectedStore', selectedStore);
    wx.setStorageSync('partnerStoreList', this.data.storeList);
    
    console.log('门店选择后 - 当前门店:', this.data.selectedStore);
    
    // 只有门店级别发生变化时才重新加载商品数据
    if (levelChanged) {
      console.log('门店级别发生变化，重新加载商品数据');
      this.refreshProducts();
    } else {
      console.log('门店级别未变化，无需重新加载商品数据');
      // 可以显示一个提示，告知用户采购价信息未变化
      wx.showToast({
        title: '采购价信息未变化',
        icon: 'none',
        duration: 1500
      });
    }
    
    console.log('=== 门店切换完成 ===');
  },

  /**
   * 刷新商品数据
   */
  refreshProducts: function() {
    console.log('=== 开始刷新商品数据 ===');
    console.log('当前门店级别:', this.data.selectedStore?.level);
    console.log('当前商品标签:', this.data.currentProductTab);
    
    this.setData({
      page: 1,
      hasMore: true,
      products: []
    });
    
    // 重新加载所有商品数据
    console.log('重新加载商品列表...');
    this.getProductsByTab(this.data.currentProductTab);
    
    console.log('重新加载热门商品...');
    this.getHotProducts();
    
    console.log('重新加载新品推荐...');
    this.getNewProducts();
    
    console.log('=== 商品数据刷新完成 ===');
  },

  /**
   * 同步门店数据
   */
  syncStoreData: function() {
    // 从本地存储获取门店数据
    const storedSelectedStore = wx.getStorageSync('partnerSelectedStore');
    const storedStoreList = wx.getStorageSync('partnerStoreList');
    
    console.log('同步门店数据，当前本地存储:', { storedSelectedStore, storedStoreList });
    
    // 如果本地存储中有门店信息，使用本地存储数据
    if (storedStoreList && storedStoreList.length > 0) {
      const selectedStore = storedSelectedStore || storedStoreList[0];
      this.setData({
        storeList: storedStoreList,
        selectedStore: selectedStore
      });
      console.log('从本地存储同步门店信息:', {
        storeList: storedStoreList,
        selectedStore: selectedStore
      });
      return;
    }
    
    // 否则重新获取门店数据
    console.log('本地存储中没有门店信息，重新获取');
    this.getPartnerStores();
  },

  /**
   * 更多按钮点击事件
   */
  showMore: function() {
    wx.showActionSheet({
      itemList: ['分享', '设置', '帮助'],
      success: function(res) {
        console.log('选择了:', res.tapIndex);
      }
    });
  },

  /**
   * 眼睛按钮点击事件
   */
  toggleView: function() {
    wx.showToast({
      title: '切换视图模式',
      icon: 'none'
    });
  },

  /**
   * 获取轮播图数据
   */
  getBanners: function() {
    return productApi.getBanners('partner_products').then(res => {
      if (res.success && res.data) {
        console.log('获取轮播图成功:', res.data);
        this.setData({
          banners: res.data
        });
      }
    }).catch(err => {
      console.error('获取轮播图失败:', err);
      // 设置默认轮播图
      this.setData({
        banners: [
          {
            id: 1,
            title: '精选商品',
            imageUrl: '/images/mo/morebuy-logo.png',
            linkUrl: ''
          }
        ]
      });
    });
  },

  /**
   * 获取快捷菜单数据（合伙人端专用）
   */
  getQuickMenus: function() {
    console.log('[调试] 开始请求合伙人端快捷菜单');
    
    // 优先使用合伙人专用API
    return request({
      url: '/api/quick-menus/partner',
      method: 'GET',
      requireAuth: true // 改回true，因为进入合伙人端必然是已登录状态
    }).then(res => {
      console.log('[调试] 合伙人端快捷菜单接口返回:', res);
      if (res.success && Array.isArray(res.data)) {
        console.log('[调试] 设置合伙人端快捷菜单:', res.data);
        this.setData({ quickMenus: res.data });
      } else {
        console.warn('[调试] 合伙人端快捷菜单接口返回异常，尝试使用通用API:', res);
        // 回退到通用API
        return this.getQuickMenusFallback();
      }
    }).catch(err => {
      console.error('[调试] 获取合伙人端快捷菜单失败，尝试使用通用API:', err);
      // 回退到通用API
      return this.getQuickMenusFallback();
    });
  },

  /**
   * 回退的快捷菜单获取方法（使用通用API）
   */
  getQuickMenusFallback: function() {
    console.log('[调试] 使用通用快捷菜单API (platform=partner)');
    return request({
      url: '/api/quick-menus?platform=partner',
      method: 'GET',
      requireAuth: false
    }).then(res => {
      console.log('[调试] 通用快捷菜单接口返回:', res);
      if (res.success && Array.isArray(res.data)) {
        console.log('[调试] 设置通用快捷菜单:', res.data);
        this.setData({ quickMenus: res.data });
      } else {
        console.warn('[调试] 通用快捷菜单接口返回异常:', res);
        // 设置默认的合伙人菜单
        this.setDefaultPartnerMenus();
      }
    }).catch(err => {
      console.error('[调试] 获取通用快捷菜单失败，使用默认菜单:', err);
      this.setDefaultPartnerMenus();
    });
  },

  /**
   * 设置默认的合伙人端菜单
   */
  setDefaultPartnerMenus: function() {
    console.log('[调试] 设置默认合伙人端菜单');
    const defaultMenus = [
      {
        id: 'default_1',
        name: '门店合伙人',
        icon: '/images/icons2/门店合伙人.png',
        link_type: 'function',
        link_url: 'viewStorePartners'
      },
      {
        id: 'default_2',
        name: '门店库存',
        icon: '/images/icons2/门店库存.png',
        link_type: 'function',
        link_url: 'viewStoreInventory'
      },
      {
        id: 'default_3',
        name: '在线客服',
        icon: '/images/icons2/在线客服.png',
        link_type: 'function',
        link_url: 'contactService'
      },
      {
        id: 'default_4',
        name: '分享门店',
        icon: '/images/icons2/分享门店.png',
        link_type: 'function',
        link_url: 'shareStore'
      }
    ];
    
    this.setData({ quickMenus: defaultMenus });
  },

  /**
   * 获取分类数据
   */
  getCategories: function() {
    return request({
      url: '/api/products/categories',
      method: 'GET'
    }).then(res => {
      if (res.success && Array.isArray(res.data)) {
        console.log('获取分类成功:', res.data.length, '个分类');
        this.setData({ categories: res.data });
        
        // 直接处理一级分类作为商品标签
        this.processProductTabs(res.data);
      } else {
        console.warn('获取分类失败:', res);
        this.setData({ categories: [] });
      }
    }).catch(err => {
      console.error('获取分类失败:', err);
      this.setData({ categories: [] });
    });
  },

  /**
   * 处理商品标签（一级分类）
   */
  processProductTabs: function(categories) {
    if (!Array.isArray(categories) || categories.length === 0) {
      console.warn('分类数据为空或格式不正确');
      this.setData({ 
        productTabs: ['全部'],
        categoryTabs: []
      });
      return;
    }
    
    // 过滤出一级分类（parentId为null或0的分类）
    const level1Categories = categories.filter(category => 
      !category.parentId || category.parentId === 0 || category.parentId === null
    );
    
    const productTabs = ['全部', ...level1Categories.map(category => category.name)];
    
    this.setData({ 
      productTabs: productTabs,
      categoryTabs: level1Categories // 保存分类数据用于筛选
    });
    
    console.log('设置商品标签完成:', productTabs.length, '个标签');
  },

  /**
   * 获取商品列表
   */
  getProducts: function() {
    const params = {
      page: this.data.page,
      pageSize: this.data.pageSize
    };
    
    // 分类筛选逻辑：优先使用selectedCategory，否则使用标签筛选
    if (this.data.selectedCategory) {
      // 如果通过分类点击设置了selectedCategory，优先使用
      params.categoryId = this.data.selectedCategory;
    } else if (this.data.currentProductTab > 0 && this.data.categoryTabs && this.data.categoryTabs.length > 0) {
      // 如果选中的是分类标签（索引大于0），设置对应的分类ID
      const categoryIndex = this.data.currentProductTab - 1; // 减1是因为第0个是"全部"
      if (categoryIndex < this.data.categoryTabs.length) {
        const selectedCategory = this.data.categoryTabs[categoryIndex];
        if (selectedCategory && selectedCategory.id) {
          params.categoryId = selectedCategory.id;
        }
      }
    }
    
    if (this.data.searchKeyword) {
      params.keyword = this.data.searchKeyword;
    }
    
    // 处理特殊的固定标签（如果有的话）
    // 注意：这里的索引需要根据实际的标签顺序调整
    // 如果标签顺序是：全部、分类1、分类2、...、热销、新品、促销
    // 那么需要根据实际情况调整这些索引
    
    // 添加筛选条件
    if (this.data.filterOptions) {
      // 价格范围筛选
      if (this.data.filterOptions.priceRange && 
          (this.data.filterOptions.priceRange[0] > 0 || this.data.filterOptions.priceRange[1] < 1000)) {
        params.minPrice = this.data.filterOptions.priceRange[0];
        params.maxPrice = this.data.filterOptions.priceRange[1];
      }
      
      // 排序方式（如果不是标签排序）
      if (this.data.currentProductTab === 0 && this.data.filterOptions.sortBy !== 'default') {
        params.sortBy = this.data.filterOptions.sortBy;
      }
      
      // 库存筛选
      if (this.data.filterOptions.onlyInStock) {
        params.onlyInStock = true;
      }
      
      // 分类筛选（来自筛选抽屉）
      if (this.data.filterOptions.selectedCategories && this.data.filterOptions.selectedCategories.length > 0) {
        params.categoryIds = this.data.filterOptions.selectedCategories.join(',');
      }
    }
    
    // 添加门店级别参数
    if (this.data.selectedStore && this.data.selectedStore.level) {
      params.storeLevel = this.data.selectedStore.level;
      console.log('传递门店级别参数:', this.data.selectedStore.level);
    } else {
      console.log('未找到门店级别信息:', this.data.selectedStore);
    }
    
    console.log('获取商品参数:', params);
    
    // 构建查询字符串
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    this.setData({ loading: true });
    
    return request({
      url: `/api/products?${queryString}`,
      method: 'GET',
      data: {},
      requireAuth: true
    }).then(res => {
      if (res.success && res.data && res.data.list && Array.isArray(res.data.list)) {
        console.log('获取商品列表成功:', res.data);
        let productsList = res.data.list;
        
        // 处理商品图片URL，确保格式正确
        productsList = productsList.map(product => {
          // 处理images字段，确保是数组或转为空数组
          if (product.images) {
            if (typeof product.images === 'string') {
              try {
                // 检查字符串是否为有效的JSON格式
                if (product.images.trim().startsWith('[') && product.images.trim().endsWith(']')) {
                  product.images = JSON.parse(product.images);
                } else {
                  product.images = [];
                }
              } catch (e) {
                console.error('解析商品图片失败:', e, '原始数据:', product.images);
                product.images = [];
              }
            } else if (!Array.isArray(product.images)) {
              product.images = [];
            }
          } else {
            product.images = [];
          }
          
          // 确保imageUrl字段存在且为完整URL
          if (!product.imageUrl && product.images && product.images.length > 0) {
            // 确保图片URL是完整路径
            let imageUrl = product.images[0];
            if (typeof imageUrl === 'string') {
              if (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('/')) {
                // 如果不是完整路径，添加前缀
                imageUrl = '/' + imageUrl;
              }
              product.imageUrl = imageUrl;
            } else {
              product.imageUrl = '/images/mo/mogoods.jpg';
            }
          }
          
          // 简化图片处理逻辑 - 如果没有图片或图片路径有问题，直接使用默认图片
          if (!product.imageUrl) {
            product.imageUrl = '/images/mo/mogoods.jpg';
          }
          
          return product;
        });
        
        let products;
        let hasMore = true; // 默认为true，等后面根据数据判断
        
        if (this.data.page === 1) {
          products = productsList;
        } else {
          // 合并商品列表
          products = [...this.data.products, ...productsList];
        }
        
        // 判断是否有更多数据的逻辑
        if (res.data.total !== undefined && res.data.total !== null) {
          // 如果后端返回了总数，用总数判断（最准确）
          hasMore = products.length < res.data.total;
          console.log('[选品页分页] 使用总数判断 - 当前商品数:', products.length, '总数:', res.data.total, '是否有更多:', hasMore);
        } else {
          // 如果没有总数，用返回的数据量判断
          if (res.data.list.length === 0) {
            // 如果本次没返回任何数据，说明没有更多了
            hasMore = false;
            console.log('[选品页分页] 本次返回0个商品，没有更多数据');
          } else if (res.data.list.length < this.data.pageSize) {
            // 如果返回的数据少于页面大小，说明是最后一页
            hasMore = false;
            console.log('[选品页分页] 返回数据少于页面大小 - 返回:', res.data.list.length, '页面大小:', this.data.pageSize, '没有更多数据');
          } else {
            // 如果返回的数据等于页面大小，可能还有更多数据
            hasMore = true;
            console.log('[选品页分页] 返回数据等于页面大小 - 返回:', res.data.list.length, '页面大小:', this.data.pageSize, '可能有更多数据');
          }
        }
        
        // 检查采购价信息
        if (products.length > 0) {
          console.log('第一个商品的采购价信息:', {
            id: products[0].id,
            name: products[0].name,
            purchasePrice: products[0].purchasePrice,
            storeLevel: this.data.selectedStore?.level
          });
        }
        
        this.setData({
          products: products,
          hasMore: hasMore,
          lastDataLength: res.data.list.length,
          loading: false
        });
      } else {
        this.setData({
          hasMore: false,
          loading: false
        });
        if (this.data.page === 1) {
          this.setData({ products: [] });
        }
      }
    }).catch(err => {
      console.error('获取商品列表失败:', err);
      this.setData({ 
        hasMore: false,
        loading: false 
      });
      
      if (this.data.page === 1) {
        this.setData({ products: [] });
      }
    });
  },

  /**
   * 获取热门商品
   */
  getHotProducts: function() {
    const params = {
      page: 1,
      pageSize: 6,
      sortType: 'sales'
    };
    
    // 添加门店级别参数
    if (this.data.selectedStore && this.data.selectedStore.level) {
      params.storeLevel = this.data.selectedStore.level;
    }
    
    // 构建查询字符串
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    return request({
      url: `/api/products?${queryString}`,
      method: 'GET',
      data: {}
    }).then(res => {
      if (res.success && res.data) {
        console.log('获取热门商品成功:', res.data);
        this.setData({
          hotProducts: res.data.list
        });
      }
    }).catch(err => {
      console.error('获取热门商品失败:', err);
    });
  },

  /**
   * 获取新品推荐
   */
  getNewProducts: function() {
    const params = {
      page: 1,
      pageSize: 10,
      isNew: 1  // 后端验证要求isNew必须是0或1的整数
    };
    
    // 添加门店级别参数
    if (this.data.selectedStore && this.data.selectedStore.level) {
      params.storeLevel = this.data.selectedStore.level;
    }
    
    // 构建查询字符串
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    return request({
      url: `/api/products?${queryString}`,
      method: 'GET',
      data: {}
    }).then(res => {
      if (res.success && res.data) {
        console.log('获取新品推荐成功:', res.data);
        this.setData({
          newProducts: res.data.list || []
        });
      }
    }).catch(err => {
      console.error('获取新品推荐失败:', err);
      this.setData({
        newProducts: []
      });
    });
  },

  /**
   * 加载更多商品
   */
  loadMoreProducts: function() {
    if (!this.data.hasMore || this.data.loading) {
      console.log('[加载更多] 跳过加载 - loading:', this.data.loading, 'hasMore:', this.data.hasMore);
      return;
    }
    
    console.log('[加载更多] 开始加载更多商品，当前页:', this.data.page, '-> 下一页:', this.data.page + 1);
    this.setData({
      page: this.data.page + 1,
      loading: true
    });
    
    this.getProducts().finally(() => {
      console.log('[加载更多] 加载完成，重置loading状态');
      this.setData({ loading: false });
    });
  },

  /**
   * 搜索输入
   */
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 搜索确认
   */
  onSearchConfirm: function(e) {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }
    
    console.log('搜索商品:', keyword);
    wx.navigateTo({
      url: `/pages/search/search?keyword=${encodeURIComponent(keyword)}&type=product`
    });
  },

  /**
   * 轮播图点击
   */
  onBannerTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const banner = this.data.banners[index];
    console.log('点击轮播图:', banner);
    
    if (banner.linkUrl) {
      // 根据链接类型进行跳转
      if (banner.linkUrl.startsWith('/pages/')) {
        wx.navigateTo({
          url: banner.linkUrl
        });
      } else if (banner.linkUrl.startsWith('http')) {
        // 外部链接，可以使用web-view或其他方式处理
        wx.showToast({
          title: '即将跳转到外部链接',
          icon: 'none'
        });
      }
    }
  },



  /**
   * 分类点击
   */
  onCategoryTap: function(e) {
    const category = e.currentTarget.dataset.category;
    console.log('选择分类:', category);
    
    this.setData({
      selectedCategory: category ? category.id : null,
      currentProductTab: 0, // 重置标签选择为"全部"
      page: 1,
      hasMore: true,
      products: [], // 清空商品列表
      filterOptions: {
        ...this.data.filterOptions,
        selectedCategories: [] // 清空筛选抽屉中的分类筛选
      }
    });
    
    this.getProducts();
  },

  /**
   * 商品标签切换
   */
  onProductTabTap: function(e) {
    const index = e.currentTarget.dataset.index;
    console.log('切换商品标签:', index);
    
    this.setData({
      currentProductTab: index,
      page: 1,
      hasMore: true
    });
    
    this.getProductsByTab(index);
  },

  /**
   * 根据标签获取商品
   */
  getProductsByTab: function(tabIndex) {
    console.log('切换商品标签:', tabIndex);
    
    // 如果切换到"全部"标签，清空所有筛选条件
    const updateData = {
      page: 1,
      hasMore: true,
      products: [],
      currentProductTab: tabIndex,
      selectedCategory: null, // 清除分类点击筛选
      loading: true,
      lastDataLength: 0
    };
    
    // 如果是"全部"标签（索引为0），清空筛选抽屉中的分类筛选
    if (tabIndex === 0) {
      updateData.filterOptions = {
        ...this.data.filterOptions,
        selectedCategories: [] // 清空筛选抽屉中的分类筛选
      };
      console.log('切换到全部标签，清空所有分类筛选');
    }
    
    // 重置页面状态
    this.setData(updateData);
    
    // 重新获取商品列表
    this.getProducts().finally(() => {
      this.setData({ loading: false });
    });
  },

  /**
   * 商品点击
   */
  onProductTap: function(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('点击商品:', productId);
    
    wx.navigateTo({
      url: `/pages/product/detail?id=${productId}&from=partner`
    });
  },

  /**
   * 热门商品点击
   */
  onHotProductTap: function(e) {
    const product = e.currentTarget.dataset.product;
    console.log('点击热门商品:', product);
    
    wx.navigateTo({
      url: `/pages/product/detail?id=${product.id}&from=partner`
    });
  },

  /**
   * 新品点击
   */
  onNewProductTap: function(e) {
    const product = e.currentTarget.dataset.product;
    console.log('点击新品:', product);
    
    wx.navigateTo({
      url: `/pages/product/detail?id=${product.id}&from=partner`
    });
  },

  /**
   * 新品上市滚动事件
   */
  onNewProductsScroll: function(e) {
    // 可以在这里添加滚动监听逻辑
    // 移除调试日志，避免控制台输出过多信息
  },

  /**
   * 图片加载错误处理
   */
  onGoodsImgError: function(e) {
    const productId = e.currentTarget.dataset.id;
    if (productId) {
      // 直接使用默认图片，不再检测旧路径
      const imgErrorMap = { ...this.data.imgErrorMap };
      imgErrorMap[productId] = true;
      this.setData({ imgErrorMap });
      console.log('商品图片加载失败，将使用默认图片: /images/mo/mogoods.jpg');
    }
  },

  /**
   * 显示筛选抽屉
   */
  onShowFilter: function() {
    this.setData({
      showFilterDrawer: true
    });
  },

  /**
   * 隐藏筛选抽屉
   */
  onHideFilter: function() {
    this.setData({
      showFilterDrawer: false
    });
  },

  /**
   * 应用筛选
   */
  onApplyFilter: function(e) {
    const filterOptions = e.detail;
    console.log('应用筛选:', filterOptions);
    
    this.setData({
      filterOptions: filterOptions,
      page: 1,
      hasMore: true
    });
    
    this.getProducts();
    this.onHideFilter();
  },

  /**
   * 重置筛选
   */
  onResetFilter: function() {
    this.setData({
      filterOptions: {
        priceRange: [0, 1000],
        sortBy: 'default',
        onlyInStock: false,
        selectedCategories: [] // 清空分类筛选
      },
      page: 1,
      hasMore: true
    });
    
    this.getProducts();
    this.onHideFilter();
  },

  /**
   * 加入采购车
   */
  onAddToCart: function(e) {
    const product = e.currentTarget.dataset.product;
    console.log('加入采购车:', product);
    
    // 检查商品数据是否完整
    if (!product || !product.id) {
      wx.showToast({
        title: '商品数据不完整',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 检查是否有门店选择
    if (!this.data.selectedStore) {
    wx.showToast({
        title: '请先选择门店',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 从本地存储获取当前采购车数据
    const purchaseCart = wx.getStorageSync('purchaseCart') || [];
    
    // 检查商品是否已在采购车中
    const existingItemIndex = purchaseCart.findIndex(item => item.id === product.id);
    
    if (existingItemIndex !== -1) {
      // 商品已存在，增加数量
      purchaseCart[existingItemIndex].quantity += 1;
      // 确保purchasePrice是数字类型
      const purchasePrice = parseFloat(purchaseCart[existingItemIndex].purchasePrice);
      purchaseCart[existingItemIndex].subtotal = (purchasePrice * purchaseCart[existingItemIndex].quantity).toFixed(2);
      
      wx.showToast({
        title: '已增加数量',
      icon: 'success',
      duration: 1500
    });
    } else {
      // 商品不存在，添加到采购车
      // 确保price字段被正确转换为数字
      const purchasePrice = parseFloat(product.purchasePrice || product.price);
      
      // 处理图片路径
      let imagePath = '/images/mo/mogoods.jpg'; // 默认图片
      
      if (product.images) {
        // 如果images是字符串，尝试解析JSON
        if (typeof product.images === 'string') {
          try {
            const imagesArray = JSON.parse(product.images);
            if (imagesArray && imagesArray.length > 0) {
              imagePath = imagesArray[0];
            }
          } catch (e) {
            console.log('解析图片JSON失败:', e);
          }
        } else if (Array.isArray(product.images) && product.images.length > 0) {
          // 如果images是数组
          imagePath = product.images[0];
        }
      } else if (product.imageUrl) {
        // 如果有imageUrl字段
        imagePath = product.imageUrl;
      }
      
      const newItem = {
        id: product.id,
        productId: product.id,
        name: product.name,
        image: imagePath,
        purchasePrice: purchasePrice,
        quantity: 1,
        subtotal: purchasePrice.toFixed(2),
        selected: false,
        storeNo: this.data.selectedStore.store_no,
        storeName: this.data.selectedStore.name,
        storeNo: this.data.selectedStore.store_no
      };
      
      purchaseCart.push(newItem);
      
      wx.showToast({
        title: '已加入采购车',
        icon: 'success',
        duration: 1500
      });
    }
    
    // 保存到本地存储
    wx.setStorageSync('purchaseCart', purchaseCart);
    
    console.log('采购车数据已更新，当前商品数量:', purchaseCart.length);
  },

  /**
   * 快捷菜单点击事件
   */
  onQuickMenuTap: function(e) {
    const menu = e.currentTarget.dataset.menu;
    console.log('[合伙人端] 点击快捷菜单:', menu);
    
    if (menu.link_type === 'function') {
      // 执行功能
      this.executePartnerFunction(menu.link_url, menu.function_params);
    } else if (menu.link_type === 'page') {
      // 页面跳转
      wx.navigateTo({
        url: menu.link_url
      }).catch(err => {
        console.error('页面跳转失败:', err);
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
      });
    } else if (menu.link_type === 'external') {
      // 外部链接
      wx.showToast({
        title: '即将跳转到外部链接',
        icon: 'none'
      });
    } else if (menu.link_type === 'modal') {
      // 弹窗功能
      this.showMenuModal(menu);
    }
  },

  /**
   * 执行合伙人端功能
   */
  executePartnerFunction: function(functionName, params = {}) {
    console.log('[合伙人端] 执行功能:', functionName, params);
    
    switch (functionName) {
      case 'viewStorePartners':
        this.viewStorePartners();
        break;
      case 'viewStoreInventory':
        this.viewStoreInventory();
        break;
      case 'contactService':
        this.contactService(params);
        break;
      case 'shareStore':
        this.shareStore(params);
        break;
      case 'viewSalesStats':
        this.viewSalesStats();
        break;
      case 'viewCommissionDetail':
        this.viewCommissionDetail();
        break;
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
    }
  },

  /**
   * 查看门店合伙人
   */
  viewStorePartners: function() {
    console.log('[合伙人端] 查看门店合伙人');
    
    // 检查是否有门店数据
    if (!this.data.storeList || this.data.storeList.length === 0) {
      // 尝试重新加载门店数据
      wx.showModal({
        title: '暂无门店数据',
        content: '检测到门店数据为空，是否重新加载门店信息？',
        confirmText: '重新加载',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.getPartnerStores(true).then(() => {
              // 重新加载后再次检查
              if (this.data.storeList && this.data.storeList.length > 0) {
                // 重新调用当前方法
                this.viewStorePartners();
              } else {
                wx.showToast({
                  title: '仍无门店数据，请联系管理员',
                  icon: 'none',
                  duration: 3000
                });
              }
            }).catch(() => {
              wx.showToast({
                title: '加载门店数据失败',
                icon: 'none'
              });
            });
          }
        }
      });
      return;
    }
    
    if (!this.data.selectedStore) {
      wx.showToast({
        title: '请先选择门店',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: `/partner/store-partners/store-partners?storeNo=${this.data.selectedStore.store_no}`
    }).catch(err => {
      console.error('跳转门店合伙人页面失败:', err);
      wx.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    });
  },

  /**
   * 查看门店库存
   */
  viewStoreInventory: function() {
    console.log('[合伙人端] 查看门店库存');
    
    // 检查是否有门店数据
    if (!this.data.storeList || this.data.storeList.length === 0) {
      // 尝试重新加载门店数据
      wx.showModal({
        title: '暂无门店数据',
        content: '检测到门店数据为空，是否重新加载门店信息？',
        confirmText: '重新加载',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.getPartnerStores(true).then(() => {
              // 重新加载后再次检查
              if (this.data.storeList && this.data.storeList.length > 0) {
                // 重新调用当前方法
                this.viewStoreInventory();
              } else {
                wx.showToast({
                  title: '仍无门店数据，请联系管理员',
                  icon: 'none',
                  duration: 3000
                });
              }
            }).catch(() => {
              wx.showToast({
                title: '加载门店数据失败',
                icon: 'none'
              });
            });
          }
        }
      });
      return;
    }
    
    if (!this.data.selectedStore) {
      wx.showToast({
        title: '请先选择门店',
        icon: 'none'
      });
      return;
    }
    
    // 跳转到门店库存页面
    wx.navigateTo({
      url: `/partner/inventory/inventory?storeNo=${this.data.selectedStore.store_no}`
    }).catch(err => {
      console.error('跳转门店库存页面失败:', err);
      wx.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    });
  },

  /**
   * 联系在线客服
   */
  contactService: function(params = {}) {
    console.log('[合伙人端] 联系在线客服', params);
    
    // 可以根据params.type来区分客服类型
    const serviceType = params.type || 'partner';
    
    // 直接跳转到客服中心页面
    wx.navigateTo({
      url: '/pages/service/index?type=' + serviceType
    }).catch(() => {
      wx.showToast({
        title: '客服功能开发中',
        icon: 'none'
      });
    });
  },

  /**
   * 分享门店
   */
  shareStore: function(params = {}) {
    console.log('[合伙人端] 分享门店', params);
    
    // 检查门店数据状态
    if (!this.data.storeList || this.data.storeList.length === 0) {
      console.log('[合伙人端] 门店数据为空，尝试重新加载');
      wx.showModal({
        title: '提示',
        content: '门店数据为空，是否重新获取？',
        success: (res) => {
          if (res.confirm) {
            this.getPartnerStores(true).then(() => {
              // 重新加载后再次尝试分享
              this.shareStore(params);
            }).catch(() => {
              wx.showToast({
                title: '获取门店数据失败',
                icon: 'none'
              });
            });
          }
        }
      });
      return;
    }
    
    if (!this.data.selectedStore) {
      wx.showToast({
        title: '请先选择门店',
        icon: 'none'
      });
      return;
    }
    
    const store = this.data.selectedStore;
    console.log('[合伙人端] 选中的门店信息:', store);
    
    // 将门店信息编码为URL参数
    const storeInfoObj = {
      id: store.id,
      store_no: store.store_no,
      name: store.name,
      address: store.address,
      level: store.level,
      cover_image: store.cover_image
    };
    
    console.log('[合伙人端] 准备传递的门店信息对象:', storeInfoObj);
    
    const storeInfo = encodeURIComponent(JSON.stringify(storeInfoObj));
    console.log('[合伙人端] 编码后的门店信息:', storeInfo);
    
    const shareUrl = `/pages/share/share?source=partner&storeInfo=${storeInfo}`;
    console.log('[合伙人端] 跳转URL:', shareUrl);
    
    // 跳转到分享页面，传递门店信息
    wx.navigateTo({
      url: shareUrl
    }).catch(err => {
      console.error('跳转分享页面失败:', err);
      wx.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    });
  },

  /**
   * 查看销售统计
   */
  viewSalesStats: function() {
    console.log('[合伙人端] 查看销售统计');
    
    wx.navigateTo({
      url: `/partner/stats/sales-stats?storeNo=${this.data.selectedStore?.store_no}`
    }).catch(err => {
      console.error('跳转销售统计页面失败:', err);
      wx.showToast({
        title: '统计功能开发中',
        icon: 'none'
      });
    });
  },

  /**
   * 查看佣金明细
   */
  viewCommissionDetail: function() {
    console.log('[合伙人端] 查看佣金明细');
    
    wx.navigateTo({
      url: `/partner/finance/commission-detail`
    }).catch(err => {
      console.error('跳转佣金明细页面失败:', err);
      wx.showToast({
        title: '佣金功能开发中',
        icon: 'none'
      });
    });
  },

  /**
   * 显示菜单弹窗
   */
  showMenuModal: function(menu) {
    console.log('[合伙人端] 显示菜单弹窗:', menu);
    
    wx.showModal({
      title: menu.name,
      content: menu.description || '功能开发中',
      showCancel: false
    });
  }
});