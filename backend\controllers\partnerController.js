const Partner = require('../models/Partner');
const Store = require('../models/Store');

exports.joinPartner = async (req, res) => {
  try {
    const { user_id, store_no, type, amount, percent } = req.body;
    if (!user_id || !store_no || !type || !amount || !percent) {
      return res.status(400).json({ success: false, message: '参数不完整' });
    }
    const result = await Partner.create({ user_id, store_no, type, amount, percent });
    // 累加门店股本金
    await Store.addCapitalByStoreNo(store_no, amount);
    res.json({ success: true, id: result.id });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

exports.getPartnersByStoreId = async (req, res) => {
  try {
    const { store_no } = req.query;
    console.log('[合伙人控制器] 接收到的参数:', req.query);
    
    if (!store_no) {
      console.log('[合伙人控制器] 缺少store_no参数');
      return res.status(400).json({ success: false, message: '缺少store_no参数' });
    }
    
    console.log('[合伙人控制器] 开始查询门店合伙人，门店编号:', store_no);
    const partners = await Partner.getPartnersByStoreId(store_no);
    console.log('[合伙人控制器] 查询结果:', partners);
    
    res.json({ success: true, data: partners });
  } catch (error) {
    console.error('[合伙人控制器] 查询失败:', error);
    console.error('[合伙人控制器] 错误详情:', {
      message: error.message,
      stack: error.stack
    });
    
    // 返回详细错误信息
    res.status(500).json({
      success: false,
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      sql: error.sql || '',
      params: error.params || '',
    });
  }
};

exports.getStorePartners = async (req, res) => {
  try {
    const { store_no } = req.query;
    console.log('[合伙人控制器] getStorePartners 接收到的参数:', req.query);
    
    if (!store_no) {
      console.log('[合伙人控制器] 缺少store_no参数');
      return res.status(400).json({ success: false, message: '缺少store_no参数' });
    }
    
    console.log('[合伙人控制器] 开始查询门店合伙人，门店编号:', store_no);
    
    // 根据门店编号获取合伙人列表
    const partners = await Partner.getPartnersByStore(store_no);
    console.log('[合伙人控制器] 查询结果:', partners);
    
    res.json({ 
      success: true, 
      data: partners.data || []
    });
  } catch (error) {
    console.error('[合伙人控制器] getStorePartners 查询失败:', error);
    console.error('[合伙人控制器] 错误详情:', {
      message: error.message,
      stack: error.stack
    });
    
    res.status(500).json({
      success: false,
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};