<!-- 我的门店页面 -->
<view class="my-stores-page">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:elif="{{isEmpty}}">
    <view class="empty-content">
      <image class="empty-icon" src="/images/icons2/门店.png" mode="aspectFit"></image>
      <view class="empty-title">暂无门店</view>
      <view class="empty-desc">您还没有加入任何门店</view>
      <view class="empty-tip">请联系管理员申请加入门店</view>
    </view>
  </view>

  <!-- 门店列表 -->
  <view class="store-list" wx:else>
    <view class="store-item" wx:for="{{storeList}}" wx:key="id">
      <!-- 门店形象照 -->
      <view class="store-image-container">
        <image class="store-image" src="{{item.image || '/images/icons2/门店.png'}}" mode="aspectFill"></image>
        <view class="store-level-badge">{{item.level_text}}</view>
      </view>
      
      <!-- 门店信息 -->
      <view class="store-info">
        <view class="store-header">
          <view class="store-name">{{item.name}}</view>
          <view class="store-status status-{{item.status || 'active'}}">{{item.status_text}}</view>
        </view>
        
        <view class="store-details">
          <view class="detail-item">
            <text class="detail-label">门店编号：</text>
            <text class="detail-value">{{item.store_no}}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">门店地址：</text>
            <text class="detail-value">{{item.full_address}}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">合伙人数：</text>
            <text class="detail-value highlight">{{item.partner_count}}人</text>
          </view>
        </view>
        
        <!-- 操作按钮区域 -->
        <view class="store-actions">
          <view class="action-item" catchtap="goToStorePartners" data-store="{{item}}">
            <image class="action-icon" src="/images/icons2/门店合伙人.png" mode="aspectFit"></image>
            <text class="action-text">合伙人</text>
          </view>
          
          <view class="action-item" catchtap="goToStoreInventory" data-store="{{item}}">
            <image class="action-icon" src="/images/icons2/门店库存.png" mode="aspectFit"></image>
            <text class="action-text">库存</text>
          </view>
          
          <view class="action-item" catchtap="goToStoreFunds" data-store="{{item}}">
            <image class="action-icon" src="/images/icons2/门店公积金.png" mode="aspectFit"></image>
            <text class="action-text">资金</text>
          </view>
          
          <view class="action-item" catchtap="goToStoreOrders" data-store="{{item}}">
            <image class="action-icon" src="/images/icons2/待发货.png" mode="aspectFit"></image>
            <text class="action-text">订单</text>
          </view>
        </view>
      </view>
      
    </view>
  </view>

  <!-- 底部提示 -->
  <view class="bottom-tip" wx:if="{{!loading && !isEmpty}}">
    <text class="tip-text">使用上方快捷按钮可快速访问门店功能</text>
  </view>
</view>

<!-- 底部导航栏 -->
<partner-tabbar selected="3" />