/* 素材管理页面样式 */
.materials-admin-page {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 顶部标签切换和搜索栏容器 */
.tabs-container {
  background: #fff;
  margin: 24rpx 24rpx 0 24rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 标签栏 */
.tabs-row {
  display: flex;
  padding: 8rpx;
  border-bottom: 1rpx solid #eee; /* 与搜索栏的分隔线 */
}

/* 搜索栏 */
.search-row {
  padding: 16rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx; /* 减小左右内边距，为文字留出更多空间 */
  font-size: 28rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s;
  font-weight: 500;
  white-space: nowrap; /* 确保标签文字不换行 */
  overflow: visible; /* 允许内容溢出显示 */
}

.tab-item.active {
  color: #ff4d4f;
  font-weight: 600;
  border-bottom: 4rpx solid #ff4d4f;
}

/* 操作按钮区域 */
.section-actions {
  display: flex;
  gap: 24rpx;
  padding: 24rpx;
  padding-bottom: 0;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  background: #fff;
  color: #333;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.action-btn.primary {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: #fff;
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

/* 公司信息预览 */
.company-section {
  margin-top: 24rpx;
}

.company-preview {
  padding: 0 24rpx;
}

.preview-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.card-header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.company-logo {
  width: 100rpx;
  height: 100rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
  border: 2rpx solid #f0f0f0;
}

.company-info {
  flex: 1;
}

.company-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.company-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.card-actions {
  display: flex;
  gap: 16rpx;
}

.edit-btn {
  padding: 12rpx 24rpx;
  background: #ff4d4f;
  color: #fff;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
}

/* 公司信息编辑 */
.company-edit-card {
  background: #fff;
  margin: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.1);
  overflow: hidden;
}

.card-content {
  padding: 32rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

/* 修改输入框样式，使其为浅灰色背景 */
.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #f8f9fa;
  box-sizing: border-box;
  min-height: 80rpx;
  line-height: 1.5;
}

.form-input:focus {
  border-color: #ff4d4f;
  background: #fff;
}

.form-textarea {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #f8f9fa;
  box-sizing: border-box;
  min-height: 120rpx;
  line-height: 1.5;
  resize: none;
  overflow-y: auto;
}

.form-textarea:focus {
  border-color: #ff4d4f;
  background: #fff;
}

.logo-edit-area {
  display: flex;
  justify-content: center;
}

.logo-upload-area {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #d9d9d9;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
}

.logo-upload-area:hover {
  border-color: #ff4d4f;
  background: #fff5f5;
}

.company-logo-preview {
  width: 180rpx;
  height: 180rpx;
  border-radius: 12rpx;
  object-fit: cover;
}

.logo-upload-hint {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.save-button-area {
  padding: 24rpx 32rpx;
  background: #fafafa;
  border-top: 1rpx solid #e8e8e8;
}

.save-btn {
  width: 100%;
  padding: 24rpx;
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
}

/* 轮播图管理 */
.banners-section {
  margin-top: 4rpx; /* 调整为4rpx，相当于2像素的间距 */
}

.page-type-section {
  padding: 0; /* 移除内边距，因为子元素已有边距设置 */
  margin-bottom: 24rpx;
}

.page-type-tabs {
  background: #fff;
  border-radius: 12rpx 12rpx 0 0; /* 只保留上方圆角，与下方按钮栏连接 */
  padding: 8rpx 0; /* 移除左右内边距，让内部的scroll-view处理边距 */
  margin-bottom: 0; /* 移除底部间距，与按钮栏背景连接 */
  margin-left: 24rpx; /* 添加左边距与按钮栏保持一致 */
  margin-right: 24rpx; /* 添加右边距与按钮栏保持一致 */
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  overflow: hidden; /* 确保内容不会超出容器边界 */
  width: calc(100% - 48rpx); /* 明确设置宽度，减去左右边距 */
  box-sizing: border-box; /* 确保边距和内边距计算在宽度内 */
}

.page-type-list {
  display: flex;
  white-space: nowrap;
  padding: 0 16rpx; /* 恢复左右内边距，确保标签与容器边界有适当距离 */
  box-sizing: border-box; /* 确保padding计算在容器宽度内 */
}

.page-type-tab {
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 12rpx; /* 调整为12rpx圆角，与选中状态保持一致 */
  margin-right: 12rpx;
  flex-shrink: 0; /* 防止标签被压缩 */
}

.page-type-tab:last-child {
  margin-right: 0; /* 移除最后一个标签的右边距，防止超出边界 */
  white-space: nowrap;
  transition: all 0.3s;
}

.page-type-tab.active {
  background: #f5f5f5; /* 改为灰白色背景 */
  color: #333; /* 文字颜色改为深色以保证可读性 */
  border-radius: 12rpx; /* 添加圆角 */
}

.actions-row {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end; /* 改为底部对齐 */
  margin-bottom: 24rpx;
  background: #fff; /* 添加白色背景 */
  height: 100rpx; /* 调整容器高度为100rpx */
  padding: 0 24rpx 16rpx 24rpx; /* 添加底部内边距，使按钮适当靠底部 */
  border-radius: 0 0 12rpx 12rpx; /* 只保留下方圆角，与上方标签栏连接 */
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1); /* 添加阴影保持一致性 */
  margin-left: 24rpx;
  margin-right: 24rpx;
  gap: 16rpx; /* 添加按钮间距 */
}

.add-btn {
  padding: 4rpx 12rpx; /* 进一步减小按钮内边距 */
  background: #fff; /* 改为白色背景 */
  color: #ff4d4f; /* 改为红色文字 */
  border: 2rpx solid #ff4d4f; /* 添加红色边框 */
  border-radius: 6rpx; /* 进一步减小圆角 */
  font-size: 22rpx; /* 进一步减小字体 */
  font-weight: 400; /* 进一步减轻字体粗细 */
  box-shadow: none; /* 移除阴影 */
  width: auto !important; /* 强制按钮宽度自适应内容 */
  min-width: auto !important; /* 移除最小宽度限制 */
  max-width: fit-content; /* 限制最大宽度为内容宽度 */
  flex: none; /* 移除flex拉伸 */
  display: inline-block; /* 改为行内块元素 */
  line-height: 1; /* 设置最小行高 */
  height: auto !important; /* 强制高度自适应 */
}

.refresh-btn {
  padding: 16rpx 32rpx;
  background: #fff;
  color: #666;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.banners-content {
  padding: 0 24rpx;
}

.empty-state {
  text-align: center;
  padding: 80rpx 24rpx;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.empty-hint {
  display: block;
  font-size: 26rpx;
  color: #ccc;
}

.banner-edit-card {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.1);
  overflow: hidden;
  position: relative;
}

.floating-actions {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  display: flex;
  gap: 12rpx;
  z-index: 10;
}

.floating-btn {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
  font-weight: 500;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-btn.save {
  background: #52c41a;
  color: #fff;
}

.floating-btn.delete {
  background: #ff4d4f;
  color: #fff;
}

.image-edit-area {
  display: flex;
  justify-content: center;
}

.image-upload-area {
  position: relative;
  width: 300rpx;
  height: 200rpx;
  border: 2rpx dashed #d9d9d9;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
  overflow: hidden;
}

.image-upload-area:hover {
  border-color: #ff4d4f;
  background: #fff5f5;
}

.banner-preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-upload-hint {
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 22rpx;
  color: #fff;
  background: rgba(0,0,0,0.6);
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.switch-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 消息管理样式 */
.messages-section {
  margin-top: 4rpx; /* 调整为4rpx，相当于2像素的间距，与轮播图管理保持一致 */
}

.message-filter-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx;
  background: #fff;
  border-radius: 12rpx;
  margin: 0 24rpx 24rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  border: 1rpx solid #f0f0f0;
  min-height: 80rpx;
}

.message-filter-list {
  display: flex;
  align-items: center;
  gap: 16rpx;
  height: 100%;
  flex: 1;
  justify-content: flex-start;
  overflow: visible;
  white-space: nowrap;
}

.message-filter-tab {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  font-size: 26rpx;
  color: #333;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
  white-space: nowrap;
  height: 44rpx;
  line-height: 1;
  flex-shrink: 0;
  min-width: 80rpx;
  border-radius: 22rpx;
  background: transparent;
}

.message-filter-tab.active {
  color: #333;
  font-weight: 600;
  background: #e8e8e8;
}

/* 筛选按钮样式 */
.filter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  background: transparent;
  border: none;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
  height: 44rpx;
  border-radius: 6rpx;
  flex-shrink: 0;
}

.filter-btn.active {
  color: #ff6b35;
  background: rgba(255, 107, 53, 0.08);
}

.filter-icon {
  font-size: 20rpx;
  margin-left: 8rpx;
  transition: all 0.3s;
}

.filter-btn.active .filter-icon {
  color: #ff6b35;
  transform: rotate(180deg);
}

.filter-text {
  font-size: 26rpx;
}

/* 排序筛选栏样式 */
.sort-filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #fff;
  margin-bottom: 16rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  margin: 0 24rpx 16rpx 24rpx;
}

.sort-buttons {
  display: flex;
  gap: 24rpx;
}

.sort-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  background: transparent;
  border: 2rpx solid #ccc;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
}

.sort-btn.active {
  background: #ccc;
  border: 2rpx solid #ccc;
  color: #333;
}

.sort-icon {
  font-size: 24rpx;
  font-weight: bold;
}

/* 消息筛选抽屉面板样式 */
.message-filter-drawer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.message-filter-drawer.show {
  visibility: visible;
  opacity: 1;
}

.drawer-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.drawer-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.message-filter-drawer.show .drawer-content {
  transform: translateY(0);
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.drawer-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.drawer-close {
  width: 60rpx;
  height: 60rpx;
  background: none;
  border: none;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 32rpx;
  height: 32rpx;
}

.drawer-body {
  flex: 1;
  padding: 20rpx 40rpx;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.time-filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.time-option {
  padding: 16rpx 32rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  border: 2rpx solid transparent;
}

.time-option.active {
  background: #e8f4fd;
  color: #1890ff;
  border-color: #1890ff;
}

.status-filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.status-option {
  padding: 16rpx 32rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  border: 2rpx solid transparent;
}

.status-option.active {
  background: #e8f4fd;
  color: #1890ff;
  border-color: #1890ff;
}

.drawer-footer {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 40rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
}

.filter-clear-btn {
  flex: 1;
  height: 88rpx;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
}

.filter-apply-btn {
  flex: 1;
  height: 88rpx;
  background: #1890ff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
}

/* 搜索栏内的样式调整 */

.user-mgr-search-bar {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0;
  justify-content: center;
  background: none;
  box-shadow: none;
}

.user-mgr-search-input-container {
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  padding: 0 8rpx;
  position: relative;
  flex: 1;
}

.user-mgr-search-input {
  flex: 1;
  height: 100%;
  font-size: 30rpx;
  padding: 0 80rpx 0 32rpx;
  border: none;
  background: transparent;
  outline: none;
}

/* 搜索框提示语样式 */
.user-mgr-search-input::placeholder {
  font-size: 28rpx;
  color: #999;
}

.user-mgr-search-btn {
  width: 58rpx;
  height: 58rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  box-shadow: none;
  padding: 0;
}

.user-mgr-search-btn image {
  width: 48rpx;
  height: 48rpx;
}



/* 消息内容区域 */
.messages-content {
  min-height: 400rpx;
  max-height: 1000rpx;
  overflow-y: auto;
  padding: 10rpx 0; /* 减小50%的上下边距，移除左右边距 */
  /* 移除背景颜色 */
}

/* 消息列表容器 */
.message-list {
  padding: 0; /* 移除左右边距，防止卡片超出屏幕 */
}

/* 消息项容器 */
.message-item-container {
  position: relative;
  margin-bottom: 20rpx; /* 增加卡片间距 */
  margin-left: 20rpx; /* 添加左边距 */
  margin-right: 20rpx; /* 添加右边距 */
  overflow: hidden;
  width: calc(100% - 40rpx); /* 减去左右边距后的宽度 */
}

/* 增强版消息项 */
.message-item-enhanced {
  background: transparent; /* 背景由movable-view提供，避免重复 */
  border-radius: 0; /* 圆角由movable-view提供，避免重复 */
  margin-bottom: 0; /* 移除margin，避免重复间距 */
  padding: 24rpx;
  box-shadow: none; /* 阴影由movable-view提供，避免重复 */
  /* 移除所有可能导致双重边框的样式 */
}

/* 消息状态 */
.message-status {
  margin-left: 20rpx;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.status-badge.normal {
  background: #e6f7ff;
  color: #1890ff;
}

.status-badge.blocked {
  background: #fff2e8;
  color: #fa8c16;
}

/* 消息内容 */
.message-body {
  margin-bottom: 20rpx;
}

.message-content.blocked-content {
  color: #999;
  text-decoration: line-through;
}

/* 操作按钮 */
.message-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
  font-weight: 500;
  min-width: 120rpx;
}

.block-btn.block {
  background: #fff2e8;
  color: #fa8c16;
}

.block-btn.unblock {
  background: #e6f7ff;
  color: #1890ff;
}

.delete-btn {
  background: #fff1f0;
  color: #ff4d4f;
}

.action-btn:active {
  opacity: 0.7;
  transform: scale(0.98);
}

/* 消息内容样式 */
.message-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  word-wrap: break-word;
  word-break: break-all;
  max-height: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

.blocked-content {
  color: #999;
  text-decoration: line-through;
}

/* 文本消息样式 */
.text-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.text-truncated {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.expand-text {
  font-size: 24rpx;
  color: #1890ff;
  align-self: flex-start;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  background: rgba(24, 144, 255, 0.1);
}

/* 图片消息样式 */
.image-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.message-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  background: #f5f5f5;
}

.image-caption {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 其他类型消息样式 */
.other-content {
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #1890ff;
}

/* 滑动消息容器样式 - 已在上方定义，此处移除重复 */

.movable-area {
  width: 100%;
  height: auto;
  position: relative;
  overflow: hidden; /* 改为hidden避免内容溢出 */
  display: block; /* 改为block布局 */
  min-height: 120rpx; /* 设置最小高度 */
}

.movable-view {
  width: 100%; /* 保持100%宽度 */
  height: 100%; /* 改为100%高度 */
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
  z-index: 2;
  position: relative;
  min-height: 120rpx; /* 设置最小高度 */
}

/* 此样式已在上方统一定义，移除重复 */

/* 滑动操作按钮样式 */
.swipe-actions {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
  z-index: 1;
}

.action-item {
  width: 160rpx; /* 增加删除按钮宽度，因为移除了屏蔽按钮 */
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-text {
  font-size: 26rpx;
  color: white;
  margin-top: 8rpx;
}

.block-action.block {
  background: #fa8c16;
}

.block-action.unblock {
  background: #1890ff;
}

.delete-action {
  background: #ff4d4f;
  border-radius: 0 16rpx 16rpx 0;
}

.action-item:active {
  opacity: 0.8;
  transform: scale(0.95);
}

.delete-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1); /* 将图标变为白色 */
}

.delete-text {
  font-size: 22rpx;
  color: #fff;
  font-weight: 500;
}

.message-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.sender-info {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.sender-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  background: #f5f5f5;
}

.sender-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.sender-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.message-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.receiver-info {
  font-size: 24rpx;
  color: #666;
  display: inline-block;
}

.message-actions {
  display: flex;
  align-items: center;
}

.block-switch-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.block-switch {
  transform: scale(0.8);
}

.block-label {
  font-size: 20rpx;
  color: #666;
  margin-top: 4rpx;
}

.message-content {
  padding: 24rpx;
  padding-bottom: 16rpx;
}

.message-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  word-break: break-all;
}

.message-footer {
  padding: 0 24rpx 24rpx;
}

.message-type {
  display: inline-block;
  padding: 4rpx 12rpx;
  background: #f0f0f0;
  color: #666;
  font-size: 22rpx;
  border-radius: 4rpx;
}

/* 常见问题管理样式 */
.faq-section {
  margin-top: 24rpx;
}

.actions-row {
  padding: 0 24rpx 24rpx;
}

.add-btn {
  width: 100%;
  padding: 24rpx;
  background: #fff;
  color: #ff4d4f;
  border: 2rpx solid #ff4d4f;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
}

.faq-content {
  padding: 0 24rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 24rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.empty-hint {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.faq-edit-card {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.1);
  overflow: hidden;
  position: relative;
}

.card-header {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #e8e8e8;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 40rpx;
  height: 40rpx;
  cursor: pointer;
}

.modal-body {
  flex: 1;
  padding: 32rpx;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  border-top: 1rpx solid #e8e8e8;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
}

.modal-btn.cancel {
  background: #f5f5f5;
  color: #666;
}

.modal-btn.confirm {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: #fff;
}

/* 页面类型选择器样式 */
.page-type-mode-switch {
  display: flex;
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 4rpx;
  margin-bottom: 16rpx;
}

.mode-tab {
  flex: 1;
  text-align: center;
  padding: 12rpx;
  font-size: 24rpx;
  color: #666;
  border-radius: 6rpx;
  transition: all 0.3s;
}

.mode-tab.active {
  background: #fff;
  color: #ff4d4f;
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}

.page-type-select {
  margin-bottom: 16rpx;
}

.form-picker {
  padding: 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
}

.page-type-input {
  margin-bottom: 16rpx;
}

.input-tip {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 图片上传样式 */
.image-upload {
  margin-bottom: 16rpx;
}

.uploaded-image {
  position: relative;
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: rgba(0,0,0,0.7);
  padding: 16rpx;
}

.img-btn {
  flex: 1;
  padding: 8rpx 16rpx;
  background: transparent;
  color: #fff;
  border: 1rpx solid #fff;
  border-radius: 6rpx;
  font-size: 22rpx;
  margin: 0 4rpx;
}

.img-btn.delete {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.upload-placeholder {
  width: 100%;
  height: 300rpx;
  border: 2rpx dashed #d9d9d9;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-placeholder:hover {
  border-color: #ff4d4f;
  background: #fff5f5;
}

.upload-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  opacity: 0.5;
}

.upload-text {
  font-size: 28rpx;
  color: #999;
}

/* 图片状态指示器 */
.image-status-indicator {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
  color: #fff;
  z-index: 10;
  font-weight: 500;
}

.image-status-indicator.error {
  background: rgba(255, 77, 79, 0.9);
}

.image-status-indicator.retry {
  background: rgba(255, 165, 0, 0.9);
}

.image-status-indicator.success {
  background: rgba(82, 196, 26, 0.9);
}

/* 安全区域 */
.safe-area {
  height: 120rpx;
}