所有的资金账户逻辑说明

一、用户的账户
1、【用户余额】
，，充值：用户可以直接通过微信或银行卡充值，充值可以增加余额（功能暂不开发）
，，提现：用户将资金直接提现到微信或银行卡，提现将减少余额（功能暂不开发）
，，余额调整：这是管理端的功能，可以当作是一种间接的充值和提现功能，通过管理端增加或减少用户的余额
，，支付：余额可以支付购物订单，支付将减少余额（这是最主要应用）


2、【待结销售分佣】
，，当顾客订单支付成功后，按照佣金计算方式，其“销售人”将获得佣金，因为订单未完成，暂时计入待结状态，这就是【待结销售分佣】（这是一种暂时状态，只为让用户了解即将获得的收益）
，，当顾客订单收货完成后，【待结销售分佣】的资金划入到【用户余额】
，，佣金计算公式：

3、【累计分红】
，，当门店分红时，每个合伙人可以按分红比例获得收益，分红的资金是直接从门店的【分红池】划入到【用户余额】，这里仅是统计用户通过分红方式获得的累计收益。


4、【累计提现】
，，当用户从余额账户提现后，这里统计用户提现的累计总金额（某种程序上可以反应出用户在平台赚取的收益）


二、门店的账户
1、【股本金】
，，【股本金】由管理员增减（这一般是根据合伙人加入情况，或其他需求而操作的，会产生相应的操作记录）
，，【股本金】只能用于支付采购订单的货款，合伙人无法支取股本金，支付货款时，股本金减少；
，，门店的商品产生销售时（即顾客订单归属门店），当订单完成后，销售的商品按采购价回收成本到【股本金】账户

2、【利润池】
，，门店的商品产生销售时（即顾客订单归属门店），当订单完成后，商品的销售利润归属门店部分会划入到门店的【利润池】账户
，，【利润池】



3、分红池


4、公积金