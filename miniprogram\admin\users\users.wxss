.user-mgr-container {
  background: #f7f7f7;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 合并并精简顶部四栏相关样式，确保每个类只保留一份定义 */
.user-mgr-header {
  background: #fff;
  padding: 10rpx 24rpx 0 24rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: center;
  height: auto;
  box-sizing: border-box;
}
.user-mgr-search-bar {
  display: flex;
  align-items: center;
  width: 90%;
  max-width: 700rpx;
  min-width: 240rpx;
  padding: 0;
  justify-content: center;
  background: none;
  box-shadow: none;
  margin-bottom: 32rpx;
}
.user-mgr-search-input-container {
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  padding: 0 8rpx;
  position: relative;
  flex: 1;
}
.user-mgr-search-input {
  flex: 1;
  height: 100%;
  font-size: 34rpx;
  padding: 0 80rpx 0 32rpx;
  border: none;
  background: transparent;
  outline: none;
}
.user-mgr-search-btn {
  width: 58rpx;
  height: 58rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  box-shadow: none;
  padding: 0;
}
.user-mgr-search-btn image {
  width: 48rpx;
  height: 48rpx;
}
.user-mgr-actions {
  display: flex;
  gap: 16rpx;
  padding: 0 24rpx;
  background: #fff;
  margin-bottom: 28rpx;
  height: auto;
  box-sizing: border-box;
}
.user-mgr-btn {
  flex: 1;
  background: #ff4d4f;
  color: #fff;
  border-radius: 16rpx;
  font-size: 26rpx;
  padding: 16rpx 0;
  border: none;
}
.user-mgr-btn-disabled {
  background: #e0e0e0 !important;
  color: transparent !important;
  pointer-events: none;
  border: none;
}
/* 分类标签栏 */
.category-tabs {
  width: 100%;
  white-space: nowrap;
  background: #fff;
  padding: 0 0 10rpx 0;
  margin-bottom: 10rpx;
}
.category-tab-list {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 18rpx;
  padding: 0 24rpx;
}
.category-tab {
  display: inline-block;
  padding: 10rpx 32rpx;
  font-size: 30rpx;
  color: #888;
  border-radius: 24rpx;
  background: #f7f7f7;
  margin-right: 0;
  transition: all 0.2s;
}
.category-tab.active {
  color: #ff4d4f;
  background: #fff0f0;
  font-weight: bold;
  border: 2rpx solid #ff4d4f;
}
/* 排序筛选栏样式 */
.sort-filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #fff;
  margin-bottom: 16rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  margin: 0 24rpx 16rpx 24rpx;
}

.sort-buttons {
  display: flex;
  gap: 24rpx;
}

.sort-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  background: transparent;
  border: 2rpx solid #ccc;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
}

.sort-btn.active {
  background: #ccc;
  border: 2rpx solid #ccc;
  color: #333;
}

.sort-icon {
  font-size: 24rpx;
  font-weight: bold;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
}

.filter-btn.applied {
  background: #1890ff;
  color: #fff;
}

.filter-icon {
  font-size: 20rpx;
  transition: transform 0.3s;
}

/* 筛选面板样式 */
.filter-panel {
  background: #fff;
  margin: 0 24rpx 16rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.filter-panel.show {
  max-height: 800rpx;
  padding: 24rpx;
}

.filter-section {
  margin-bottom: 32rpx;
}

.filter-section:last-of-type {
  margin-bottom: 24rpx;
}

.filter-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.status-options {
  display: flex;
  gap: 16rpx;
}

.status-option {
  flex: 1;
  padding: 16rpx;
  text-align: center;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  background: #fafafa;
  transition: all 0.3s;
}

.status-option.selected {
  border-color: #ff4d4f;
  background: #fff0f0;
  color: #ff4d4f;
  font-weight: bold;
}

.filter-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 8rpx;
}

.filter-clear-btn {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.filter-apply-btn {
  flex: 1;
  height: 72rpx;
  background: #ff4d4f;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.sortable-header.sorted-asc::after,
.sortable-header.sorted-desc::after {
  display: none;
}

.user-mgr-list {
  margin: 0 0 24rpx 0;
  background: #f7f7f7;
}
.user-mgr-item {
  display: flex;
  align-items: center;
  background: #fff;
  margin: 5rpx 15rpx 0 15rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx #eee;
  padding: 18rpx 18rpx;
  position: relative;
}
.user-mgr-checkbox {
  margin-right: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  cursor: pointer;
  background-color: transparent;
  transition: all 0.2s ease;
}

.user-mgr-checkbox image {
  width: 48rpx;
  height: 48rpx;
}

.user-mgr-checkbox.selected {
  background-color: transparent;
}

.user-mgr-checkbox:hover {
  transform: scale(1.05);
}
.user-mgr-avatar {
  width: 82.8rpx;
  height: 82.8rpx;
  border-radius: 50%;
  margin-right: 18rpx;
  border: 2rpx solid #f0f0f0;
  background: #fafafa;
}
.user-mgr-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}
.user-mgr-nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.user-mgr-id,
.user-mgr-phone,
.user-mgr-date {
  font-size: 24rpx;
  color: #888;
}
.user-mgr-status-top {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 24rpx;
  padding: 4rpx 18rpx;
  border-radius: 16rpx 0 0 16rpx;
  font-weight: bold;
  z-index: 2;
  border: 2rpx solid #e0e0e0;
  box-shadow: 0 2rpx 8rpx #eee;
  background: #fff;
}
.user-mgr-edit-btn {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  background: #f5f5f5;
  color: #1765d5;
  border-radius: 12rpx;
  padding: 8rpx 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  cursor: pointer;
  z-index: 2;
}
.user-mgr-status-edit, .user-mgr-edit, .user-mgr-edit-icon {
  display: none !important;
}
.user-mgr-footer {
  text-align: center;
  font-size: 28rpx;
  color: #888;
  background: #fff;
  padding: 24rpx 0 24rpx 0;
  margin-top: 24rpx;
}

/* 选中数量样式 */
.selected-count {
  color: #ff4d4f;
  font-weight: bold;
  margin-left: 16rpx;
}
.user-mgr-nickname-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.user-mgr-role {
  font-size: 22rpx;
  padding: 2rpx 16rpx;
  border-radius: 12rpx;
  font-weight: 500;
  margin-left: 8rpx;
  line-height: 28rpx;
}
.user-mgr-role-customer {
  background: #f5f5f5;
  color: #888;
  border: 1rpx solid #e0e0e0;
}
.user-mgr-role-partner {
  background: #e6f0ff;
  color: #1765d5;
  border: 1rpx solid #b3d1ff;
}
.user-mgr-role-admin {
  background: #fff7e6;
  color: #fa8c16;
  border: 1rpx solid #ffe1b8;
}
.user-mgr-checkbox-custom {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #bbb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 18rpx;
  background: #fff;
  cursor: pointer;
}
.user-mgr-checkbox-custom.checked {
  border-color: #ff4d4f;
  background: #ffeded;
}
.checkbox-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 搜索状态样式 */
.user-mgr-searching {
  text-align: center;
  padding: 40rpx 0;
  background: #fff;
  margin: 16rpx 24rpx;
  border-radius: 16rpx;
  color: #666;
  font-size: 28rpx;
}

/* 搜索结果提示样式 */
.user-mgr-search-result {
  text-align: center;
  padding: 20rpx 0;
  background: #f0f9ff;
  margin: 16rpx 24rpx;
  border-radius: 16rpx;
  color: #1765d5;
  font-size: 26rpx;
  border: 1rpx solid #b3d1ff;
}

.user-mgr-top-sticky {
  background: #fff;
}

/* 用户调整半屏弹窗 */
.user-edit-drawer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.user-edit-drawer.show {
  display: flex;
  opacity: 1;
  visibility: visible;
}

.user-edit-content {
  width: 100%;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow-y: auto;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.user-edit-drawer.show .user-edit-content {
  transform: translateY(0);
}

.user-edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.user-edit-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.user-edit-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.user-edit-body {
  padding: 30rpx;
}

.user-edit-section {
  margin-bottom: 40rpx;
}

.user-edit-section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #eee;
}

.user-edit-field {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-edit-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.user-edit-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.user-edit-input {
  flex: 1;
  height: 60rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
}

.user-edit-picker {
  flex: 1;
}

.user-edit-picker-text {
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
}

.user-edit-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.user-edit-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.user-edit-btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.user-edit-btn-save {
  background: #007aff;
  color: #fff;
}