const { partnerApi } = require('../../utils/api');
const partnerErrorHandler = require('../../utils/partner-error-handler');

Page({
  data: {
    storeList: [], // 门店列表
    loading: true, // 加载状态
    isEmpty: false // 是否为空
  },

  onLoad() {
    console.log('我的门店页面加载');
    this.loadStoreList();
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadStoreList();
  },

  // 加载门店列表
  async loadStoreList() {
    try {
      this.setData({ loading: true });
      
      console.log('开始加载门店列表...');
      const result = await partnerApi.getPartnerStores();
      
      if (result.success) {
        const storeList = result.data || [];
        
        // 为每个门店获取合伙人数量
        const storeListWithPartnerCount = await Promise.all(
          storeList.map(async (store) => {
            try {
              const partnersResult = await partnerApi.getStorePartners(store.store_no);
              const partnerCount = partnersResult.success ? (partnersResult.data || []).length : 0;
              
              return {
                ...store,
                partner_count: partnerCount,
                // 格式化地址
                full_address: store.address || '地址未设置',
                // 格式化门店状态
                status_text: this.getStoreStatusText(store.status || 'active'),
                // 格式化门店级别
                level_text: store.level_title || this.getLevelText(store.level)
              };
            } catch (error) {
              console.error(`获取门店 ${store.store_no} 合伙人数量失败:`, error);
              return {
                ...store,
                partner_count: 0,
                full_address: store.address || '地址未设置',
                status_text: this.getStoreStatusText(store.status || 'active'),
                level_text: store.level_title || this.getLevelText(store.level)
              };
            }
          })
        );
        
        console.log('门店列表加载成功:', storeListWithPartnerCount);
        
        this.setData({
          storeList: storeListWithPartnerCount,
          isEmpty: storeListWithPartnerCount.length === 0,
          loading: false
        });
      } else {
        console.error('获取门店列表失败:', result.message);
        this.setData({
          storeList: [],
          isEmpty: true,
          loading: false
        });
        partnerErrorHandler.showError(result.message || '获取门店列表失败');
      }
    } catch (error) {
      console.error('加载门店列表异常:', error);
      this.setData({
        storeList: [],
        isEmpty: true,
        loading: false
      });
      partnerErrorHandler.handlePartnerApiError(error, {
        defaultMessage: '加载门店列表失败，请稍后重试'
      });
    }
  },

  // 获取门店状态文本
  getStoreStatusText(status) {
    const statusMap = {
      'active': '正常营业',
      'inactive': '暂停营业',
      'closed': '已关闭',
      'pending': '待审核'
    };
    return statusMap[status] || '未知状态';
  },

  // 获取门店级别文本
  getLevelText(level) {
    const levelMap = {
      'L1': '一星门店',
      'L2': '二星门店', 
      'L3': '三星门店',
      'L4': '四星门店',
      'L5': '五星门店'
    };
    return levelMap[level] || '三星门店';
  },



  // 跳转到门店合伙人页面
  goToStorePartners(store) {
    wx.navigateTo({
      url: `/partner/store-partners/store-partners?storeNo=${store.store_no}&storeName=${encodeURIComponent(store.name)}`
    });
  },

  // 跳转到门店库存页面
  goToStoreInventory(store) {
    wx.navigateTo({
      url: `/partner/inventory/inventory?storeNo=${store.store_no}&storeName=${encodeURIComponent(store.name)}`
    });
  },

  // 跳转到门店资金页面
  goToStoreFunds(store) {
    wx.navigateTo({
      url: `/partner/store-funds/store-funds?storeNo=${store.store_no}&storeName=${encodeURIComponent(store.name)}`
    });
  },

  // 跳转到门店订单页面
  goToStoreOrders(store) {
    wx.navigateTo({
      url: `/partner/store-orders/store-orders?storeNo=${store.store_no}&storeName=${encodeURIComponent(store.name)}`
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    console.log('下拉刷新门店列表');
    this.loadStoreList().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 返回上一页
  onBackTap() {
    wx.navigateBack();
  }
});