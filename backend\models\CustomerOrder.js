const db = require('../config/db');

/**
 * 顾客主订单模型
 */
class CustomerOrder {
  /**
   * 创建顾客主订单
   * @param {Object} orderData - 订单数据
   * @returns {Object} 创建结果
   */
  static async create(orderData) {
    const {
      order_no,
      user_id,
      salesman_id,
      delivery_method,
      address_id,
      store_no,
      payment_methods,
      total_amount,
      total_quantity,
      sub_order_count,
      status = '待支付'
    } = orderData;

    const now = Date.now();
    const sql = `
      INSERT INTO customer_orders (
        order_no, user_id, salesman_id, delivery_method, address_id, store_no,
        payment_methods, total_amount, total_quantity, sub_order_count, status,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await db.query(sql, [
      order_no, user_id, salesman_id, delivery_method, address_id, store_no,
      JSON.stringify(payment_methods), total_amount, total_quantity, sub_order_count, status,
      now, now
    ]);

    return { id: result.insertId, order_no };
  }

  /**
   * 根据ID查找订单
   * @param {number} id - 订单ID
   * @returns {Object|null} 订单信息
   */
  static async findById(id) {
    const sql = 'SELECT * FROM customer_orders WHERE id = ?';
    const result = await db.query(sql, [id]);
    return result.length > 0 ? result[0] : null;
  }

  /**
   * 根据订单号查找订单
   * @param {string} orderNo - 订单号
   * @returns {Object|null} 订单信息
   */
  static async findByOrderNo(orderNo) {
    const sql = 'SELECT * FROM customer_orders WHERE order_no = ?';
    const result = await db.query(sql, [orderNo]);
    return result.length > 0 ? result[0] : null;
  }

  /**
   * 更新订单状态
   * @param {number} id - 订单ID
   * @param {string} status - 新状态
   * @param {Object} extraFields - 额外字段
   * @returns {boolean} 更新结果
   */
  static async updateStatus(id, status, extraFields = {}) {
    const now = Date.now();
    let sql = 'UPDATE customer_orders SET status = ?, updated_at = ?';
    let params = [status, now];

    // 处理额外字段
    if (extraFields.paid_at) {
      sql += ', paid_at = ?';
      params.push(extraFields.paid_at);
    }
    if (extraFields.shipped_at) {
      sql += ', shipped_at = ?';
      params.push(extraFields.shipped_at);
    }
    if (extraFields.completed_at) {
      sql += ', completed_at = ?';
      params.push(extraFields.completed_at);
    }

    sql += ' WHERE id = ?';
    params.push(id);

    const result = await db.query(sql, params);
    return result.affectedRows > 0;
  }

  /**
   * 获取用户订单列表
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Array} 订单列表
   */
  static async findByUserId(userId, options = {}) {
    const { limit = 20, offset = 0, status } = options;
    
    let sql = 'SELECT * FROM customer_orders WHERE user_id = ?';
    let params = [userId];

    if (status) {
      sql += ' AND status = ?';
      params.push(status);
    }

    sql += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    params.push(limit, offset);

    return await db.query(sql, params);
  }

  /**
   * 获取订单详情（包含子订单和商品明细）
   * @param {number} id - 订单ID
   * @returns {Object|null} 完整订单信息
   */
  static async getOrderDetail(id) {
    // 获取主订单信息
    const mainOrder = await this.findById(id);
    if (!mainOrder) return null;

    // 获取子订单列表
    const subOrders = await CustomerSubOrder.findByMainOrderId(id);

    // 获取商品明细
    const items = await CustomerOrderItem.findByMainOrderId(id);

    return {
      ...mainOrder,
      sub_orders: subOrders,
      items: items
    };
  }

  /**
   * 统计订单数量
   * @param {string} userId - 用户ID
   * @param {string} status - 订单状态
   * @returns {number} 订单数量
   */
  static async countByUser(userId, status = null) {
    let sql = 'SELECT COUNT(*) as count FROM customer_orders WHERE user_id = ?';
    let params = [userId];

    if (status) {
      sql += ' AND status = ?';
      params.push(status);
    }

    const result = await db.query(sql, params);
    return result[0].count;
  }
}

/**
 * 顾客子订单模型
 */
class CustomerSubOrder {
  /**
   * 创建子订单
   * @param {Object} subOrderData - 子订单数据
   * @returns {Object} 创建结果
   */
  static async create(subOrderData) {
    const {
      main_order_id,
      sub_order_no,
      store_no,
      store_type,
      sub_total_amount,
      sub_total_quantity,
      status = '待支付'
    } = subOrderData;

    const now = Date.now();
    const sql = `
      INSERT INTO customer_sub_orders (
        main_order_id, sub_order_no, store_no, store_type,
        sub_total_amount, sub_total_quantity, status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await db.query(sql, [
      main_order_id, sub_order_no, store_no, store_type,
      sub_total_amount, sub_total_quantity, status, now, now
    ]);

    return { id: result.insertId, sub_order_no };
  }

  /**
   * 根据主订单ID查找子订单
   * @param {number} mainOrderId - 主订单ID
   * @returns {Array} 子订单列表
   */
  static async findByMainOrderId(mainOrderId) {
    const sql = 'SELECT * FROM customer_sub_orders WHERE main_order_id = ? ORDER BY id';
    return await db.query(sql, [mainOrderId]);
  }

  /**
   * 更新子订单状态
   * @param {number} id - 子订单ID
   * @param {string} status - 新状态
   * @param {Object} extraFields - 额外字段
   * @returns {boolean} 更新结果
   */
  static async updateStatus(id, status, extraFields = {}) {
    const now = Date.now();
    let sql = 'UPDATE customer_sub_orders SET status = ?, updated_at = ?';
    let params = [status, now];

    if (extraFields.shipped_at) {
      sql += ', shipped_at = ?';
      params.push(extraFields.shipped_at);
    }
    if (extraFields.completed_at) {
      sql += ', completed_at = ?';
      params.push(extraFields.completed_at);
    }

    sql += ' WHERE id = ?';
    params.push(id);

    const result = await db.query(sql, params);
    return result.affectedRows > 0;
  }

  /**
   * 批量更新主订单下的所有子订单状态
   * @param {number} mainOrderId - 主订单ID
   * @param {string} status - 新状态
   * @returns {boolean} 更新结果
   */
  static async updateStatusByMainOrderId(mainOrderId, status) {
    const now = Date.now();
    const sql = 'UPDATE customer_sub_orders SET status = ?, updated_at = ? WHERE main_order_id = ?';
    const result = await db.query(sql, [status, now, mainOrderId]);
    return result.affectedRows > 0;
  }
}

/**
 * 顾客订单商品明细模型
 */
class CustomerOrderItem {
  /**
   * 创建订单商品明细
   * @param {Object} itemData - 商品明细数据
   * @returns {Object} 创建结果
   */
  static async create(itemData) {
    const {
      main_order_id,
      sub_order_id,
      product_id,
      product_name,
      product_image,
      product_price,
      quantity,
      subtotal
    } = itemData;

    const now = Date.now();
    const sql = `
      INSERT INTO customer_order_items (
        main_order_id, sub_order_id, product_id, product_name, product_image,
        product_price, quantity, subtotal, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await db.query(sql, [
      main_order_id, sub_order_id, product_id, product_name, product_image,
      product_price, quantity, subtotal, now
    ]);

    return { id: result.insertId };
  }

  /**
   * 根据主订单ID查找商品明细
   * @param {number} mainOrderId - 主订单ID
   * @returns {Array} 商品明细列表
   */
  static async findByMainOrderId(mainOrderId) {
    const sql = 'SELECT * FROM customer_order_items WHERE main_order_id = ? ORDER BY sub_order_id, id';
    return await db.query(sql, [mainOrderId]);
  }

  /**
   * 根据子订单ID查找商品明细
   * @param {number} subOrderId - 子订单ID
   * @returns {Array} 商品明细列表
   */
  static async findBySubOrderId(subOrderId) {
    const sql = 'SELECT * FROM customer_order_items WHERE sub_order_id = ? ORDER BY id';
    return await db.query(sql, [subOrderId]);
  }
}

module.exports = {
  CustomerOrder,
  CustomerSubOrder,
  CustomerOrderItem
};
