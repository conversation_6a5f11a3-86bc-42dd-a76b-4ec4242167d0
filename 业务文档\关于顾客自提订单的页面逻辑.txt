关于顾客自提订单的页面逻辑

1、在【购物车】页，点击“结算”按钮，会跳转到【确认订单】页，默认是在“快递”卡片页面。

2、此时如果点击“自提”卡片，要切换到“自提”方式，即刻检查当前用户是否有“销售人”。
，，2.1如果当前用户没有销售人，则弹窗提示“暂无可供自提的门店，请选择快递方式，如有其他请求可联系客服处理”，弹窗显示两个按钮“取消”和“联系客服”（点击“取消”则回到快递方式页面；点击“联系客服”则跳转到【客服中心】页），这种情况下，不会跳转到“自提”卡片内（注意，当前用户如果没有“销售人”，是不能跳转到“自提”卡片页面的）；

，，2.2如果当前用户有“销售人”，此时才会跳转到“自提”卡片页面（注意，用户有“销售人”，就必定有“订阅门店”，没有“销售人”就没有“订阅门店”），此时，就要在自提门店栏，显示“订阅门店”（查询users表的subscribe_store_no字段）作为默认自提门店（就像快递方式下的默认收货地址一样）。


3、在此时的页面上，判断当前用户的所在城市（users表的province和city字段），如果与门店所在城市（stores表的province和city字段）不相同，则在自提门店栏下面以条幅形式提醒用户“您与门店不在同一城市，请确认是否要在此门店自提”。
，，3.2此时，如果用户点击“提交订单”按钮，检查当前选中的自提门店的线下库存是否足够（即store_inventory表的offline_quantity字段），如果该商品在选中的门店的线下库存不足，则弹出提示“线下库存不足，无法自提，请选择快递方式”；
，，3.2此时如果用户进入【选择自提门店】页点击选择自提门店时，即检查所选门店的线下库存，如果库存不足，同样弹出提示“线下库存不足，无法自提，请选择快递方式”）；
，，3.3选择了新的门店后，再次判断当前用户的所在城市与门店所在城市是否相同，如果不同，则在自提门店栏下面以条幅形式提醒用户“您与门店不在同一城市，请确认是否要在此门店自提”。

4、在前面都正常的情况下（库存足够、城市没有不相同的提醒），则可以正常提交和创建订单。
，，4.1当有条幅提示城市不同的情况下，用户仍然点击“提交订单”的，则继续弹窗提醒您与门店不在同一城市，请确认是否要在此门店自提”，让用户再次确认后，才提交订单。


