<!-- partner/store-orders/detail.wxml -->
<!-- 门店订单详情页面 -->
<view class="order-detail-page">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:elif="{{error}}">
    <icon type="warn" size="64"></icon>
    <view class="error-text">{{errorMsg || '加载订单详情失败'}}</view>
    <button class="retry-btn" bindtap="loadOrderDetail">重试</button>
  </view>

  <!-- 订单详情内容 -->
  <block wx:elif="{{orderDetail}}">
    <!-- 订单头部信息 -->
    <view class="order-header">
      <view class="order-type-tag {{orderDetail.order_type === 'purchase' ? 'purchase' : 'transfer'}}">
        {{orderDetail.order_type === 'purchase' ? '采购订单' : '移库订单'}}
      </view>
      <view class="order-status">
        <text class="status-text {{orderDetail.status}}">{{orderDetail.status_text}}</text>
      </view>
    </view>

    <!-- 订单基本信息 -->
    <view class="order-info-card">
      <view class="info-item">
        <text class="info-label">订单编号：</text>
        <text class="info-value">{{orderDetail.order_no}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">下单时间：</text>
        <text class="info-value">{{orderDetail.created_at}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">门店信息：</text>
        <text class="info-value">{{orderDetail.store_name}}({{orderDetail.store_no}})</text>
      </view>
      <view class="info-item">
        <text class="info-label">操作人员：</text>
        <text class="info-value">{{orderDetail.operator_name}}</text>
      </view>
      
      <!-- 采购订单特有信息 -->
      <block wx:if="{{orderDetail.order_type === 'purchase'}}">
        <view class="info-item" wx:if="{{orderDetail.status === 'reviewed'}}">
          <text class="info-label">审核时间：</text>
          <text class="info-value">{{orderDetail.review_time}}</text>
        </view>
      </block>
      
      <!-- 移库订单特有信息 -->
      <block wx:else>
        <view class="info-item" wx:if="{{orderDetail.logistics_status}}">
          <text class="info-label">物流状态：</text>
          <text class="info-value">{{orderDetail.logistics_status}}</text>
        </view>
        <view class="info-item" wx:if="{{orderDetail.shipped_time}}">
          <text class="info-label">发货时间：</text>
          <text class="info-value">{{orderDetail.shipped_time}}</text>
        </view>
        <view class="info-item" wx:if="{{orderDetail.received_time}}">
          <text class="info-label">到店时间：</text>
          <text class="info-value">{{orderDetail.received_time}}</text>
        </view>
      </block>
    </view>

    <!-- 商品列表 -->
    <view class="products-card">
      <view class="card-title">商品信息</view>
      <view class="product-list">
        <block wx:for="{{orderDetail.items}}" wx:key="id">
          <view class="product-item">
            <image class="product-image" src="{{item.product_image || '/images/icons2/默认商品.png'}}" mode="aspectFit"></image>
            <view class="product-info">
              <view class="product-name">{{item.product_name}}</view>
              <view class="product-specs" wx:if="{{item.specs}}">规格：{{item.specs}}</view>
              <view class="product-price-qty">
                <view class="price-info">
                  <text class="price-label">采购价：</text>
                  <text class="price-value">¥{{item.price}}</text>
                </view>
                <view class="qty-info">
                  <text class="qty-label">数量：</text>
                  <text class="qty-value">{{item.quantity}}</text>
                </view>
                <view class="amount-info">
                  <text class="amount-label">金额：</text>
                  <text class="amount-value">¥{{item.amount || '0.00'}}</text>
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>

    <!-- 订单金额信息 -->
    <view class="amount-card">
      <view class="amount-row">
        <text class="amount-label">商品总数：</text>
        <text class="amount-value">{{orderDetail.total_quantity || 0}}件</text>
      </view>
      <view class="amount-row">
        <text class="amount-label">订单总金额：</text>
        <text class="amount-value price">¥{{orderDetail.total_amount}}</text>
      </view>
    </view>

    <!-- 订单操作按钮 -->
    <view class="action-bar">
      <!-- 采购订单：合伙人端不显示审核按钮，审核功能仅限管理端 -->
      <block wx:if="{{orderDetail.order_type === 'purchase'}}">
        <!-- 采购订单在合伙人端无操作按钮，审核权限仅限管理员 -->
      </block>
      <!-- 移库订单：保留发货和确认到店功能 -->
      <block wx:else>
        <button class="action-btn primary" wx:if="{{orderDetail.status === 'pending_shipment'}}" bindtap="shipOrder">发货</button>
        <button class="action-btn primary" wx:if="{{orderDetail.status === 'shipped'}}" bindtap="receiveOrder">确认到店</button>
      </block>
    </view>
  </block>
</view>