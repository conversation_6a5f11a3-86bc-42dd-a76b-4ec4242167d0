/**
 * 合伙人统计控制器
 */
const UserFundAccount = require('../models/UserFundAccount');
const db = require('../config/db');

/**
 * 获取合伙人统计信息
 * GET /api/partner/stats
 */
exports.getPartnerStats = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      console.error('获取合伙人统计信息失败 - 用户ID不存在');
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    console.log('获取合伙人统计信息 - 用户ID:', userId);
    console.log('请求头信息:', JSON.stringify(req.headers, null, 2));
    console.log('用户数据:', JSON.stringify(req.userData, null, 2));

    // 获取合伙人完整统计信息
    const stats = await UserFundAccount.getPartnerStats(userId);

    console.log('合伙人统计信息获取成功:', {
      fundAccount: stats.fundAccount,
      referralStats: stats.referralStats
    });

    // 确保返回的数据格式正确
    const responseData = {
      success: true,
      data: {
        fundAccount: {
          user_id: stats.fundAccount.user_id,
          account_balance: parseFloat(stats.fundAccount.account_balance || 0),
          pending_commission: parseFloat(stats.fundAccount.pending_commission || 0),
          total_commission: parseFloat(stats.fundAccount.total_commission || 0),
          total_withdrawal: parseFloat(stats.fundAccount.total_withdrawal || 0),
          total_dividend: parseFloat(stats.fundAccount.total_dividend || 0)
        },
        referralStats: {
          user_id: stats.referralStats.user_id,
          referral_count: parseInt(stats.referralStats.referral_count || 0),
          store_count: parseInt(stats.referralStats.store_count || 0)
        }
      }
    };

    console.log('最终返回的响应数据:', JSON.stringify(responseData, null, 2));

    res.json(responseData);
  } catch (error) {
    console.error('获取合伙人统计信息失败 - 详细错误:', error);
    console.error('错误堆栈:', error.stack);
    res.status(500).json({ 
      success: false, 
      message: '获取统计信息失败',
      error: error.message,
      details: error.stack
    });
  }
};

/**
 * 获取合伙人钱包统计信息
 * GET /api/partner/wallet/stats
 */
exports.getPartnerWalletStats = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      console.error('获取合伙人钱包统计失败 - 用户ID不存在');
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    console.log('获取合伙人钱包统计 - 用户ID:', userId);

    // 获取用户资金账户信息
    const fundAccountQuery = `
      SELECT 
        account_balance as balance,
        pending_commission,
        total_commission,
        total_dividend,
        total_withdrawal as total_withdraw
      FROM user_fund_accounts 
      WHERE user_id = ?
    `;
    
    const fundAccountResult = await db.query(fundAccountQuery, [userId]);
    
    let walletStats = {
      balance: '0.00',
      pending_commission: '0.00',
      total_commission: '0.00',
      total_dividend: '0.00',
      total_withdraw: '0.00'
    };
    
    if (fundAccountResult.length > 0) {
      const account = fundAccountResult[0];
      walletStats = {
        balance: parseFloat(account.balance || 0).toFixed(2),
        pending_commission: parseFloat(account.pending_commission || 0).toFixed(2),
        total_commission: parseFloat(account.total_commission || 0).toFixed(2),
        total_dividend: parseFloat(account.total_dividend || 0).toFixed(2),
        total_withdraw: parseFloat(account.total_withdraw || 0).toFixed(2)
      };
    }

    console.log('合伙人钱包统计获取成功:', walletStats);

    res.json({
      success: true,
      data: walletStats
    });
  } catch (error) {
    console.error('获取合伙人钱包统计失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '获取钱包统计失败',
      error: error.message
    });
  }
};

/**
 * 获取合伙人钱包明细记录
 * GET /api/partner/wallet/records
 */
exports.getPartnerWalletRecords = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    const { type = 'balance', limit = 20, offset = 0 } = req.query;
    
    console.log('获取合伙人钱包记录 - 用户ID:', userId, '类型:', type, '限制:', limit, '偏移:', offset);

    let whereCondition = 'WHERE user_id = ?';
    let queryParams = [userId];
    
    // 根据类型筛选记录
    switch (type) {
      case 'balance':
        // 余额变化记录：包括支付、提现等
        whereCondition += ` AND type IN ('payment', 'withdrawal')`;
        break;
      case 'commission':
        // 分佣记录：销售订单分佣
        whereCondition += ` AND type = 'commission'`;
        break;
      case 'dividend':
        // 分红记录：门店分红
        whereCondition += ` AND type = 'dividend'`;
        break;
      case 'withdraw':
        // 提现记录：用户提现
        whereCondition += ` AND type = 'withdrawal'`;
        break;
      default:
        // 默认显示所有记录
        break;
    }
    
    const recordsQuery = `
      SELECT 
        id,
        type as transaction_type,
        amount,
        balance,
        description,
        order_id,
        store_no,
        status,
        created_at
      FROM user_fund_records 
      ${whereCondition}
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `;
    
    queryParams.push(parseInt(limit), parseInt(offset));
    
    const records = await db.query(recordsQuery, queryParams);
    
    // 格式化记录数据
    const formattedRecords = records.map(record => ({
      id: record.id,
      transaction_type: record.transaction_type,
      amount: parseFloat(record.amount || 0).toFixed(2),
      balance: parseFloat(record.balance || 0).toFixed(2),
      description: record.description || exports.getTransactionTypeText(record.transaction_type),
      order_id: record.order_id,
      store_no: record.store_no,
      status: record.status,
      created_at: record.created_at,
      created_at_formatted: exports.formatDateTime(record.created_at)
    }));

    console.log(`获取到 ${formattedRecords.length} 条钱包记录`);

    res.json({
      success: true,
      data: formattedRecords
    });
  } catch (error) {
    console.error('获取合伙人钱包记录失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '获取钱包记录失败',
      error: error.message
    });
  }
};

/**
 * 获取交易类型显示文本
 */
exports.getTransactionTypeText = function(type) {
  const typeMap = {
    'commission': '销售分佣',
    'dividend': '门店分红',
    'payment': '订单支付',
    'withdrawal': '账户提现'
  };
  return typeMap[type] || '其他交易';
};

/**
 * 格式化日期时间
 */
exports.formatDateTime = function(dateTime) {
  if (!dateTime) return '';
  
  try {
    const date = new Date(dateTime);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hour}:${minute}`;
  } catch (error) {
    console.error('日期格式化失败:', error);
    return dateTime;
  }
};

/**
 * 获取合伙人资金变动记录
 * GET /api/partner/fund-records
 */
exports.getFundRecords = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    const { limit = 20, offset = 0 } = req.query;
    const records = await UserFundAccount.getFundRecords(userId, parseInt(limit), parseInt(offset));

    res.json({
      success: true,
      data: records
    });
  } catch (error) {
    console.error('获取资金变动记录失败:', error);
    res.status(500).json({ success: false, message: '获取记录失败' });
  }
};

/**
 * 获取合伙人订单统计
 * GET /api/partner/order-stats
 */
exports.getOrderStats = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    console.log('获取合伙人订单统计 - 用户ID:', userId);

    // 先获取当前用户的门店列表
    const userStoresQuery = `
      SELECT DISTINCT s.store_no 
      FROM stores s
      INNER JOIN partners p ON s.store_no = p.store_no
      WHERE p.user_id = ?
    `;
    const userStores = await db.query(userStoresQuery, [userId]);
    const userStoreNos = userStores.map(store => store.store_no);
    
    console.log('用户门店列表:', userStoreNos);

    // 根据业务文档，合伙人端查询顾客订单的逻辑包括两个条件：
    // 1. 销售人（salesman_id）为当前用户的订单
    // 2. 归属门店store_no在当前用户的门店列表中的订单（子订单）
    
    let orderStatsQuery;
    let queryParams;
    
    if (userStoreNos.length > 0) {
      // 构建门店条件
      const storeCondition = userStoreNos.map(() => '?').join(',');
      
      // 统计顾客订单：销售人为当前用户 OR 子订单归属门店在用户门店列表中
      orderStatsQuery = `
           SELECT 
             COUNT(CASE WHEN co.status = '待支付' THEN 1 END) as pending_payment,
             COUNT(CASE WHEN co.status = '待自提' THEN 1 END) as pending_pickup,
             COUNT(CASE WHEN co.status = '待发货' THEN 1 END) as pending_shipment,
             COUNT(CASE WHEN co.status = '已发货' THEN 1 END) as shipped,
             COUNT(CASE WHEN co.status = '已完成' THEN 1 END) as completed,
             COUNT(CASE WHEN co.status = '已签收' THEN 1 END) as signed,
             COUNT(CASE WHEN co.status IN ('已取消', '已退款') THEN 1 END) as returns
           FROM customer_orders co
           WHERE co.salesman_id = ? OR EXISTS (
             SELECT 1 FROM customer_sub_orders cso
             WHERE cso.main_order_id = co.id AND cso.store_no IN (${storeCondition})
           )
         `;
      queryParams = [userId, ...userStoreNos];
    } else {
      // 如果用户没有门店，只统计销售人为当前用户的订单
      orderStatsQuery = `
           SELECT 
             COUNT(CASE WHEN status = '待支付' THEN 1 END) as pending_payment,
             COUNT(CASE WHEN status = '待自提' THEN 1 END) as pending_pickup,
             COUNT(CASE WHEN status = '待发货' THEN 1 END) as pending_shipment,
             COUNT(CASE WHEN status = '已发货' THEN 1 END) as shipped,
             COUNT(CASE WHEN status = '已完成' THEN 1 END) as completed,
             COUNT(CASE WHEN status = '已签收' THEN 1 END) as signed,
             COUNT(CASE WHEN status IN ('已取消', '已退款') THEN 1 END) as returns
           FROM customer_orders 
           WHERE salesman_id = ?
         `;
      queryParams = [userId];
    }
    
    console.log('订单统计查询SQL:', orderStatsQuery);
    const orderStats = await db.query(orderStatsQuery, queryParams);

    console.log('订单统计结果:', orderStats[0]);

    res.json({
      success: true,
      data: orderStats[0] || {
        pending_payment: 0,
        pending_pickup: 0,
        pending_shipment: 0,
        shipped: 0,
        completed: 0,
        signed: 0,
        returns: 0
      }
    });
  } catch (error) {
    console.error('获取订单统计失败:', error);
    res.status(500).json({ success: false, message: '获取订单统计失败' });
  }
};

/**
 * 获取合伙人门店列表
 * GET /api/partner/stores
 */
exports.getPartnerStores = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    console.log('获取合伙人门店列表 - 用户ID:', userId);

    // 获取当前用户作为合伙人加入的门店信息
    // 使用JOIN条件而不是COLLATE以解决字符集问题
    const stores = await db.query(`
      SELECT DISTINCT 
        s.id,
        s.store_no,
        s.name,
        s.level,
        s.level_title,
        s.province,
        s.city,
        s.district,
        s.address,
        s.image,
        s.contact_person,
        s.phone as contact_phone,
        s.status,
        s.create_time,
        s.update_time,
        p.type as partner_type,
        p.amount as investment_amount,
        p.percent as share_percent,
        p.created_at
      FROM partners p
      JOIN stores s ON p.store_no = s.store_no
      WHERE p.user_id = ?
      ORDER BY p.created_at ASC
    `, [userId]);

    console.log('查询到的门店数量:', stores.length);
    if (stores.length > 0) {
      console.log('第一条门店记录:', stores[0]);
    }

    res.json({
      success: true,
      data: stores
    });
  } catch (error) {
    console.error('获取门店列表失败 - 详细错误:', error);
    console.error('错误堆栈:', error.stack);
    res.status(500).json({ 
      success: false, 
      message: '获取门店列表失败',
      error: error.message,
      details: error.stack
    });
  }
};

/**
 * 获取我推荐的合伙人列表
 * GET /api/partner/my-partner-referrals
 */
exports.getMyPartnerReferrals = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    const { page = 1, limit = 10, sortBy = 'join_date', sortOrder = 'desc' } = req.query;
    const offset = (page - 1) * limit;

    console.log('获取我推荐的合伙人列表 - 用户ID:', userId, '参数:', { page, limit, sortBy, sortOrder });

    // 构建排序字段映射
    const sortFieldMap = {
      'partner_type': 'p.type',
      'join_date': 'p.created_at'
    };
    const sortField = sortFieldMap[sortBy] || 'p.created_at';
    const order = sortOrder === 'asc' ? 'ASC' : 'DESC';

    // 根据业务文档：查询推荐人是当前用户，销售人是他本人的那些用户（因为成为合伙人就会变更销售人为他本人）
    const query = `
      SELECT 
        u.user_id,
        u.nickname,
        u.avatar,
        u.createTime as register_time,
        p.type as partner_type,
        p.created_at as join_time,
        s.name as store_name,
        s.store_no,
        CASE 
          WHEN p.type = 'A' THEN 'A型合伙人'
          WHEN p.type = 'B' THEN 'B型合伙人'
          WHEN p.type = 'C' THEN 'C型合伙人'
          WHEN p.type = 'M' THEN '门店管理员'
          ELSE '普通合伙人'
        END as partner_type_name
      FROM users u
      INNER JOIN partners p ON u.user_id = p.user_id
      LEFT JOIN stores s ON p.store_no = s.store_no
      WHERE u.referrerId = ? AND u.salesman_id = u.user_id
      ORDER BY ${sortField} ${order}
      LIMIT ? OFFSET ?
    `;

    const countQuery = `
      SELECT COUNT(*) as total
      FROM users u
      INNER JOIN partners p ON u.user_id = p.user_id
      WHERE u.referrerId = ? AND u.salesman_id = u.user_id
    `;

    const [list, countResult] = await Promise.all([
      db.query(query, [userId, parseInt(limit), parseInt(offset)]),
      db.query(countQuery, [userId])
    ]);

    const total = countResult[0]?.total || 0;

    // 格式化数据
    const formattedList = list.map(item => ({
      user_id: item.user_id,
      nickname: item.nickname,
      avatar: item.avatar,
      partner_type: item.partner_type,
      partner_type_name: item.partner_type_name,
      store_name: item.store_name || '暂无门店',
      store_no: item.store_no,
      join_date: item.join_time,
      join_date_formatted: item.join_time ? new Date(item.join_time).toLocaleDateString('zh-CN') : '',
      register_time: item.register_time
    }));

    console.log('我推荐的合伙人列表查询成功:', { total, count: formattedList.length });

    res.json({
      success: true,
      data: {
        list: formattedList,
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        hasMore: formattedList.length >= limit
      }
    });
  } catch (error) {
    console.error('获取我推荐的合伙人列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取推荐合伙人列表失败',
      error: error.message
    });
  }
};

/**
 * 获取我推荐的顾客列表
 * GET /api/partner/my-customer-referrals
 */
exports.getMyCustomerReferrals = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    const { page = 1, limit = 10, sortBy = 'register_date', sortOrder = 'desc' } = req.query;
    const offset = (page - 1) * limit;

    console.log('获取我推荐的顾客列表 - 用户ID:', userId, '用户ID类型:', typeof userId, '参数:', { page, limit, sortBy, sortOrder });

    // 确保userId是字符串类型（因为salesman_id字段是varchar类型）
    const salesmanId = String(userId);
    console.log('转换后的销售人ID:', salesmanId, '类型:', typeof salesmanId);

    // 先检查数据库中是否有以当前用户为销售人的用户
    const checkQuery = `
      SELECT COUNT(*) as count, 
             GROUP_CONCAT(DISTINCT u.user_id) as user_ids
      FROM users u 
      WHERE u.salesman_id = ?
    `;
    const checkResult = await db.query(checkQuery, [salesmanId]);
    console.log('数据库检查结果:', checkResult[0]);

    // 构建排序字段映射
    const sortFieldMap = {
      'register_date': 'u.createTime',
      'user_id': 'u.user_id',
      'total_consumption': 'COALESCE(order_stats.total_amount, 0)'
    };
    const sortField = sortFieldMap[sortBy] || 'u.createTime';
    const order = sortOrder === 'asc' ? 'ASC' : 'DESC';

    // 查询我推荐的顾客（以当前用户作为销售人的仅拥有顾客身份，没有合伙人身份的用户）
    const query = `
      SELECT 
        u.user_id,
        u.nickname,
        u.avatar,
        u.createTime as register_time,
        u.salesman_id,
        COALESCE(order_stats.total_amount, 0) as total_consumption
      FROM users u
      LEFT JOIN (
        SELECT 
          user_id,
          SUM(total_amount) as total_amount
        FROM customer_orders 
        WHERE status IN ('已完成', '已签收')
        GROUP BY user_id
      ) order_stats ON u.user_id = order_stats.user_id
      WHERE u.salesman_id = ?
        AND u.user_id NOT IN (
          SELECT DISTINCT user_id FROM partners
        )
      ORDER BY ${sortField} ${order}
      LIMIT ? OFFSET ?
    `;

    const countQuery = `
      SELECT COUNT(*) as total
      FROM users u
      WHERE u.salesman_id = ?
        AND u.user_id NOT IN (
          SELECT DISTINCT user_id FROM partners
        )
    `;

    console.log('执行查询SQL:', query);
    console.log('查询参数:', [salesmanId, parseInt(limit), parseInt(offset)]);

    const [list, countResult] = await Promise.all([
      db.query(query, [salesmanId, parseInt(limit), parseInt(offset)]),
      db.query(countQuery, [salesmanId])
    ]);

    const total = countResult[0]?.total || 0;
    console.log('查询结果 - 总数:', total, '当前页数据条数:', list.length);
    
    if (list.length > 0) {
      console.log('查询到的用户示例:', list[0]);
    }

    // 格式化数据
    const formattedList = list.map(item => ({
      user_id: item.user_id,
      nickname: item.nickname,
      avatar: item.avatar,
      register_time: item.register_time,
      register_date_formatted: item.register_time ? new Date(item.register_time).toLocaleDateString('zh-CN') : '',
      total_consumption: parseFloat(item.total_consumption || 0),
      total_consumption_formatted: parseFloat(item.total_consumption || 0).toFixed(2),
      salesman_id: item.salesman_id // 添加调试信息
    }));

    console.log('我推荐的顾客列表查询成功:', { total, count: formattedList.length });

    res.json({
      success: true,
      data: {
        list: formattedList,
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        hasMore: formattedList.length >= limit
      }
    });
  } catch (error) {
    console.error('获取我推荐的顾客列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取推荐顾客列表失败',
      error: error.message
    });
  }
};