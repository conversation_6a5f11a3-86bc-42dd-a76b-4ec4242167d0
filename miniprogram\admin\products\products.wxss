/* 保留主容器和flex布局 */
.product-admin-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100vh;
  background: #fff;
}
.product-mgr-header,
.product-mgr-actions,
.product-mgr-tabs {
  flex-shrink: 0;
}
.product-list {
  flex: 1;
  overflow-y: auto;
  margin: 0 0 24rpx 0;
  background: #f7f7f7;
}

/* 搜索栏相关 */
.user-mgr-header {
  background: #fff;
  padding: 10rpx 24rpx 0 24rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: center;
}
.user-mgr-search-bar {
  display: flex;
  align-items: center;
  width: 90%;
  max-width: 700rpx;
  min-width: 240rpx;
  padding: 0;
  justify-content: center;
  background: none;
  box-shadow: none;
  margin-bottom: 32rpx;
}
.user-mgr-search-input-container {
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  padding: 0 8rpx;
  position: relative;
  flex: 1;
}
.user-mgr-search-input {
  flex: 1;
  height: 100%;
  font-size: 34rpx;
  padding: 0 80rpx 0 32rpx;
  border: none;
  background: transparent;
  outline: none;
}
.user-mgr-search-btn {
  width: 58rpx;
  height: 58rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  box-shadow: none;
  padding: 0;
}
.user-mgr-search-btn image {
  width: 48rpx;
  height: 48rpx;
}

/* 顶部操作按钮栏 */
.user-mgr-actions {
  display: flex;
  gap: 16rpx;
  padding: 0 24rpx;
  background: #fff;
  margin-bottom: 28rpx;
}
.user-mgr-btn {
  flex: 1;
  background: #ff4d4f;
  color: #fff;
  border-radius: 16rpx;
  font-size: 26rpx;
  padding: 16rpx 0;
  border: none;
}
.user-mgr-btn-disabled {
  background: #e0e0e0 !important;
  color: transparent !important;
  pointer-events: none;
  border: none;
}

/* 分类标签栏 */
.category-tabs {
  width: 100%;
  white-space: nowrap;
  background: #fff;
  padding: 0 0 10rpx 0;
  margin-bottom: 10rpx;
}
.category-tab-list {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 18rpx;
  padding: 0 24rpx;
}
.category-tab {
  display: inline-block;
  padding: 10rpx 32rpx;
  font-size: 30rpx;
  color: #888;
  border-radius: 24rpx;
  background: #f7f7f7;
  margin-right: 0;
  transition: all 0.2s;
}
.category-tab.active {
  color: #ff4d4f;
  background: #fff0f0;
  font-weight: bold;
  border: 2rpx solid #ff4d4f;
}
/* 排序筛选栏样式 */
.sort-filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #fff;
  margin-bottom: 10rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  position: sticky;
  top: 210rpx;
  z-index: 17;
}

.sort-buttons {
  display: flex;
  gap: 24rpx;
}

.sort-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  background: transparent;
  border: 2rpx solid #ccc;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
}

.sort-btn.active {
  background: #ccc;
  border: 2rpx solid #ccc;
  color: #333;
}

.sort-icon {
  font-size: 24rpx;
  font-weight: bold;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
}

.filter-btn.applied {
  background: #1890ff;
  color: #fff;
}

.filter-icon {
  font-size: 20rpx;
  transition: transform 0.3s;
}

/* 筛选面板样式 */
.filter-panel {
  background: #fff;
  margin: 0 24rpx 16rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.filter-panel.show {
  max-height: 800rpx;
  padding: 24rpx;
}

.filter-section {
  margin-bottom: 32rpx;
}

.filter-section:last-of-type {
  margin-bottom: 24rpx;
}

.filter-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.price-range {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.price-input {
  flex: 1;
  height: 72rpx;
  padding: 0 16rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fafafa;
}

.price-input:focus {
  border-color: #ff4d4f;
  background: #fff;
}

.price-separator {
  font-size: 28rpx;
  color: #666;
}

.status-options {
  display: flex;
  gap: 16rpx;
}

.status-option {
  flex: 1;
  padding: 16rpx;
  text-align: center;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  background: #fafafa;
  transition: all 0.3s;
}

.status-option.selected {
  border-color: #ff4d4f;
  background: #fff0f0;
  color: #ff4d4f;
  font-weight: bold;
}

.filter-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 8rpx;
}

.filter-clear-btn {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.filter-apply-btn {
  flex: 1;
  height: 72rpx;
  background: #ff4d4f;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 商品卡片相关样式 */
.product-mgr-item {
  display: flex;
  align-items: center;
  background: #fff;
  margin: 5rpx 15rpx 0 15rpx; /* 调整间距为5像素(5rpx)，左右边距为15像素(15rpx) */
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx #eee;
  padding: 38rpx 18rpx 18rpx 18rpx; /* 顶部内边距增加20rpx（10像素） */
  position: relative;
}
.product-mgr-checkbox {
  margin-right: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  cursor: pointer;
  background-color: transparent;
  transition: all 0.2s ease;
}
.product-mgr-checkbox image {
  width: 48rpx;
  height: 48rpx;
}
.product-mgr-checkbox.selected {
  background-color: transparent;
}
.product-mgr-img {
  width: 86rpx;
  height: 86rpx;
  border-radius: 12rpx;
  margin-right: 18rpx;
  border: 2rpx solid #f0f0f0;
  background: #fafafa;
}
.product-mgr-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}
.product-mgr-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.product-mgr-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.product-mgr-status {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 24rpx;
  padding: 4rpx 18rpx;
  border-radius: 16rpx 0 0 16rpx;
  font-weight: bold;
  z-index: 2;
  border: 2rpx solid #e0e0e0;
  box-shadow: 0 2rpx 8rpx #eee;
  background: #fff;
}
.product-mgr-status-on {
  background: #fff;
  color: #1bc47d;
  border: 2rpx solid #1bc47d;
}
.product-mgr-status-off {
  background: #fff;
  color: #aaa;
  border: 2rpx solid #ddd;
}
.product-mgr-status-frozen {
  background: #fff;
  color: #ff4d4f;
  border: 2rpx solid #ff4d4f;
}
.product-mgr-spec {
  font-size: 24rpx;
  color: #888;
}
.product-mgr-price {
  font-size: 30rpx;
  color: #e60012;
  font-weight: bold;
  margin-top: 4rpx;
}
.product-mgr-edit-btn {
  position: absolute;
  right: 24rpx;
  top: auto;
  bottom: 18rpx;
  transform: none;
  font-size: 26rpx;
  color: #222;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  background: #fff;
  padding: 0 18rpx;
  height: 48rpx;
  line-height: 48rpx;
}
.user-mgr-footer {
  text-align: center;
  font-size: 28rpx;
  color: #888;
  background: #fff;
  padding: 24rpx 0 24rpx 0;
  margin-top: 24rpx;
}
.selected-count {
  color: #ff4d4f;
  font-size: 28rpx;
  margin-left: 8rpx;
}
.safe-area {
  height: 120rpx;
  background: transparent;
}