<!-- 合伙人端合伙人页面 -->
<view class="partner-partner page-content">
  <!-- 头部信息栏 -->
  <view class="profile-header">
    <view wx:if="{{isLogin}}" class="user-info-row">
      <image class="avatar-large" src="{{userInfo.avatar || userInfo.avatarUrl || '/images/profile.png'}}"></image>
      <view class="user-info-content user-info-flex">
        <view class="user-info-main-row user-info-align-center">
          <view class="user-name">{{userInfo.nickname || userInfo.nickName || '用户'}}</view>
          <view class="user-role-right">
            <view class="user-role-label">{{userInfo.partnerLevel || userInfo.role_name || ''}}
              <image class="switch-role-icon" src="/images/icons2/switch-role.png" mode="aspectFit" bindtap="onSwitchLogin" />
            </view>
          </view>
        </view>
        <view class="user-id user-id-bottom">ID号：{{userInfo.id || userInfo._id || ''}}</view>
      </view>
    </view>
    <view class="login-prompt" wx:else>
      <image class="avatar-large" src="/images/profile.png"></image>
      <view class="login-text">登录后查看合伙人信息</view>
      <view class="login-btn" bindtap="goToLogin">立即登录</view>
    </view>
  </view>

  <!-- 账户数据卡片，悬浮效果 -->
  <view class="account-data float-card" bindtap="goToWallet">
    <view class="data-item">
      <view class="value">{{fundAccount.account_balance}}</view>
      <view class="label">账户余额</view>
    </view>
    <view class="data-item">
      <view class="value">{{fundAccount.pending_commission}}</view>
      <view class="label">待结算销售分佣</view>
    </view>
    <view class="data-item">
      <view class="value">{{fundAccount.total_commission}}</view>
      <view class="label">累计销售分佣</view>
    </view>
    <view class="data-item">
      <view class="value">{{fundAccount.total_withdrawal}}</view>
      <view class="label">累计提现</view>
    </view>
  </view>

  <!-- 门店/推荐 -->
  <view class="store-recommend">
    <view class="store" bindtap="goToMyStores">
      <view class="label">我的门店</view>
      <view class="value">{{referralStats.store_count}}家</view>
    </view>
    <view class="recommend" bindtap="goToMyReferrals">
      <view class="label">我的推荐</view>
      <view class="value">{{referralStats.referral_count}}人</view>
    </view>
  </view>

  <!-- 顾客订单管理 -->
  <view class="order-manage">
    <view class="order-header">
      <view class="order-title">顾客订单</view>
      <view class="order-all-btn" bindtap="goToOrderManage" data-status="all">
        <text class="order-all-text">全部</text>
        <image class="order-all-icon" src="/images/icons2/向右.svg" mode="aspectFit" />
      </view>
    </view>
    <view class="order-list">
      <view class="order-item" bindtap="goToOrderManage" data-status="pending_payment">
        <view class="order-icon-wrapper">
          <image class="order-icon" src="/images/icons2/待付款.svg" />
          <view class="order-badge" wx:if="{{orderStats.pending_payment > 0}}">{{orderStats.pending_payment}}</view>
        </view>
        <view class="order-label">待付款</view>
      </view>
      <view class="order-item" bindtap="goToOrderManage" data-status="pending_pickup">
        <view class="order-icon-wrapper">
          <image class="order-icon" src="/images/icons2/待自提.svg" />
          <view class="order-badge" wx:if="{{orderStats.pending_pickup > 0}}">{{orderStats.pending_pickup}}</view>
        </view>
        <view class="order-label">待自提</view>
      </view>
      <view class="order-item" bindtap="goToOrderManage" data-status="shipped">
        <view class="order-icon-wrapper">
          <image class="order-icon" src="/images/icons2/待收货.svg" />
          <view class="order-badge" wx:if="{{orderStats.shipped > 0}}">{{orderStats.shipped}}</view>
        </view>
        <view class="order-label">待收货</view>
      </view>
      <view class="order-item" bindtap="goToOrderManage" data-status="completed">
        <view class="order-icon-wrapper">
          <image class="order-icon" src="/images/icons2/已完成.svg" />
          <view class="order-badge" wx:if="{{orderStats.completed > 0}}">{{orderStats.completed}}</view>
        </view>
        <view class="order-label">已完成</view>
      </view>
      <view class="order-item" bindtap="goToOrderManage" data-status="returns">
        <view class="order-icon-wrapper">
          <image class="order-icon" src="/images/icons/order-return.svg" />
          <view class="order-badge" wx:if="{{orderStats.returns > 0}}">{{orderStats.returns}}</view>
        </view>
        <view class="order-label">退换货</view>
      </view>
    </view>
  </view>

  <!-- 门店管理 -->
  <view class="store-manage">
    <view class="store-header">
      <view class="store-title">门店管理</view>
      <picker class="store-picker" mode="selector" range="{{storeList}}" range-key="name" bindchange="onStoreChange" wx:if="{{storeList.length > 0}}">
        <view class="store-selector">
          <text class="store-name">{{selectedStore.name}}（{{selectedStore.store_no}}）</text>
          <view class="dropdown-arrow">▼</view>
        </view>
      </picker>
      <view class="store-selector" wx:else>
        <text class="store-name">暂无门店数据</text>
      </view>
    </view>
    <view class="store-grid">
      <view class="store-grid-item" bindtap="goToStoreManage" data-type="partners">
        <image class="store-grid-icon" src="/images/icons2/门店合伙人.png" />
        <text class="store-grid-label">门店合伙人</text>
      </view>
      <view class="store-grid-item" bindtap="goToStoreManage" data-type="inventory">
        <image class="store-grid-icon" src="/images/icons2/门店库存.png" />
        <text class="store-grid-label">门店库存</text>
      </view>
      <view class="store-grid-item" bindtap="goToStoreManage" data-type="fund">
        <image class="store-grid-icon" src="/images/icons2/门店公积金.png" />
        <text class="store-grid-label">门店资金</text>
      </view>
      <view class="store-grid-item" bindtap="goToStoreManage" data-type="share">
        <image class="store-grid-icon" src="/images/icons2/分享门店.png" />
        <text class="store-grid-label">分享门店</text>
      </view>
      <view class="store-grid-item" bindtap="goToStoreManage" data-type="orders">
        <image class="store-grid-icon" src="/images/icons2/待发货.png" />
        <text class="store-grid-label">门店订单</text>
      </view>
      <view class="store-grid-item" bindtap="goToStoreManage" data-type="settings">
        <image class="store-grid-icon" src="/images/icons2/门店设置.png" />
        <text class="store-grid-label">门店设置</text>
      </view>
    </view>
  </view>

  <!-- 其他功能 -->
  <view class="other-func">
    <view class="func-item" bindtap="goToAbout">
      <image class="func-icon" src="/images/icons2/关于我们.png" />
      <text class="func-text">关于我们</text>
    </view>
    <view class="func-item" bindtap="goToFAQ">
      <image class="func-icon" src="/images/icons2/常见问题.png" />
      <text class="func-text">常见问题</text>
    </view>
    <view class="func-item" bindtap="goToOnlineService">
      <image class="func-icon" src="/images/icons2/在线客服.png" />
      <text class="func-text">在线客服</text>
    </view>
    <view class="func-item" bindtap="goToAccountSettings">
      <image class="func-icon" src="/images/icons2/账户设置.png" />
      <text class="func-text">账户设置</text>
    </view>
  </view>
</view>
<partner-tabbar selected="3" />