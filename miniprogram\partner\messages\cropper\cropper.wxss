.cropper-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f8f8f8;
}
.cropper-box {
  width: 90vw;
  height: 90vw;
  margin-top: 40rpx;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  position: relative;
}
.preview-image {
  position: absolute;
  left: 0; top: 0; width: 100%; height: 100%; z-index: 1;
}
.cropper-canvas {
  position: absolute;
  left: 0; top: 0; width: 100%; height: 100%; z-index: 2;
  background: transparent;
}
.cropper-btn {
  margin-top: 48rpx;
  width: 60vw;
  background: #5698c3;
  color: #fff;
  border-radius: 24rpx;
  font-size: 16px;
  font-weight: bold;
  padding: 16rpx 0;
}