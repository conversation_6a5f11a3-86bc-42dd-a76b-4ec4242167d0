const { storeApi, fundApi, uploadFile } = require('../../utils/api');
Page({
  data: {
    storeSearchValue: '',
    filteredStoreList: [],
    showStoreDropdown: false,
    selectedStore: null,
    selectedStoreIndex: null,
    fundActionBtns: [
      '股本金增减',
      '（不可用）',
      '提取公积金',
      '合伙人分红'
    ],
    tabIndex: 0,
    tabs: ['全部记录', '股本金记录', '公积金记录', '分红记录'],
    fundRecords: [],
    sortType: 'time',
    sortOrder: 'desc',
    filterVisible: false,
    showCapitalDrawer: false,
    capitalForm: {
      amount: '',
      reasonIndex: -1, // 初始无选项
      reasonList: [
        { label: '合伙人注资', value: 'ZZ' },
        { label: '支付货款', value: 'HK' },
        { label: '提取公积金', value: 'GJ' },
        { label: '分红', value: 'FH' },
        { label: '特别调整', value: 'TZ' },
        { label: '其他', value: 'QT' }
      ],
      useFund: false,
      desc: '',
      voucherImages: [] // 新增：凭证图片
    },
  },
  onLoad() {
    // TODO: 加载门店列表和资金记录
  },
  
  onShow() {
    // 处理裁剪后的凭证图片
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage.data.croppedImage && currentPage.data.imageType === 'voucher') {
      console.log('检测到裁剪后的凭证图片，只显示缩略图:', currentPage.data.croppedImage);
      
      // 只将裁剪后的图片路径添加到列表中以显示缩略图，不立即上传
      const images = this.data.capitalForm.voucherImages || [];
      if (images.length < 3) {
        images.push(currentPage.data.croppedImage); // 显示本地路径的缩略图
        this.setData({ 
          'capitalForm.voucherImages': images
        });
      }
      
      // 清除页面数据
      currentPage.setData({
        croppedImage: null,
        imageType: null
      });
    }
  },
  // 门店搜索输入
  onStoreSearchInput(e) {
    const val = e.detail.value.trim();
    this.setData({ storeSearchValue: val });
    if (val.length < 1) {
      this.setData({ filteredStoreList: [], showStoreDropdown: false });
      return;
    }
    storeApi.searchStores(val).then(res => {
      if (res.success && Array.isArray(res.data)) {
        const list = res.data.map(item => ({ ...item, code: item.code || item.store_no || item.storeNo || '' }));
        this.setData({ filteredStoreList: list, showStoreDropdown: true });
      } else {
        this.setData({ filteredStoreList: [], showStoreDropdown: true });
      }
    }).catch(() => {
      this.setData({ filteredStoreList: [], showStoreDropdown: true });
    });
  },
  // 选中门店
  selectStore(e) {
    const idx = e.currentTarget.dataset.index;
    const store = this.data.filteredStoreList[idx];
    this.setData({
      selectedStoreIndex: idx,
      selectedStore: store,
      storeSearchValue: store.name,
      showStoreDropdown: false
    });
    // 加载该门店资金记录
    if (store && store.store_no) {
      wx.showLoading({ title: '加载资金记录...' });
      fundApi.getFundRecords(store.store_no).then(res => {
        wx.hideLoading();
        if (res.success && Array.isArray(res.data)) {
          this.setData({ fundRecords: res.data });
        } else {
          this.setData({ fundRecords: [] });
        }
      }).catch(() => {
        wx.hideLoading();
        this.setData({ fundRecords: [] });
      });
    }
  },
  // 点击页面空白处关闭下拉
  onCloseDropdown() {
    this.setData({ showStoreDropdown: false });
  },
  // tab切换
  onTabChange(e) {
    this.setData({ tabIndex: e.currentTarget.dataset.index });
    // TODO: 切换tab加载数据
  },
  // 排序切换
  onSortChange(e) {
    const type = e.currentTarget.dataset.type;
    let order = this.data.sortOrder;
    if (this.data.sortType === type) {
      order = order === 'desc' ? 'asc' : 'desc';
    } else {
      order = 'desc';
    }
    this.setData({ sortType: type, sortOrder: order });
    // TODO: 重新加载数据
  },
  // 筛选器开关
  onFilterToggle() {
    this.setData({ filterVisible: !this.data.filterVisible });
  },
  // 打开股本金增减弹窗
  openCapitalDrawer() {
    if (!this.data.selectedStore) {
      wx.showToast({ title: '请先选择操作门店', icon: 'none' });
      return;
    }
    // 重置表单数据
    this.setData({ 
      showCapitalDrawer: true,
      'capitalForm.amount': '',
      'capitalForm.reasonIndex': -1,
      'capitalForm.desc': '',
      'capitalForm.useFund': false,
      'capitalForm.voucherImages': []
    });
  },
  // 关闭弹窗
  closeCapitalDrawer() {
    this.setData({ showCapitalDrawer: false });
  },
  // 金额输入
  onCapitalAmountInput(e) {
    this.setData({ 'capitalForm.amount': e.detail.value });
  },
  // 事由选择
  onCapitalReasonChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({ 'capitalForm.reasonIndex': index });
  },
  // 公积金转股本开关
  onUseFundChange(e) {
    this.setData({ 'capitalForm.useFund': e.detail.value });
  },
  // 事由描述输入
  onCapitalDescInput(e) {
    this.setData({ 'capitalForm.desc': e.detail.value });
  },
  // 选择凭证图片
  chooseVoucherImage() {
    const images = this.data.capitalForm.voucherImages || [];
    const remain = 3 - images.length;
    if (remain <= 0) {
      wx.showToast({ title: '最多上传3张图片', icon: 'none' });
      return;
    }
    
    console.log('开始选择凭证图片');
    // 先尝试使用chooseMedia API
    try {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        success: res => {
          console.log('选择凭证图片成功:', res);
          const file = res.tempFiles[0];
          if (file.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
            wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
            return;
          }
          // 资金凭证图片不需要裁剪，直接添加到图片列表
          this.addVoucherImage(file.tempFilePath);
        },
        fail: err => {
          console.error('chooseMedia 失败:', err);
          // 检查是否是用户取消操作
          if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
            console.log('用户取消选择图片');
            return; // 用户取消，直接返回
          }
          // 其他错误情况才使用备选方案
          this.chooseVoucherImageFallback();
        }
      });
    } catch (error) {
      console.error('chooseMedia 异常:', error);
      // 如果chooseMedia出现异常，尝试使用chooseImage作为备选方案
      this.chooseVoucherImageFallback();
    }
  },

  // 使用chooseImage作为备选方案
  chooseVoucherImageFallback() {
    console.log('使用chooseImage作为备选方案');
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: res => {
        console.log('chooseImage成功:', res);
        const tempFilePath = res.tempFilePaths[0];
        const tempFile = res.tempFiles[0];
        if (tempFile && tempFile.size > 1.2 * 1024 * 1024) { // 限制到1.2MB
          wx.showToast({ title: '图片不能大于1.2MB', icon: 'none' });
          return;
        }
        // 资金凭证图片不需要裁剪，直接添加到图片列表
        this.addVoucherImage(tempFilePath);
      },
      fail: err => {
        console.error('chooseImage 失败:', err);
        // 检查是否是用户取消操作
        if (err.errMsg && (err.errMsg.indexOf('cancel') > -1 || err.errMsg.indexOf('取消') > -1)) {
          console.log('用户取消选择图片');
          return; // 用户取消，直接返回
        }
        // 其他错误情况才显示提示
        wx.showToast({
          title: '选择图片失败，请检查相关权限',
          icon: 'none'
        });
      }
    });
  },
  // 预览图片
  previewVoucherImage(e) {
    const idx = e.currentTarget.dataset.index;
    wx.previewImage({
      current: this.data.capitalForm.voucherImages[idx],
      urls: this.data.capitalForm.voucherImages
    });
  },
  // 删除图片
  deleteVoucherImage(e) {
    const idx = e.currentTarget.dataset.index;
    const images = this.data.capitalForm.voucherImages.slice();
    images.splice(idx, 1);
    this.setData({ 'capitalForm.voucherImages': images });
  },

  // 添加凭证图片（不需要裁剪）
  addVoucherImage(tempFilePath) {
    console.log('添加凭证图片:', tempFilePath);
    const images = this.data.capitalForm.voucherImages.slice();
    images.push(tempFilePath);
    this.setData({ 'capitalForm.voucherImages': images });

    wx.showToast({
      title: '图片添加成功',
      icon: 'success',
      duration: 1000
    });
  },
  // 确认提交
  async onCapitalConfirm() {
    // 校验必填
    const { amount, reasonIndex, reasonList, desc, voucherImages } = this.data.capitalForm;
    
    if (!amount || amount.trim() === '') {
      wx.showToast({ title: '请填写金额', icon: 'none' });
      return;
    }
    
    if (reasonIndex === -1 || reasonIndex < 0 || reasonIndex >= reasonList.length) {
      wx.showToast({ title: '请选择资金事由', icon: 'none' });
      return;
    }
    
    const selectedReason = reasonList[reasonIndex];
    if (!selectedReason || !selectedReason.value) {
      wx.showToast({ title: '资金事由选择无效', icon: 'none' });
      return;
    }
    
    wx.showLoading({ title: '提交中...' });
    
    // 上传图片（允许为空）
    let uploadedImages = [];
    try {
      if (voucherImages && voucherImages.length > 0) {
        for (let img of voucherImages) {
          if (img.startsWith('cloud://')) {
            uploadedImages.push(img);
          } else {
            const fileID = await uploadFile(img, 'fund_vouchers');
            uploadedImages.push(fileID);
          }
        }
      }
    } catch (e) {
      console.error('图片上传失败:', e);
      wx.hideLoading();
      wx.showToast({ title: '图片上传失败', icon: 'none' });
      return;
    }
    
    // 组装数据
    const data = {
      store_no: this.data.selectedStore.store_no,
      type: selectedReason.value,
      amount: parseFloat(amount),
      description: desc || '',
      voucher_images: uploadedImages
    };
    
    console.log('提交资金记录数据:', data);
    
    // 提交到后端
    fundApi.createFundRecord(data).then(res => {
      wx.hideLoading();
      if (res.success) {
        wx.showToast({ title: '提交成功', icon: 'success' });
        this.closeCapitalDrawer();
        // 刷新资金记录
        this.selectStore({ currentTarget: { dataset: { index: this.data.selectedStoreIndex } } });
      } else {
        wx.showToast({ title: res.message || '提交失败', icon: 'none' });
      }
    }).catch((err) => {
      console.error('提交资金记录失败:', err);
      wx.hideLoading();
      wx.showToast({ title: '提交失败', icon: 'none' });
    });
  }
});