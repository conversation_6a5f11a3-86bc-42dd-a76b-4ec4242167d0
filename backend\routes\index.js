/**
 * 路由索引
 */
const express = require('express');
const db = require('../config/db');
const productRoutes = require('./products');
const userRoutes = require('./users');

const cartRoutes = require('./cart');
const messageRoutes = require('./messages');
const messageRoutesSingular = require('./message');
const systemRoutes = require('./system');
const uploadRoutes = require('./upload');
const groupRoutes = require('./group');
const adminRoutes = require('./admin');
const companyRoutes = require('./company');
const shareRoutes = require('./share');
const pointsRoutes = require('./points');
const vipRoutes = require('./vip');
const quickMenusRoutes = require('./quickMenus');
const regionRoutes = require('./region');
const partnerRoutes = require('./partner');
const favoriteRoutes = require('./favorites');
const faqRoutes = require('./faq');
const fundRoutes = require('./fund');
const storeInventoryRoutes = require('./storeInventory');
const storeController = require('../controllers/storeController');
// 引入拆分后的订单路由
const customerOrderRoutes = require('./customer/customerOrderRoutes');
const partnerOrderRoutes = require('./partner/partnerOrderRoutes');
const adminOrderRoutes = require('./admin/adminOrderRoutes');
const storeFundController = require('../controllers/storeFundController');
const bannerController = require('../controllers/bannerController');
const { checkAuth } = require('../middleware/auth');

const router = express.Router();

router.use('/products', productRoutes);
router.use('/users', userRoutes);

router.use('/cart', cartRoutes);
router.use('/messages', messageRoutes);
router.use('/message', messageRoutesSingular);
router.use('/system', systemRoutes);
router.use('/upload', uploadRoutes);
router.use('/group', groupRoutes);
router.use('/admin', adminRoutes);
router.use('/company', companyRoutes);
router.use('/share', shareRoutes);
router.use('/points', pointsRoutes);
router.use('/vip', vipRoutes);
router.use('/quick-menus', quickMenusRoutes);
router.use('/region', regionRoutes);
router.use('/partner', partnerRoutes);
router.use('/favorites', favoriteRoutes);
router.use('/faq', faqRoutes);
router.use('/fund', fundRoutes);
router.use('/store/inventory', storeInventoryRoutes);

// 轮播图前端API路由（无需认证）
router.get('/banners', bannerController.getActiveBanners);

// 订单相关路由 - 按端拆分
router.use('/customer/orders', checkAuth, customerOrderRoutes);

// 临时测试支付路由（无需认证）- 仅用于调试
router.use('/customer/orders-test', customerOrderRoutes);
router.use('/partner/orders', checkAuth, partnerOrderRoutes);
router.use('/admin/orders', checkAuth, adminOrderRoutes);

// 订单控制器引用已移除，现在使用专用的订单路由



// 门店相关路由
router.get('/store/by-no', checkAuth, storeController.getStoreByNo);

// 门店资金相关路由
router.get('/store/funds/:storeNo', checkAuth, storeFundController.getStoreFunds);
router.post('/store/funds/record', checkAuth, storeFundController.createFundRecord);
router.get('/store/funds/records/:storeNo', checkAuth, storeFundController.getFundRecords);

module.exports = router;
