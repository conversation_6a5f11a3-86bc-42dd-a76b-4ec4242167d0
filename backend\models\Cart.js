/**
 * 购物车模型
 */
const db = require('../config/db');

class Cart {
  static async findAll(userId) {
    try {
      console.log('【Cart模型】查询购物车，userId:', userId);
      
      const result = await db.query(`
        SELECT c.*, p.name, p.price, p.originalPrice, p.images, p.shopName
        FROM cart c
        LEFT JOIN products p ON c.productId COLLATE utf8mb4_0900_ai_ci = p.id COLLATE utf8mb4_0900_ai_ci
        WHERE c.userId = ?
        ORDER BY c.createTime DESC
      `, [userId]);
      
      console.log('【Cart模型】查询结果:', result);
      return result;
    } catch (error) {
      console.error('【Cart模型】查询购物车失败:', error);
      throw error;
    }
  }

  static async findById(id) {
    const result = await db.query('SELECT * FROM cart WHERE id = ?', [id]);
    return result.length > 0 ? result[0] : null;
  }

  static async findByIds(userId, ids) {
    try {
      console.log('【Cart模型】根据ID查询购物车商品，userId:', userId, 'ids:', ids);
      
      if (!ids || ids.length === 0) {
        return [];
      }
      
      // 构建IN查询的占位符
      const placeholders = ids.map(() => '?').join(',');
      
      const result = await db.query(`
        SELECT c.*, p.name, p.price, p.originalPrice, p.images, p.shopName
        FROM cart c
        LEFT JOIN products p ON c.productId COLLATE utf8mb4_0900_ai_ci = p.id COLLATE utf8mb4_0900_ai_ci
        WHERE c.userId = ? AND c.id IN (${placeholders})
        ORDER BY c.createTime DESC
      `, [userId, ...ids]);
      
      console.log('【Cart模型】根据ID查询结果:', result);
      return result;
    } catch (error) {
      console.error('【Cart模型】根据ID查询购物车商品失败:', error);
      throw error;
    }
  }

  static async findByUserAndProduct(userId, productId) {
    const result = await db.query('SELECT * FROM cart WHERE userId = ? AND productId = ?', [userId, productId]);
    return result.length > 0 ? result[0] : null;
  }

  static async create(cartData) {
    cartData.createTime = cartData.createTime || Date.now();
    cartData.updateTime = cartData.updateTime || Date.now();

    // 构建插入SQL
    const sql = `
      INSERT INTO cart (id, userId, productId, name, price, image, quantity, selected, createTime, updateTime)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const params = [
      cartData.id,
      cartData.userId,
      cartData.productId,
      cartData.name,
      cartData.price,
      cartData.image,
      cartData.quantity || 1,
      cartData.selected !== undefined ? cartData.selected : true,
      cartData.createTime,
      cartData.updateTime
    ];
    await db.query(sql, params);
    return cartData;
  }

  static async update(id, cartData) {
    try {
      cartData.updateTime = Date.now();
      
      // 先检查购物车项是否存在
      const existingItem = await this.findById(id);
      if (!existingItem) {
        throw new Error('购物车项不存在');
      }
      
      // 构建更新SQL
      const fields = [];
      const params = [];
      if (cartData.quantity !== undefined) { 
        // 验证数量必须大于0
        if (cartData.quantity < 1) {
          throw new Error('商品数量必须大于0');
        }
        fields.push('quantity = ?'); 
        params.push(cartData.quantity); 
      }
      if (cartData.selected !== undefined) { 
        fields.push('selected = ?'); 
        params.push(cartData.selected); 
      }
      fields.push('updateTime = ?'); 
      params.push(cartData.updateTime);
      
      const sql = `UPDATE cart SET ${fields.join(', ')} WHERE id = ?`;
      params.push(id);
      
      console.log('【Cart模型】更新购物车项 - SQL:', sql, '参数:', params);
      
      const result = await db.query(sql, params);
      console.log('【Cart模型】更新结果:', result);
      
      // 检查是否有行被更新
      if (result.affectedRows === 0) {
        throw new Error('购物车项更新失败，可能已被删除');
      }
      
      // 返回更新后的数据
      const updatedItem = await this.findById(id);
      if (!updatedItem) {
        throw new Error('更新后无法找到购物车项');
      }
      
      return updatedItem;
    } catch (error) {
      console.error('【Cart模型】更新购物车项失败:', error);
      throw error;
    }
  }

  static async remove(id) {
    const sql = 'DELETE FROM cart WHERE id = ?';
    await db.query(sql, [id]);
    return { id };
  }

  static async removeByUser(userId) {
    const sql = 'DELETE FROM cart WHERE userId = ?';
    await db.query(sql, [userId]);
    return { userId };
  }

  static async count(userId) {
    const result = await db.query('SELECT COUNT(*) as total FROM cart WHERE userId = ?', [userId]);
    return result[0].total;
  }
}

module.exports = Cart;
